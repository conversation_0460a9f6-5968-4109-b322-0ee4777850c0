package com.allcore.isc.feign;

import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.tool.api.R;
import com.allcore.isc.vo.IscUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * （服务feign）
 * <AUTHOR>
 * @date 2022/09/08 13:21
 */
@FeignClient(
	value = AppConstant.APPLICATION_ISC_NAME,
	fallback = IIscClientFallback.class
)
public interface IIscClient {

	String API_PREFIX = "/client";

	String DEPT_NATURE_ID = API_PREFIX + "/dept-nature-id";

	String VALIDATE_BY_APP = API_PREFIX + "/validate-by-app";

	String USER_LOGIN_AUTH_ISC = API_PREFIX + "/userLoginAuthIsc";

	String VALIDATE_BY_APP_IGW = API_PREFIX + "/validate-by-app-igw";

	/**
	 * 获取isc的NatureIdZh
	 *
	 * @param id 主键
	 * @return Dept
	 */
	@GetMapping(DEPT_NATURE_ID)
	R<String> getNatureIdZh(@RequestParam("id") String id);

	/**
	 * （isc feign校验ticket方式）
	 * <AUTHOR>
	 * @date 2022/09/20 16:59
	 * @param ticket
	 * @return com.allcore.core.tool.api.R<com.allcore.platform.vo.IscUserVO>
	 */
	@GetMapping(VALIDATE_BY_APP)
	R<IscUserVO> validateByApp(@RequestParam("ticket") String ticket);

	/**
	 * 登录isc返回系统用户数据
	 * @param loginName
	 * @param passWord
	 * @return
	 */
	@GetMapping(USER_LOGIN_AUTH_ISC)
	R<String> userLoginAuthIsc(@RequestParam("loginName") String loginName,@RequestParam("passWord") String passWord);

	/**
	 * （i国网 feign校验ticket方式）
	 * <AUTHOR>
	 * @date 2022/09/20 17:00
	 * @param ticket
	 * @return com.allcore.core.tool.api.R<com.allcore.platform.vo.IscUserVO>
	 */
	@GetMapping(VALIDATE_BY_APP_IGW)
	R<IscUserVO> validateByAppIgw(@RequestParam("ticket") String ticket);


}
