package com.allcore.netty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 开柜返回实体类
 *
 * <AUTHOR>
 * @date 2022-11-22 10:21:29
 */
@Data
public class OpenCabinetVO {

	/**
	 * 开柜层级返回实体类
	 */
	@ApiModelProperty(value = "开柜层级返回实体类")
	private List<EquipmentCabinetFloorVO> equipmentCabinetFloorVOList;

	/**
	 * 设备情况
	 */
	@ApiModelProperty(value = "未归还设备情况")
	private List<DeviceChildVO> deviceStatusVOS;

}
