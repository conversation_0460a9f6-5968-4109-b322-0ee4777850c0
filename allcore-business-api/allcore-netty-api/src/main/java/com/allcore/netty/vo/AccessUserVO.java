package com.allcore.netty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2022/11/30 0030 14:49
 */
@Data
public class AccessUserVO {

	@ApiModelProperty("序号")
	private Integer serialNumber;

	@ApiModelProperty("省公司名称")
	private String firstDeptName;

	@ApiModelProperty("分公司名称")
	private String secondDeptName;

	@ApiModelProperty("所属部门")
	private String thirdDeptName;

	@ApiModelProperty("工号")
	private String postId;

	@ApiModelProperty("姓名")
	private String realName;

	@ApiModelProperty("人员GUID")
	private String userGUID;
}
