package com.allcore.netty.feign;

import com.allcore.core.tool.api.R;
import com.allcore.netty.dto.BatteryDTO;
import com.allcore.netty.vo.BatteryVO;
import org.springframework.stereotype.Component;

/**
 * 电池信息 Feign接口类
 *
 * <AUTHOR>
 */
@Component
public class IBatteryClientFallBack implements IBatteryClient{

	@Override
	public R<BatteryVO> updateBySn(BatteryDTO batteryDTO) {
		return R.fail("获取数据失败");
	}
}
