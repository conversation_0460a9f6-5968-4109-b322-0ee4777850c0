package com.allcore.netty.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务操作日志表
 *
 * <AUTHOR>
 * @date 2022-12-26 10:21:29
 */
@Data
public class LogEntityDTO {

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTime;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTime;

	/**
	 * 关键词检索
	 */
	@ApiModelProperty(value = "关键词检索")
	private String text;


	@ApiModelProperty(value = "导出勾选，逗号拼接")
	private String guids;

}
