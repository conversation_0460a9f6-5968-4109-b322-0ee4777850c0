package com.allcore.netty.enums;

/**
 * 设备领取类型
 */
public enum ReceiveTypeEnum {

    RECEIVETYP_ORDER("1","工单领取"),
    RECEIVETYP_ADMIN("2","后台领取");

    private String type;

    private String name;

    ReceiveTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameById(String type){
        ReceiveTypeEnum[] errorNames = ReceiveTypeEnum.values();
        for(ReceiveTypeEnum errorName:errorNames){
            if(errorName.getType().equals(type)){
                return errorName.getName();
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
