package com.allcore.netty.enums;

/**
 * 电池充电状态
 * <AUTHOR>
 */
public enum BatteryChargeStatusEnum {
    STATE_NO_CONNECT(0, "未接上电池"),
    STATE_CONNECT_NOT_START(1, "接上电池未开始充电"),
    STATE_0_1_CHARGE(2, "正在0.1C充电"),
    STATE_0_3_CHARGE(3, "正在0.3C充电"),
    STATE_0_6_CHARGE(4, "正在0.6C充电"),
    STATE_1_0_CHARGE(5, "正在1.0C充电"),
    STATE_CHARGE_FINISH(6, "充电已完成"),
    STATE_DISCHARGING(7, "正在放电中"),
    STATE_DISCHARGE_FINISH(8, "放电已完成");
    private Integer id;

    private String name;

    BatteryChargeStatusEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameById(Integer id) {
        BatteryChargeStatusEnum[] errorNames = BatteryChargeStatusEnum.values();
        for (BatteryChargeStatusEnum errorName : errorNames) {
            if (errorName.getId().equals(id)) {
                return errorName.getName();
            }
        }
        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
