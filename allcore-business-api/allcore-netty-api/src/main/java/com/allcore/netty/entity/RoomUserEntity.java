package com.allcore.netty.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 访问人-人员库房关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-30 13:34:33
 */
@Data
@TableName("netty_room_user")
public class RoomUserEntity extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * guid
	 */
	private String roomUserGuid;
	/**
	 * 库房GUID
	 */
	private String storeRoomGuid;
	/**
	 * 人员GUID
	 */
	private String userGuid;

}
