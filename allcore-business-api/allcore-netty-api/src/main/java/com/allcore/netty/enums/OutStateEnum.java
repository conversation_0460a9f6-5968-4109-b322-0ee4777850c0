package com.allcore.netty.enums;

/**
 * 出入库状态
 */
public enum OutStateEnum {

    OUT_STATE_NORMAL(0,"正常"),
    OUT_STATE_REPAIR(1,"维修"),
    OUT_STATE_MAINTAIN(3,"保养"),
    OUT_STATE_REMOVE(4,"报废");

    private Integer id;

    private String name;

    OutStateEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameById(Integer id){
        OutStateEnum[] errorNames = OutStateEnum.values();
        for(OutStateEnum errorName:errorNames){
            if(errorName.getId().equals(id)){
                return errorName.getName();
            }
        }
        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
