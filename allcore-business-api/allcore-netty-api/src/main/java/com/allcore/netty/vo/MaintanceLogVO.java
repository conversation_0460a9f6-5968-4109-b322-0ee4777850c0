package com.allcore.netty.vo;

import com.allcore.netty.entity.MaintanceLogEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维修记录表
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "维修记录表", description = "维修记录表")
public class MaintanceLogVO extends MaintanceLogEntity {

	@ApiModelProperty("设备类型")
	private String deviceTypeName;

	@ApiModelProperty("设备编号")
	private String deviceCode;

	@ApiModelProperty("设备型号")
	private String deviceModleName;

	@ApiModelProperty("厂家")
	private String factoryName;

	@ApiModelProperty("维修状态")
	private String statusName;


}
