package com.allcore.netty.enums;

/**
 * 电池状态
 */
public enum BatteryStatusEnum {

    UAVSTATUS_WAIT_USE(0,"待领用"),//无人机分配电池后所处状态
    UAVSTATUS_IDLE(1,"空闲"),
    UAVSTATUS_LOSE(2,"报丢"),
    UAVSTATUS_DAMAGE(3,"报损"),
    UAVSTATUS_REDUCE(4,"报废"),
    UAVSTATUS_USE(5,"使用中"),
    UAVSTATUS_REPAIR(6,"维修中"),
    UAVSTATUS_MINTAIN(7,"保养中");

    private Integer id;

    private String name;

    BatteryStatusEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static String getNameById(Integer id){
        BatteryStatusEnum[] errorNames = BatteryStatusEnum.values();
        for(BatteryStatusEnum errorName:errorNames){
            if(errorName.getId().equals(id)){
                return errorName.getName();
            }
        }
        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
