package com.allcore.netty.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum WsOperateType {

	ADD("add", "新增"),
	UPDATE("update", "修改"),
	DELETE("delete", "删除"),
	;

	WsOperateType(String type, String name) {
		this.type = type;
		this.name = name;
	}

	private String type;

	private String name;

	public String getType() {
		return type;
	}

	public String getName() {
		return name;
	}
}
