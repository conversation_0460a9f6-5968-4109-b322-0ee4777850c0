package com.allcore.platform.param;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class params {
    /**
    * 页码
    */
    private Integer page;
    /**
    * 每页显示数据量
    */
    private Integer perPage;
    /**
    * 排序方式
    */
    private String orderBy;
    /**
    * 查询的的字段
    */
     private String fields;

     private Integer pageIndex;
     private Integer pageSize;
     private String sorter;


    /**
    * 过滤条件集合
    */
    private List<filters> filters;

    public params(Integer page, Integer perPage, String orderBy, String fields, List<filters> filters) {
        this.page = page;
        this.perPage = perPage;
        this.orderBy = orderBy;
        this.fields = fields;
        this.filters = filters;
    }

    public params(Integer perPage, String orderBy) {
        this.perPage = perPage;
        this.orderBy = orderBy;
    }

    public params(Integer page, Integer perPage) {
        this.page = page;
        this.perPage = perPage;
    }

    public params(Integer page, Integer perPage, String orderBy, List<filters> filters) {
        this.page = page;
        this.perPage = perPage;
        this.orderBy = orderBy;
        this.filters = filters;
    }

    public params(Integer page, Integer perPage, List<filters> filters) {
        this.page = page;
        this.perPage = perPage;
        this.filters = filters;
    }


    public params() {
    }

    @Override
    public String toString() {
        return "params{" +
                "page=" + page +
                ", perPage=" + perPage +
                ", orderBy='" + orderBy + '\'' +
                ", filters=" + filters +
                '}';
    }
}
