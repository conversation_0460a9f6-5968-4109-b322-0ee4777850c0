package com.allcore.platform.feign;


import com.allcore.platform.vo.DroneLedgerVo;
import com.allcore.core.tool.api.R;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2022/10/13 13:11
 */
@Component
public class IDroneLedgerClientFallBack implements IDroneLedgerClient {

	@Override
	public R<List<DroneLedgerVo>> selectInDeptCode(String deptCode) {

		return R.fail("获取数据失败");
	}


}
