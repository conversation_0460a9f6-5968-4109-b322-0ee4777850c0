package com.allcore.platform.cqbean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中台巡视记录实体类---对应要求中的巡视轨迹
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatrolRecordVo extends BaseEntity {

    //唯一标识ID 0
    private String mRID;
    //电站电压等级 0
    private String stationVoltage;
    //变电站名称 0
    private String substationName;
    //所属大馈线名称 0
    private String largeFeederName;
    //巡检类型 0
    private String patrolType;
    //巡视班组名称 0
    private String patrolTeamName;
    //巡检记录内容 0
    private String inspectionContent;
    //巡视状态 0
    private String patrolStaff;
    //巡视结果 0
    private String patrolResult;
    //附件数量 0
    private String numberOfAppendages;
    //计划到期时间 0
    private String scheduledTourExpiryTime;
    //实际开始时间 0
    private String tourStartTime;
    //实际结束时间 0
    private String endOfTourTime;
    //天气 0
    private String weather;
    //气温 0
    private String temperature;
    //巡视范围 0
    private String rangeOfInspection;
    //巡视是否完成 0
    private String whetherCompleted;
    //巡视未完成原因 0
    private String tourUnfinishedReason;
    //专业类型 1
    private String professionalClass;
    //是否归档 0
    private String whetherOrNotToFile;
    //是否APP等级 0
    private String mobileJob;
    //备注 0
    private String remarks;
    //移动作业轨迹 0
    private String mobileWorkTrack;
    //附件资料 0
    private String appendages;
    //巡视计划ID 0
    private String patrolPlanId;

}
