package com.allcore.platform.cqbean;

import lombok.Data;

import java.util.Date;

/**
 * 备品备件入库实体类
 */
@Data
public class PaSpareStockConverter extends BaseEntity {

    // 分类
    private String classification;
    // 创建人
    private String creator;
    // 创建时间
    private Date ctime;
    // 部门
    private String department;
    // 设备编码
    private String equipCode;
    // 设备名称
    private String equipName;
    // 设备类型
    private String equipType;
    // 主键
    private String id;
    // 机构路由
    private String institutionRoute;
    // 是否有效
    private String isValid;
    // 最后修改时间
    private Date lastModifiedTime;
    // 最后修改人
    private String lastModifier;
    // 运维单位
    private String maintOrg;
    // 出厂编号
    private String manufactureNum;
    // 设备厂家
    private String manufacturer;
    // 设备型号
    private String model;
    // 采购编号
    private String purchaseCode;
    // 二维码编号
    private String qrCode;
    // 换流站
    private String station;
    // 入库单号
    private String stockCode;
    // 入库单位
    private String stockOrg;
    // 存放地点
    private String storageLocation;
    // 入库员
    private String storePerson;
    // 入库数量
    private String storeQuantity;
    // 入库状态
    private String storeStatus;
    // 入库时间
    private Date storeTime;

}
