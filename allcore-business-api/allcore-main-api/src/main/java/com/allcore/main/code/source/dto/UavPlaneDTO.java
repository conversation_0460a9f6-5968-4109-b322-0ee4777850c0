package com.allcore.main.code.source.dto;

import com.allcore.main.code.source.entity.UavPlane;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UavPlaneDTO extends UavPlane {
	private static final long serialVersionUID = 1L;
	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty(value = "类别(数据字典)")
	private String uavType;

}
