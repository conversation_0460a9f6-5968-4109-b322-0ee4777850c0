package com.allcore.main.code.inspection.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class InspectionTaskReportPicDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "巡检任务id")
    private String inspectionTaskId;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
    private String deviceType;

    @ApiModelProperty(value = "图片GUID")
    private String picGuid;



}
