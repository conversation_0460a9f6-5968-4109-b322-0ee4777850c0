package com.allcore.main.code.inspection.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.allcore.main.code.inspection.vo.PatrolDataImageVO;
import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * </p>
 *
 * @author: taoye Date: 2023/9/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImgPackSaveDTO implements Serializable {

    private static final long serialVersionUID = 428839336170660037L;

    private Long id;

    @ApiModelProperty("打包状态 0 打包中 1打包完成")
    private Integer status;

    @ApiModelProperty("文件guid")
    private Long fileGuid;

    @ApiModelProperty("巡检或消缺 (patrol | eliminateDefects)")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "设备名")
    private String deviceName;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty("上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date uploadTime;

    @ApiModelProperty(value = "图片guid组合")
    private String fileGuids;

    private List<Map<String, Object>> data;
    private List<PatrolDataImageVO> dataSource;
}
