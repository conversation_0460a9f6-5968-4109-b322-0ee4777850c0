package com.allcore.main.code.system.entity;

import com.allcore.common.base.ZxhcEntity;
import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@TableName("main_three_data")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ThreeData对象", description = "ThreeData对象")
public class ThreeData extends ZxhcEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 桶名称
     */
    @ApiModelProperty(value = "桶名称")
    private String bucketName;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 区间开始设备id
     */
    @ApiModelProperty(value = "区间开始设备id")
    private String startId;
    /**
     * 区间结束设备id
     */
    @ApiModelProperty(value = "区间结束设备id")
    private String endId;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    /**
     * 存储路径或文件guid
     */
    @ApiModelProperty(value = "存储路径或文件guid")
    private String filePath;
    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Double fileSize;
    /**
     * 上传状态 (0:上传中 1:上传成功 2:上传失败)
     */
    @ApiModelProperty(value = "上传状态 (0:上传中 1:上传成功 2:上传失败)")
    private Integer uploadStatus;
    /**
     * 数据厂商
     */
    @ApiModelProperty(value = "数据厂商")
    private String dataFactory;
    /**
     * 采集时间（具体到天）
     */
    @ApiModelProperty(value = "采集时间（具体到天）")
    private String gatherTime;
    /**
     * 作业年份
     */
    @ApiModelProperty(value = "作业年份")
    private String workTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否显示(0:显示 1:不显示)
     */
    @ApiModelProperty(value = "是否显示(0:显示 1:不显示)")
    private Integer isShow;



}
