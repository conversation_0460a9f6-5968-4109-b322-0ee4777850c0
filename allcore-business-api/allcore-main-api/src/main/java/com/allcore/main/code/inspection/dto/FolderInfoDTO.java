package com.allcore.main.code.inspection.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件夹信息
 *
 * <AUTHOR>
 * @date 2023/11/06 10:15
 **/
@Data
public class FolderInfoDTO {

    @ApiModelProperty(value = "设备类型【device_type字典】")
    private String deviceType;

    @ApiModelProperty(value = "巡检任务id")
    private String inspectionTaskId;

    @ApiModelProperty(value = "巡检任务编号")
    private String inspectionTaskNo;

    @ApiModelProperty(value = "文件夹名称")
    private String folderName;

    @ApiModelProperty(value = "需要上传的数据")
    private List<InspectionPictureSaveDTO> folderUploadList;

    @ApiModelProperty(value = "下一级文件夹信息")
    private List<FolderInfoDTO> nextFolderInfo;

}
