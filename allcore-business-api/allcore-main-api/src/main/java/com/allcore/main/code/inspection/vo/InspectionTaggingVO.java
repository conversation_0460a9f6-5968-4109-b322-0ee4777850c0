package com.allcore.main.code.inspection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 01 7月 2025
 */
@Data
@ApiModel("缺陷信息")
public class InspectionTaggingVO implements Serializable {

    private static final long serialVersionUID = 5571148461078426902L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "缺陷描述")
    private String defectDescription;

    @ApiModelProperty(value = "缺陷描述中文")
    private String defectDescriptionZh;

    @ApiModelProperty(value = "缺陷等级")
    private String defectLevel;

    @ApiModelProperty(value = "缺陷等级中文")
    private String defectLevelZh;

    @ApiModelProperty(value = "可见光图片")
    private String visibleLightFilePath;

    @ApiModelProperty(value = "红外图片")
    private String infraredFilePath;

    private String pvComponentId;

    private String longitude;

    private String latitude;

    private String coordinates;

    private String groupId;

    /**
     * x最小值
     */
    private Double xmin;
    /**
     * y最小值
     */
    private Double ymin;
    /**
     * x最大值
     */
    private Double xmax;
    /**
     * y最大值
     */
    private Double ymax;

    /**
     * x最小值
     */
    private Double xbigmin;
    /**
     * y最小值
     */
    private Double ybigmin;
    /**
     * x最大值
     */
    private Double xbigmax;
    /**
     * y最大值
     */
    private Double ybigmax;
}
