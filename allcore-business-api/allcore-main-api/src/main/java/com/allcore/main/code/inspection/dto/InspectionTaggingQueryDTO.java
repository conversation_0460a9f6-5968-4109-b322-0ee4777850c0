package com.allcore.main.code.inspection.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>查询缺陷列表dto</p>
 *
 * @author: sunkun
 * Date: 01 7月 2025
 */
@Data
public class InspectionTaggingQueryDTO implements Serializable {

    private static final long serialVersionUID = 3974927934071371689L;

    @NotBlank(message = "设备id不能为空")
    private String deviceId;

    @NotBlank(message = "设备类型不能为空")
    private String deviceType;
}
