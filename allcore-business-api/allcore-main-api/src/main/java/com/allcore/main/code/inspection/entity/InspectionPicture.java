package com.allcore.main.code.inspection.entity;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_inspection_picture")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionPicture对象", description = "InspectionPicture对象")
public class InspectionPicture extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 巡检任务guid
     */
    @ApiModelProperty(value = "巡检任务guid")
    private String inspectionTaskId;
    /**
     * 缺陷识别任务guid【defect_recognition_task_info】
     */
    @ApiModelProperty(value = "缺陷识别任务guid【defect_recognition_task_info】")
    private String recognitionTaskId;
    /**
     * 可见光、红外一组的标识
     */
    @ApiModelProperty(value = "可见光、红外一组的标识")
    private String groupId;
    /**
     * 图片guid
     */
    @ApiModelProperty(value = "图片guid")
    private String fileGuid;

    /**
     * 缩略图id
     */
    @ApiModelProperty(value = "缩略图id")
    private String thumbnailFileGuid;
    /**
     * 设备guid
     */
    @ApiModelProperty(value = "设备guid")
    private String deviceId;

    @ApiModelProperty(value = "是否关联")
    private String bindFlag;
    /**
     * 设备类型【device_type字典】
     */
    @ApiModelProperty(value = "设备类型【device_type字典】")
    private String deviceType;
    /**
     * 字典值【表暂未确定】
     */
    @ApiModelProperty(value = "字典值【表暂未确定】")
    private String devicePart;
    /**
     * 图片审核状态【audit_status字典】
     */
    @ApiModelProperty(value = "图片审核状态【audit_status字典】")
    private String picAuditStatus;
    /**
     * 图片缺陷标识【pic_defect_flg字典】
     */
    @ApiModelProperty(value = "图片最终识别状态【pic_defect_flg字典】")
    private String picDefectFlg;

    @ApiModelProperty(value = "图片算法识别状态【pic_defect_flg字典】")
    private String picAlgorithmFlg;
    /**
     * 缺陷图片类型【defect_pic_type字典】
     */
    @ApiModelProperty(value = "缺陷图片类型【defect_pic_type字典】")
    private String defectPicType;
    /**
     * 是否导出过【common_yes_no字典】
     */
    @ApiModelProperty(value = "是否导出过【common_yes_no字典】")
    private String isExport;
    @ApiModelProperty(value = "风机缺陷大体的位置")
    private String fanBigPosition;
    @ApiModelProperty(value = "风机叶片排序")
    private String fanBladeOrder;
    @ApiModelProperty(value = "风机缺陷具体位置")
    private String fanDetailPosition;
    /**
     * 最后一次导出时间
     */
    @ApiModelProperty(value = "最后一次导出时间")
    private Date exportTime;

}
