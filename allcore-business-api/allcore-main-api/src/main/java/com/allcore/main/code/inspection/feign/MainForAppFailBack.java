package com.allcore.main.code.inspection.feign;

import com.allcore.app.code.flightsorties.vo.*;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.defect.dto.DefectTaggingInfoCollectDTO;
import com.allcore.main.code.fences.vo.FenceAlarmVO;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.feign.MainForAppClient;
import com.allcore.main.code.inspection.vo.AppRouteResponseVO;
import com.allcore.main.code.inspection.vo.InspectionTaskVO;
import com.allcore.main.code.source.vo.AppDeviceDetailVO;
import com.allcore.main.code.source.vo.AppLedgerDetailVO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MainForAppFailBack implements MainForAppClient {

    @Override
    public R<FenceAlarmVO> fenceAlarmVerification(String userId, String longitude, String latitude) {
        return R.fail("查询失败！");
    }

    @Override
    public R<InspectionTask> getOrderByGuid(String inspectionTaskId) {
        return R.fail("查询失败！");
    }

    @Override
    public R fanFolderUpload(FolderUpLoadDTO dto) {
        return  R.fail("查询失败！");
    }


    /**
     * 根据工单负责人id查询工单
     *
     * @param map
     * @return
     */
    @Override
    public R<AppPageVO<AppWorkOrderVO>> getOrderByCommander(Map map) {
        return R.fail("查询失败！");
    }

    /**
     * 查询工单设备
     *
     * @param workOrderGuid
     * @return
     */
    @Override
    public R<List<AppWorkOderDetailVO>> getOrderDeviceByOrderGuid(String workOrderGuid) {
        return R.fail("查询失败！");
    }

    @Override
    public R updateAppInspectStatus(String workOrderGuid, String deviceGuid) {
        return R.fail("查询失败！");
    }

    @Override
    public R<Boolean> saveDefectTaggingInfoCollect(DefectTaggingInfoCollectDTO dto) {
        return R.fail("查询失败！");
    }

    @Override
    public R<HashMap<String, String>> getDeviceInformationForOrderPic(Map<String, List<Long>> deviceGuids) {
        return R.fail("失败！");
    }

    @Override
    public R uploadPicForApp(AppInspectionPictureDTO  param) {
        return R.fail("操作失败！");

    }

    @Override
    public R<List<AppLedgerDetailVO>> getDeviceByDeptId(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<Map<String, BasicCommonVO>> getCommonDeviceByIds(List<String> deviceIds, String deviceType, String deptCode)  {
        return R.fail("查询失败！");
    }

    @Override
    public R<AppDeviceDetailVO> getDeviceByGroupGuid(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<Map<String, String>> getUavInformationForApp(String sn) {
        return R.fail("查询失败！");
    }

    @Override
    public R<AppRouteResponseVO> getRouteInfo(Map<String, String> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<Map<String, BasicCommonVO>> getDeviceByDeviceIds(List<String> deviceIds, String deviceType) {
        return R.fail("查询失败！");
    }

    @Override
    public R<InspectionTaskVO> getTaskAndUserInfo(String userId) {
        return R.fail("查询失败！");
    }

    @Override
    public R<AppPageVO<AppInspectionTaskVO>> getAppInspectionTaskVO(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<AppInspectionTaskOnlineReportingVO> queryAppTaskReport(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<List<AppPvTrackVO>> appGetRoute(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<List<List<String>>> appGetPicByDevice(Map<String, Object> map) {
        return R.fail("查询失败！");
    }

    @Override
    public R<AppStatisticVO> getPvStatistics(Map<String, Object> map) {
        return R.fail("查询失败！");
    }
}
