package com.allcore.main.code.source.excel;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.allcore.main.code.common.dto.BaseExcelDTO;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.allcore.main.code.source.entity.Line;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * LineExcel 实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LineExcel extends BaseExcelDTO {
    @Excel(name = "*线路名称")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 100, message = "长度范围 1-100")
    private String lineName;

    @Excel(name = "*区域特征")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 255, message = "长度范围 1-255")
    private String regionFeatures;

    @Excel(name = "*电压等级")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String voltageLevel;

    @Excel(name = "*架设方式")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 255, message = "长度范围 1-255")
    private String erectionMethod;

    @Excel(name = "*线路总长度（km）")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 64, message = "线路总长度（km）长度范围 1-64")
    private String lineTotalLength;

    @Excel(name = "*投运时间", format = "yyyy-MM-dd")
    @NotNull(message = "不能为空")
    private Date putDate;

    @Excel(name = "单位名称")
    @Size(min = 3, max = 64, message = "单位长度范围 3-64")
    private String deptCode;

}
