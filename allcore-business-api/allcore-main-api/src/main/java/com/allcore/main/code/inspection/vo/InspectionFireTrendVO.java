package com.allcore.main.code.inspection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@ApiModel(value = "InspectionPictureTaggingVO对象", description = "InspectionPictureTaggingVO对象")
public class InspectionFireTrendVO {
	/**
	 * 月份
	 */
	@ApiModelProperty(value = "月份")
	private int month;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private String number;


}
