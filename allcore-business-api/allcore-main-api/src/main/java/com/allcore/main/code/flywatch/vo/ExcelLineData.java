package com.allcore.main.code.flywatch.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ExcelLineData对象", description = "AirspaceVO对象")
public class ExcelLineData {
    String bureauCode;
    String flId;
    String siteName;
    String flName;
    double longitude;
    double latitude;
    String classifyName;
    String voltageId;
    String bureauName;
    String updateTime;
}
