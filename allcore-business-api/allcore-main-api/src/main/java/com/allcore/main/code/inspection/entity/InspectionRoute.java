package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @program: bl
 * @description: 巡检路线实体类
 * @author: fanxiang
 * @create: 2025-06-05 20:13
 **/

@Data
@TableName("main_inspection_route")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="InspectionRoute对象", description = "巡检路线对象")
public class InspectionRoute extends ZxhcEntity {

    private static final long serialVersionUID = 12545232054227L;

    @NotBlank
    @Size(min=1,max=64,message = "路线名称长度范围 1-64")
    @ApiModelProperty("路线名称")
    private String routeName;

    @ApiModelProperty("路线编号")
    private String routeCode;

    @NotBlank(message = "站点id不能为空")
    @ApiModelProperty("站点id")
    private String stationId;

    @ApiModelProperty("站点名称")
    private String stationName;

    @NotBlank(message = "设备类型不能为空")
    @ApiModelProperty(value="设备类型(1.风机 2.光伏 3.线路)")
    private String deviceType;

    @ApiModelProperty(value="设备数量")
    private Integer deviceCount;

    @Size(max=255,message = "路线描述长度不能超过255")
    @ApiModelProperty(value="路线描述")
    private String routeDescription;

    /**
     * 状态（0-禁用 1-启用）
     */
    @ApiModelProperty(value = "状态（0-禁用 1-启用）")
    private Integer status;

}
