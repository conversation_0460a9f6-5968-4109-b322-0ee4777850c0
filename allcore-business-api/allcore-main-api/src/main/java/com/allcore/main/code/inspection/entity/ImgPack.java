package com.allcore.main.code.inspection.entity;

import java.util.Date;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@TableName("main_img_pack")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ImgPack对象", description = "ImgPack对象")
public class ImgPack extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 文件guid
     */
    @ApiModelProperty(value = "文件guid")
    private String fileGuid;
    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 巡检或消缺 (patrol | eliminateDefects)
     */
    @ApiModelProperty(value = "巡检或消缺 (patrol | eliminateDefects)")
    private String type;
    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;
    /**
     * 机构code
     */
    @ApiModelProperty(value = "机构code")
    private String deptCode;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    /**
     * 打包状态
     */
    @ApiModelProperty(value = "打包状态")
    private String status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
