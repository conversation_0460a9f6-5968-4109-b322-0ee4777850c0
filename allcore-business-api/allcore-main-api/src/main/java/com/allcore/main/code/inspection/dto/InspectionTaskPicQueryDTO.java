package com.allcore.main.code.inspection.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class InspectionTaskPicQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "巡检任务id")
    private String inspectionTaskId;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "缺陷等级")
    private String defectLevel;

    @ApiModelProperty(value = "类型【defect_type字典】")
    private String defectCode;

    @ApiModelProperty(value = "消缺状态：【eliminate_status字典】")
    private String eliminateStatus;

    private String fanDefectPosition;

    private String inspectionPictureTaggingId;
    private String photoType;



}
