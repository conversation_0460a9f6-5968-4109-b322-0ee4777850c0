package com.allcore.main.code.source.vo;

import com.allcore.core.tool.node.INode;
import com.allcore.system.entity.Dept;
import com.allcore.system.vo.DeptVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * main服务的deptVO
 *
 * <AUTHOR>
 * @date 2025/01/06 13:10
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ForMainDeptVO对象", description = "ForMainDeptVO对象")
public class ForMainDeptVO extends DeptVO {
    private static final long serialVersionUID = 1L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ForMainDeptVO> child;

    @ApiModelProperty(value = "无人机列表")
    private List<UavPlaneVO> planeList;
}
