package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionRoute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @program: bl
 * @description: 巡检路线查询DTO
 * @author: fanxiang
 * @create: 2025-06-09 10:00
 **/

@Data
@ApiModel(value="InspectionRouteDTO对象", description = "巡检路线查询DTO")
public class InspectionRouteDTO implements Serializable {

    private static  final long serialVersionUID = 132155244L;

    @ApiModelProperty(value = "路线名称")
    private String routeName;

    @ApiModelProperty(value="设备类型")
    private String deviceType;
}
