package com.allcore.main.code.inspection.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-26
 */
@Data
public class DefectInfoOnlineReportingVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "标注明细id")
    private String id;
    private String deviceId;
    private String parentDeviceId;
    private String deviceName;
    private String deviceType;
    private String deviceNo;
    @ApiModelProperty(value = "杆塔编号")
    private String towerNum;

    private String bigPosition;
    private String bladeOrder;
    private String detailPosition;
    private String bigPositionZh;
    private String bladeOrderZh;
    private String detailPositionZh;
    private String positionName;
    @ApiModelProperty(value = "缺陷小图裁剪范围放大版id")
    private String bigFileGuid;
    private String bigFilePath;

    @ApiModelProperty(value = "巡检照片id")
    private String inspectionPictureId;
    private String inspectionPictureGuid;
    private String inspectionPictureFilePath;
    private String inspectionPictureFileName;
    @ApiModelProperty(value = "可见光巡检照片id")
    private String lightPictureId;
    private String lightPictureGuid;
    private String lightPictureFilePath;
    private String lightPictureFileName;

    @ApiModelProperty(value = "巡检任务id")
    private String inspectionTaskId;
    @ApiModelProperty(value = "巡检任务编号")
    private String inspectionTaskNo;

    @ApiModelProperty(value = "识别任务id")
    private String recognitionTaskId;

    @ApiModelProperty(value = "部件")
    private String component;

    @ApiModelProperty(value = "部件类型")
    private String componentType;

    @ApiModelProperty(value = "部位")
    private String part;

    @ApiModelProperty(value = "缺陷描述")
    private String defectDescription;
    private String defectDescriptionZh;

    @ApiModelProperty(value = "缺陷等级")
    private String defectLevel;
    private String defectLevelZh;

    @ApiModelProperty(value = "组串信息")
    private String pvStringId;
    private String pvStringName;
    private String pvStringLongitude;
    private String pvStringLatitude;
    private String pvStringCoordinates;

    @ApiModelProperty(value = "对应的可见光缺陷位置坐标")
    private String lightDefectKey;
    private String lightDefectFileGuid;
    private String lightDefectPicPath;
    /**
     * x最小值
     */
    private Double xmin;
    /**
     * y最小值
     */
    private Double ymin;
    /**
     * x最大值
     */
    private Double xmax;
    /**
     * y最大值
     */
    private Double ymax;

    @ApiModelProperty(value = "缺陷图")
    private String fileGuid;
    private String filePath;
    private String fileName;
    @ApiModelProperty(value = "红外图")
    private String redFileGuid;
    private String redFilePath;
    private String redFileName;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "拍摄时间")
    private String shootTime;

    @ApiModelProperty(value = "缺陷框名称")
    private String defectName;

    @ApiModelProperty(value = "类型【defect_type字典】")
    private String defectType;

    @ApiModelProperty(value = "缺陷框的key【生成规则：xmin_ymin_xmax_ymax】")
    private String defectKey;

    @ApiModelProperty(value = "消缺状态：【eliminate_status字典】")
    private String eliminateStatus;

    @ApiModelProperty(value = "光伏组件id")
    private String pvComponentId;
    private String pvComponentName;
    private String pvComponentLongitude;

    private String pvComponentLatitude;
    private String pvComponentCoordinates;

    @ApiModelProperty(value = "消缺任务编号")
    private String removeTaskName;

    @ApiModelProperty(value = "消缺时间")
    private String removeTime;
    @ApiModelProperty("部门编码")
    private String deptCode;
    @ApiModelProperty("部门编码Zh")
    private String deptCodeZh;
    @ApiModelProperty(value = "缺陷类型Zh")
    private String defectTypeZh;
    @ApiModelProperty(value = "消缺状态：【eliminate_status字典】")
    private String eliminateStatusZh;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty(value = "缺陷尺寸")
    private String defectSize;
}
