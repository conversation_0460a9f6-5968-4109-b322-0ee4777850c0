package com.allcore.main.code.inspection.dto;


import lombok.Data;


import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author：tanxiangyu
 * @Date：2025/7/18 14:57
 */
@Data
public class InspectionImageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    private String deviceId;
    private String startDate;
    private String endDate;
}
