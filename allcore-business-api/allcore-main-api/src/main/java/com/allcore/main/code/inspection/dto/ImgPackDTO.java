package com.allcore.main.code.inspection.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 2023/9/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImgPackDTO implements Serializable {

    private static final long serialVersionUID = 428839336170660037L;

    private String id;

    @ApiModelProperty("打包状态 0 打包中 1打包完成")
    private Integer status;

    @ApiModelProperty("文件guid")
    private String fileGuid;

    @ApiModelProperty("巡检或消缺 (patrol | eliminateDefects)")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty("上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date uploadTime;
}
