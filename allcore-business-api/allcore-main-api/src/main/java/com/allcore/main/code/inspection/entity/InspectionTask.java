package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.allcore.common.utils.NullToEmptyStringSerializer;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_inspection_task")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionTask对象", description = "InspectionTask对象")
public class InspectionTask extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划GUID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String inspectionTaskName;
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String inspectionTaskNo;
    /**
     * 作业性质（列如：通道巡检，设备本体巡检等）
     */
    @ApiModelProperty(value = "作业性质（列如：通道巡检，设备本体巡检等）")
    private String inspectionType;
    /**
     * 巡检方式
     */
    @ApiModelProperty(value = "巡检方式")
    private String inspectionMethod;

    @ApiModelProperty(value = "巡检区块图")
    private String inspectionAreaGuid;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private String startDate;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private String endDate;
    /**
     * 使用空域范围
     */
    @ApiModelProperty(value = "使用空域范围")
    private String useAirspace;
    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private String inspectionTaskStatus;
    /**
     * 是否是无计划工单(1是，0否)
     */
    @ApiModelProperty(value = "是否是无计划工单(1是，0否)")
    private Integer planNotFlag;
    /**
     * 其他作业人员
     */
    @ApiModelProperty(value = "其他作业人员")
    private String otherWorkers;
    /**
     * 机构code
     */
    @ApiModelProperty(value = "机构code")
    private String deptCode;

    @ApiModelProperty(value = "责任人")
    @JsonInclude(JsonInclude.Include.ALWAYS) // 确保 null 字段也参与序列化
    @JsonSerialize(nullsUsing = NullToEmptyStringSerializer.class)
    private String responsibleUser;

    @ApiModelProperty(value="是否为智能处理(1是，0否)")
    private Integer smartProcess;

   @ApiModelProperty(value="智能处理内容")
    private String processContent;

   @ApiModelProperty(value="备注（JSON格式，根据不同处理类型存储不同数据）")
    private String remark;

    @ApiModelProperty("取消下发时间")
    private String cancelTime;



/*    public String getResponsibleUser() {
        return responsibleUser == null ? "" : responsibleUser;
    }*/
}
