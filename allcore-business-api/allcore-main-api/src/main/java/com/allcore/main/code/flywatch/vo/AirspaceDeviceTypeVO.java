package com.allcore.main.code.flywatch.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@ApiModel(value = "AirspaceDeviceTypeVO对象", description = "AirspaceDeviceTypeVO对象")
public class AirspaceDeviceTypeVO {
	/**
	 * 设备类型 对应字典表device_type
	 */
	@ApiModelProperty(value = "设备类型 对应字典表device_type")
	private String deviceType;

	@ApiModelProperty(value = "详细设备")
	private List<AirspaceDeviceDetailVO> devices;

}
