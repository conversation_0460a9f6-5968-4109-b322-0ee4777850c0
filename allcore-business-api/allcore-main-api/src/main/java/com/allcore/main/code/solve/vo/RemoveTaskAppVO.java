package com.allcore.main.code.solve.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@ApiModel(value = "RemoveTaskVO对象", description = "RemoveTaskVO对象")
public class RemoveTaskAppVO {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("任务编号")
    private String removeTaskName;

    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty("消缺任务开始时间")
    private Date createTime;

    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty("下发时间")
    private Date sendTime;

    @ApiModelProperty("已完成任务的提交时间")
    private Date updateTime;


    @ApiModelProperty("已下发的缺陷总数")
    private Integer sendDefectNum;
    @ApiModelProperty("已完成的缺陷总数")
    private Integer tagCompletedCnt;

    @ApiModelProperty(value = "任务状态")
    private String removeTaskStatus;

    @ApiModelProperty(value = "任务状态zh")
    private String removeTaskStatusZh;


}
