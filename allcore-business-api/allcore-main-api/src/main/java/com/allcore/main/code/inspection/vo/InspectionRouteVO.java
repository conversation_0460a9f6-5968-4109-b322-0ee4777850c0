package com.allcore.main.code.inspection.vo;

import com.allcore.main.code.inspection.entity.InspectionRouteDevice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @program: bl
 * @description: 巡检路线VO
 * @author: fanxiang
 * @create: 2025-06-09 10:06
 **/

@Data
public class InspectionRouteVO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("路线名称")
    private String routeName;

    @ApiModelProperty("路线编号")
    private String routeCode;

    @ApiModelProperty("站点名称")
    private String stationName;

    @ApiModelProperty("设备数量")
    private Integer deviceCount;

    @ApiModelProperty("站点id")
    private String stationId;

    @ApiModelProperty("设备详情")
    private List<InspectionRouteDevice> deviceList;

}
