package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionTaskDTO extends InspectionTask {
	private static final long serialVersionUID = 1L;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;


}
