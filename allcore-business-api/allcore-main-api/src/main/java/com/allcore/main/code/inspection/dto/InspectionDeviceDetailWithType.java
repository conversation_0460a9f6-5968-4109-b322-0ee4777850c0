package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InspectionDeviceDetailWithType extends InspectionDeviceDetail {

    @ApiModelProperty(value = "父级设备Id")
    private String parentId;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 作业性质（列如：通道巡检，设备本体巡检等）
     */
    @ApiModelProperty(value = "作业性质（列如：通道巡检，设备本体巡检等）")
    private String inspectionType;
}
