package com.allcore.main.code.inspection.dto;

import java.util.List;

import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.entity.InspectionMethodDetail;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.entity.InspectionUser;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionTaskSaveDTO extends InspectionTask {
    private static final long serialVersionUID = 1L;

    /**
     * 设备类型
     */
    @ApiModelProperty(value = "远程巡检")
    private Boolean remoteFlag;

    @ApiModelProperty(value = "远程巡检的航线集合")
    private List<String> routeIds;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    /**
     * 详情列表
     */
    @ApiModelProperty(value = "详情列表")
    private List<InspectionDeviceDetail> deviceDetailList;
    /**
     * 作业人员列表
     */
    @ApiModelProperty(value = "作业人员列表")
    private List<InspectionUser> inspectionUserList;
    /**
     * 作业方式详情列表
     */
    @ApiModelProperty(value = "作业方式详情列表")
    private List<InspectionMethodDetail> inspectionMethodDetailList;

    @ApiModelProperty(value = "告警表id")
    private String alarmId;

}
