package com.allcore.main.code.inspection.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@Builder
public class ProgressQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    @NotBlank
    private String deviceId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @NotNull
    private String startDate;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @NotNull
    private String endDate;

}
