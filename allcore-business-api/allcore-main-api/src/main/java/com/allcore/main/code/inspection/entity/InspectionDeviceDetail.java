package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_inspection_device_detail")
@ApiModel(value = "InspectionDeviceDetail对象", description = "InspectionDeviceDetail对象")
public class InspectionDeviceDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 任务GUID
     */
    @ApiModelProperty(value = "任务GUID")
    private String inspectionTaskId;
    /**
     * 任务设备类型GUID
     */
    @ApiModelProperty(value = "任务设备类型GUID")
    private String inspectionDeviceTypeId;
    /**
     * 设备guid
     */
    @ApiModelProperty(value = "设备guid")
    private String deviceId;
    /**
     * 父设备guid
     */
    @ApiModelProperty(value = "父设备guid")
    private String parentDeviceId;

    @ApiModelProperty(value = "巡检报告图")
    private String inspectionReportPic;
    /**
     * 设备等级
     */
    @ApiModelProperty(value = "设备等级")
    private Integer deviceLevel;
    /**
     * 机场巡检时巡检对象航迹
     */
    @ApiModelProperty(value = "机场巡检时巡检对象航迹")
    private String routeFileId;

    @ApiModelProperty(value = "app端是否巡检 0 未巡检 1已巡检")
    private Integer inspect;

}
