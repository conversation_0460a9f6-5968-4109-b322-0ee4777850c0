package com.allcore.main.code.inspection.dto;

import java.util.List;

import com.allcore.main.code.inspection.entity.Plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlanSaveDTO extends Plan {
    private static final long serialVersionUID = 1L;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    @NotBlank(message = "设备类型不能为空，根据字典device_type取值")
    private String deviceType;
    /**
     * 设备编码集合
     */
    @ApiModelProperty(value = "设备编码集合")
    private List<String> deviceCodeList;

}
