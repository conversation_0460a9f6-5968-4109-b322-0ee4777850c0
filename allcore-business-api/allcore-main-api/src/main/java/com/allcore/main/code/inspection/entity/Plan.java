package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_plan")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Plan对象", description = "Plan对象")
public class Plan extends ZxhcEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 计划名
     */
    @ApiModelProperty(value = "计划名")
    @Size(min = 1, max = 30, message = "计划名长度范围1-30")
    private String planName;
    /**
     * 父计划GUID
     */
    @ApiModelProperty(value = "父计划ID")
    private String parentPlanId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private String startDate;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private String endDate;
    /**
     * 计划类型（见业务字典）
     */
    @ApiModelProperty(value = "计划类型（见业务字典）")
    private String planType;
    /**
     * 计划状态（计划状态（1-待上报，2-待提交，3-待审核，4-驳回待修改，5-终止，6-待执行，7-执行中，8-已完成,9-暂停））
     */
    @ApiModelProperty(value = "计划状态（计划状态（1-待上报，2-待提交，3-待审核，4-驳回待修改，5-终止，6-待执行，7-执行中，8-已完成,9-暂停））")
    private String planStatus;
    /**
     * 机构code
     */
    @ApiModelProperty(value = "机构code")
    private String deptCode;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private String progress;

}
