package com.allcore.main.code.source.vo;

import com.allcore.main.code.source.entity.Line;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LineVO对象", description = "LineVO对象")
public class LineVO extends Line {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位名称Zh")
    private String deptCodeZh;

    @ApiModelProperty(value = "电压等级Zh")
    private String voltageLevelZh;

    @ApiModelProperty(value = "区域特征Zh")
    private String regionFeaturesZh;

}
