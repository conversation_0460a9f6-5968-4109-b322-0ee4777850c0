package com.allcore.main.code.inspection.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@TableName("main_inspection_method_detail")
@ApiModel(value = "InspectionMethodDetail对象", description = "InspectionMethodDetail对象")
public class InspectionMethodDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private String inspectionTaskId;
    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID")
    private String inspectionDeviceId;

    @ApiModelProperty(value = "型号ID")
    private String modelId;

    @ApiModelProperty(value = "类别(数据字典)")
    private String uavType;

}
