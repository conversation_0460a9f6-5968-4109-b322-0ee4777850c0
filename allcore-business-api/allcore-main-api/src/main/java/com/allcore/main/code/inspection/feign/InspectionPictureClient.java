package com.allcore.main.code.inspection.feign;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.InspectionPictureSaveDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 23 6月 2025
 */
@FeignClient(name = LauncherConstant.MAIN_SERVER_NAME, path = "/inspectionpicture")
public interface InspectionPictureClient {

  @PostMapping("/saveInspectionPicture")
  public R savePicList(@RequestBody List<InspectionPictureSaveDTO> list);
}
