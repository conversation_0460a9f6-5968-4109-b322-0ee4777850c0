package com.allcore.main.code.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 算法厂家相关信息分页查询条件
 *
 * <AUTHOR>
 * @date 2022/04/28 09:29
 **/
@Data
public class AlgorithmManufacturersDTO {
	@ApiModelProperty(value = "厂家简称")
	private String manufacturersAbbreviate;

	@ApiModelProperty(value = "厂家全称")
	private String manufacturersName;

	@ApiModelProperty(value = "算法模型")
	private String algorithmModel;

}
