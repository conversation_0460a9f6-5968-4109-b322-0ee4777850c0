package com.allcore.main.code.inspection.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class InspectionTaskFileDTO {

        @NotBlank
        @ApiModelProperty(value = "文件guid")
        private String fileGuid;

        @NotBlank
        @ApiModelProperty(value = "文件名称")
        private String fileName;



        @NotBlank
        @ApiModelProperty(value = "文件全路径")
        private String webkitRelativePath;

}
