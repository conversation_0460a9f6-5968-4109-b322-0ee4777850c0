package com.allcore.main.code.inspection.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class InspectionTaskPicDTO {


    @ApiModelProperty(value = "工单guid")
    private String workOrderGuid;



    @NotNull
    @ApiModelProperty(value = "文件列表")
    private List<InspectionTaskFileDTO> files;

    @ApiModelProperty(value = "业务系统")
    private String businessSystem;



}
