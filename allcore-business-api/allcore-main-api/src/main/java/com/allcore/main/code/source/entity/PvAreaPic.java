package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Data
@TableName("main_pv_area_pic")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PvAreaPic对象", description = "PvAreaPic对象")
public class PvAreaPic extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 光伏区域id
     */
    @ApiModelProperty(value = "光伏区域id")
    private String pvAreaId;
    /**
     * 图片类型
     */
    @ApiModelProperty(value = "图片类型")
    private String pvAreaPicType;
    /**
     * 文件guid
     */
    @ApiModelProperty(value = "文件guid")
    private String fileGuid;


}
