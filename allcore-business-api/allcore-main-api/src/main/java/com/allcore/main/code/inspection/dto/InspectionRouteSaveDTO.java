package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionRoute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @program: bl
 * @description: 巡检路线dto类
 * @author: fanxiang
 * @create: 2025-06-05 20:33
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="inspectionRouteSaveDTO对象", description = "巡检路线保存dto类")
public class InspectionRouteSaveDTO extends InspectionRoute {

    private static final long serialVersionUID = 171154545L;

    /**
     *  设备ID列表
     */
    @NotEmpty(message = "设备列表不能为空")
    @Size(min=2,message = "至少选择两个设备")
    @ApiModelProperty(value = "设备ID列表")
    private List<String> deviceIds;

    /**
     *  设备详情列表(包含设备信息和坐标)
     */
    @ApiModelProperty(value = "设备详情列表")
    private List<InspectionRouteDeviceDTO> deviceList;


}
