package com.allcore.main.code.defect.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 逆变器离散率视图类
 *
 * @Author：tanxiangyu
 * @Date：2025/7/28 11:14
 */
@Data
public class DeviceAnalysisVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "逆变器编号")
    private String name;
    @ApiModelProperty(value = "光伏区域id")
    private String pvAreaId;
    @ApiModelProperty(value = "小时离散率")
    private Double discreteRate;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "平均离散率")
    private Double averageDiscreteRate;

}
