package com.allcore.main.code.inspection.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_plan_device_detail")
@ApiModel(value = "PlanDeviceDetail对象", description = "PlanDeviceDetail对象")
public class PlanDeviceDetail implements Serializable {

    private static final long serialVersionUID = 1L;


    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 计划guid
     */
    @ApiModelProperty(value = "计划id")
    private String planId;
    /**
     * 计划设备类型guid
     */
    @ApiModelProperty(value = "计划设备类型id")
    private String planDeviceTypeId;
    /**
     * 设备guid
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 父设备guid
     */
    @ApiModelProperty(value = "父设备id")
    private String parentDeviceId;
    /**
     * 设备等级
     */
    @ApiModelProperty(value = "设备等级")
    private Integer deviceLevel;

}
