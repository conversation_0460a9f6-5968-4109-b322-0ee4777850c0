package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @program: bl
 * @description: 巡检路线设备实体类
 * @author: fanxiang
 * @create: 2025-06-06 16:10
 **/

@Data
@TableName("main_inspection_route_device")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="InspectionRouteDevice对象", description = "巡检路线设备对象")
public class InspectionRouteDevice extends ZxhcEntity {

    private static final long serialVersionUID = 12548415212154L;

    @NotBlank(message = "路线id不能为空")
    @ApiModelProperty("路线id")
    private String routeId;

     @NotBlank(message = "设备id不能为空")
    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("设备顺序")
    private Integer deviceOrder;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备类型(1.风机 2.光伏 3.线路)")
    private String deviceType;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty(value = "高程")
    private String elevation;
}
