package com.allcore.main.code.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: bl
 * @description: 巡检路线设备DTO类
 * @author: fanxiang
 * @create: 2025-06-06 09:19
 **/

@Data
@ApiModel(value="inspectionRouteDeviceDTO对象", description = "巡检路线设备dto类")
public class InspectionRouteDeviceDTO {


    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("设备顺序")
    private Integer deviceOrder;

    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty(value = "高程")
    private String elevation;


}
