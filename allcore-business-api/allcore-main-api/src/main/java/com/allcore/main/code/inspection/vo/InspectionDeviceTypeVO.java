package com.allcore.main.code.inspection.vo;

import com.allcore.main.code.inspection.entity.InspectionDeviceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionDeviceTypeVO对象", description = "InspectionDeviceTypeVO对象")
public class InspectionDeviceTypeVO extends InspectionDeviceType {
	private static final long serialVersionUID = 1L;

}
