package com.allcore.main.code.inspection.entity;

import com.allcore.common.base.ZxhcEntity;
import com.allcore.core.tool.utils.StringUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@TableName("main_inspection_picture_tagging")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionPictureTagging对象", description = "InspectionPictureTagging对象")
public class InspectionPictureTagging extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 巡检照片guid
     */
    @ApiModelProperty(value = "巡检照片guid")
    private String inspectionPictureId;
    /**
     * 巡检任务guid
     */
    @ApiModelProperty(value = "巡检任务guid")
    private String inspectionTaskId;
    @ApiModelProperty(value = "巡检任务编号")
    private String inspectionTaskNo;
    /**
     * 识别任务guid
     */
    @ApiModelProperty(value = "识别任务guid")
    private String recognitionTaskId;

    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;

    /**
     * 设备类型【device_type字典】
     */
    @ApiModelProperty(value = "设备类型【device_type字典】")
    private String deviceType;
    /**
     * 部件
     */
    @ApiModelProperty(value = "部件")
    private String component;
    /**
     * 部件类型
     */
    @ApiModelProperty(value = "部件类型")
    private String componentType;
    /**
     * 部位
     */
    @ApiModelProperty(value = "部位")
    private String part;
    /**
     * 缺陷描述
     */
    @ApiModelProperty(value = "缺陷描述")
    private String defectDescription;
    @ApiModelProperty(value = "缺陷描述算法")
    private String defectDescriptionAlgorithm;
    /**
     * 缺陷等级
     */
    @ApiModelProperty(value = "缺陷等级")
    private String defectLevel;

    /**
     * x最小值
     */
    private Double xmin;
    /**
     * y最小值
     */
    private Double ymin;
    /**
     * x最大值
     */
    private Double xmax;
    /**
     * y最大值
     */
    private Double ymax;

    /**
     * x最小值
     */
    private Double xbigmin;
    /**
     * y最小值
     */
    private Double ybigmin;
    /**
     * x最大值
     */
    private Double xbigmax;
    /**
     * y最大值
     */
    private Double ybigmax;

    /**
     * x最小值
     */
    @TableField(exist = false)
    private Double xbigminLight;
    /**
     * y最小值
     */
    @TableField(exist = false)
    private Double ybigminLight;
    /**
     * x最大值
     */
    @TableField(exist = false)
    private Double xbigmaxLight;
    /**
     * y最大值
     */
    @TableField(exist = false)
    private Double ybigmaxLight;
    /**
     * 缺陷小图id
     */
    @ApiModelProperty(value = "缺陷小图id")
    private String fileGuid;

    @ApiModelProperty(value = "缺陷小图裁剪范围放大版id")
    private String bigFileGuid;

    /**
     * 缺陷框名称
     */
    @ApiModelProperty(value = "缺陷框名称")
    private String defectName;
    /**
     * 审核状态【audit_status字典】
     */
    @ApiModelProperty(value = "审核状态【audit_status字典】")
    private String auditStatus;
    /**
     * 类型【defect_type字典】
     */
    @ApiModelProperty(value = "类型【defect_type字典】")
    private String defectType;
    /**
     * 标注类型【tagging_type字典】
     */
    @ApiModelProperty(value = "标注类型【tagging_type字典】")
    private String taggingType;
    /**
     * 缺陷框的key【生成规则：xmin_ymin_xmax_ymax】
     */
    @ApiModelProperty(value = "缺陷框的key【生成规则：xmin_ymin_xmax_ymax】")
    private String defectKey;

    @ApiModelProperty(value = "对应的可见光缺陷位置坐标")
    private String lightDefectKey;
    /**
     * 是否是算法识别生成【common_yes_no字典】
     */
    @ApiModelProperty(value = "是否是算法识别生成【common_yes_no字典】")
    private String isAlgorithm;
    /**
     * 是否移动过位置【common_yes_no字典】
     */
    @ApiModelProperty(value = "是否移动过位置【common_yes_no字典】")
    private String movePosition;
    /**
     * 消缺状态：【eliminate_status字典】
     */
    @ApiModelProperty(value = "消缺状态：【eliminate_status字典】")
    private String eliminateStatus;
    /**
     * 系统类型【system_type字典】
     */
    @ApiModelProperty(value = "系统类型【system_type字典】")
    private String systemType;
    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String examine;
    /**
     * 光伏设备树，顶-当前，分割
     */
    @ApiModelProperty(value = "光伏组件id")
    private String pvComponentId;

    @ApiModelProperty(value = "光伏组件名称")
    private String pvComponentName;

    /**
     * 是否生过成报告【common_yes_no字典】
     */
    @ApiModelProperty(value = "是否生过成报告【common_yes_no字典】")
    private String isCreateReport;
    /**
     * 温度
     */
    @ApiModelProperty(value = "温度")
    private String temperature;

    @TableField(exist = false)
    private String removeTaskStatus;

    @TableField(exist = false)
    private String pvStringId;

    public boolean containsFieldInAnotherField(InspectionPictureTagging otherObj) {
        return StringUtil.isNotBlank(otherObj.lightDefectKey) && defectKey.contains(otherObj.lightDefectKey);
    }

    /**
     * 是否可见光
     */
    @ApiModelProperty(value = "是否可见光")
    private String isZ;
    @ApiModelProperty(value = "缺陷尺寸")
    private String defectSize;
    @ApiModelProperty(value = "组id")
    private String groupId;
}
