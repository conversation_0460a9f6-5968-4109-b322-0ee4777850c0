package com.allcore.main.code.inspection.dto;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
public class ImgPackQueryDTO implements Serializable {

    private static final long serialVersionUID = 289802463110213221L;

    @NotBlank
    @Pattern(regexp = "^(patrol|eliminateDefects)$", message = "类型不正确")
    @ApiModelProperty("类型,巡检或消缺  (patrol|eliminateDefects)")
    private String type;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("上传时间(创建时间) - 列表筛选使用")
    private Date uploadTime;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    private String deptCode;
}
