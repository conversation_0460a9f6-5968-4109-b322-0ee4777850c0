package com.allcore.main.code.inspection.dto;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotBlank;

import org.springframework.format.annotation.DateTimeFormat;

import com.allcore.core.secure.utils.AuthUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <p>
 * 巡检数据影像查询实体
 * </p>
 *
 * @author: ty Date: 2023/9/18
 */
@ApiModel("巡检数据影像查询实体")
@Builder
@Data
public class PatrolDataImageQueryDTO implements Serializable {

    private static final long serialVersionUID = -6782092955585556756L;

    @ApiModelProperty("部门code")
    private String deptCode = AuthUtil.getDeptCode();

    @ApiModelProperty("巡检装备类型")
    private String patrolType;

    @ApiModelProperty("设备类型")
    @NotBlank(message = "设备类型不能为空")
    private String deviceType;

    @ApiModelProperty("设备id")
    private String deviceId;
    private String deviceName;

    @ApiModelProperty("巡检工单编号")
    private String workOrderNum;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("上传图片时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date uploadImgTime;
}
