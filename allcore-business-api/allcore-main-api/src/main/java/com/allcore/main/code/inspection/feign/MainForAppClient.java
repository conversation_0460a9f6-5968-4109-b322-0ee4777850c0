package com.allcore.main.code.inspection.feign;


import com.allcore.app.code.flightsorties.vo.*;
import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.defect.dto.DefectTaggingInfoCollectDTO;
import com.allcore.main.code.fences.vo.FenceAlarmVO;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.vo.AppRouteResponseVO;
import com.allcore.main.code.inspection.vo.InspectionTaskVO;
import com.allcore.main.code.source.vo.AppDeviceDetailVO;
import com.allcore.main.code.source.vo.AppLedgerDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FeignClient(value = LauncherConstant.MAIN_SERVER_NAME,fallback = MainForAppFailBack.class)
public interface MainForAppClient {

    String API_PREFIX = "/mainForApp";

    String GET_QUERY_TASK = API_PREFIX + "/getOrderByGuid";

    String GET_ORDER_BY_COMMANDER = API_PREFIX + "/getOrderByCommander";
    String QUERY_APP_INSPECTION_TASK_PAGE = API_PREFIX + "/queryAppInspectionTaskPage";
    String QUERY_APP_TASK_REPORT = API_PREFIX + "/queryAppTaskReport";
    String APP_GET_ROUTE = API_PREFIX + "/appGetRoute";
    String APP_GET_PIC_BY_DEVICE = API_PREFIX + "/appGetPicByDevice";
    String APP_GET_STATISTICS = API_PREFIX + "/getPvStatistics";
    String FAN_FOLDER_UPLOAD = API_PREFIX + "/fanFolderUpload";

    String GET_ORDER_DEVICE_BY_ORDER_GUID = API_PREFIX + "/getOrderDeviceByOrderGuid";

    String UPDATE_APP_INSPECT_STATUS_BY_ORDER_GUID_AND_DEVICE = API_PREFIX + "/updateAppInspectStatus";

    String GET_DEVICE_INFORMATION_FOR_ORDER_PIC = API_PREFIX + "/getDeviceInformationForOrderPic";

    String UPLOAD_PIC_FOR_APP = API_PREFIX + "/uploadPicForApp";

    String GET_DEVICE_BY_DEPT = API_PREFIX + "/getDeviceByDept";

    String GET_DEVICE_BY_GROUP_GUID = API_PREFIX + "/getDeviceByGroupGuid";

    String GET_UAV_INFORMATION_FOR_APP = API_PREFIX + "/getUavInformationForApp";

    String GET_ROUTE_INFO = API_PREFIX + "/getRouteInfo";

    String GET_DEVICE_BY_DEVICE_IDS= API_PREFIX+"/getDeviceByDeviceIds";

    String GET_COMMON_DEVICE = API_PREFIX+"getCommonDeviceByIds";

    String GET_TASK_AND_USER_INFO = API_PREFIX + "/getTaskAndUserInfo";

    String CHECK_FENCE_USER_ALARM = API_PREFIX + "/checkFenceAlarm";

    @GetMapping(CHECK_FENCE_USER_ALARM)
    R<FenceAlarmVO> fenceAlarmVerification(@RequestParam String userId, @RequestParam String longitude, @RequestParam String latitude);

    @GetMapping(GET_QUERY_TASK)
    R<InspectionTask> getOrderByGuid(@RequestParam String inspectionTaskId);

    @PostMapping(FAN_FOLDER_UPLOAD)
    R fanFolderUpload(@RequestBody FolderUpLoadDTO dto);

    /**
     * 根据工单负责人id查询工单
     * @param map
     * @return
     */
    @PostMapping(GET_ORDER_BY_COMMANDER)
    R<AppPageVO<AppWorkOrderVO>> getOrderByCommander(@RequestBody Map map);

    /**
     * 查询工单设备
     * @param workOrderGuid
     * @return
     */
    @GetMapping(GET_ORDER_DEVICE_BY_ORDER_GUID)
    R<List<AppWorkOderDetailVO>> getOrderDeviceByOrderGuid(@RequestParam String workOrderGuid);

    /**
     * app更新执行状态
     * @param workOrderGuid
     * @param deviceGuid
     * @return
     */
    @GetMapping(UPDATE_APP_INSPECT_STATUS_BY_ORDER_GUID_AND_DEVICE)
    R updateAppInspectStatus(@RequestParam String workOrderGuid,@RequestParam String deviceGuid);

    @PostMapping("/app/defect/tagging/info/collect/save")
    R<Boolean> saveDefectTaggingInfoCollect(@RequestBody DefectTaggingInfoCollectDTO dto);

    /**
     * 专为巡检图片上传定制
     * @param deviceGuids
     * @return
     */
    @PostMapping(GET_DEVICE_INFORMATION_FOR_ORDER_PIC)
    R<HashMap<String, String>> getDeviceInformationForOrderPic(@RequestBody Map<String, List<Long>> deviceGuids);

    /**
     * app回传图片
     * @param param
     * @return
     */
    @PostMapping(UPLOAD_PIC_FOR_APP)
    R uploadPicForApp(@RequestBody AppInspectionPictureDTO param);

    /**
     * 根据单位查询设备
     * @param map
     * @return
     */
    @PostMapping(GET_DEVICE_BY_DEPT)
    R<List<AppLedgerDetailVO>> getDeviceByDeptId(@RequestBody Map<String,Object> map);

    /**
     * 根据一级设备查询二级或当前设备
     * @param map
     * @return
     */
    @PostMapping(GET_DEVICE_BY_GROUP_GUID)
    R<AppDeviceDetailVO> getDeviceByGroupGuid(@RequestBody Map<String, Object> map);

    /**
     * 查询无人机信息
     * @param sn
     * @return
     */
    @GetMapping(GET_UAV_INFORMATION_FOR_APP)
    R<Map<String,String>> getUavInformationForApp(@RequestParam String sn);

    /**
     * 获取航线文件
     * @param map
     * @return
     */
    @PostMapping(GET_ROUTE_INFO)
    R<AppRouteResponseVO> getRouteInfo(@RequestBody Map<String,String> map);

    @PostMapping(GET_DEVICE_BY_DEVICE_IDS)
    R<Map<String, BasicCommonVO>> getDeviceByDeviceIds(@RequestParam List<String> deviceIds, @RequestParam String deviceType);

    @PostMapping(GET_COMMON_DEVICE)
    R<Map<String, BasicCommonVO>> getCommonDeviceByIds(@RequestParam List<String> deviceIds, @RequestParam String deviceType, @RequestParam String deptCode);

    @GetMapping(GET_TASK_AND_USER_INFO)
    R<InspectionTaskVO> getTaskAndUserInfo(@RequestParam String userId);

    /**
     * 查询建任务信息
     * @param map
     * @return
     */
    @PostMapping(QUERY_APP_INSPECTION_TASK_PAGE)
    R<AppPageVO<AppInspectionTaskVO>> getAppInspectionTaskVO(@RequestBody Map<String, Object> map);

    /**
     * 任务统计信息
     * @param map
     * @return
     */
    @PostMapping(QUERY_APP_TASK_REPORT)
    R<AppInspectionTaskOnlineReportingVO> queryAppTaskReport(@RequestBody Map<String, Object> map);

    @PostMapping(APP_GET_ROUTE)
    R<List<AppPvTrackVO>> appGetRoute(@RequestBody Map<String, Object> map);

    @PostMapping(APP_GET_PIC_BY_DEVICE)
    R<List<List<String>>> appGetPicByDevice(@RequestBody Map<String, Object> map);
    @PostMapping(APP_GET_STATISTICS)
    R<AppStatisticVO> getPvStatistics(@RequestBody Map<String, Object> map);
}
