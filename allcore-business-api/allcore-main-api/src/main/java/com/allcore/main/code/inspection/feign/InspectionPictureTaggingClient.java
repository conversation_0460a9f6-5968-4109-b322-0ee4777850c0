package com.allcore.main.code.inspection.feign;


import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.InspectionTaskPicQueryDTO;
import com.allcore.main.code.inspection.vo.DefectInfoOnlineReportingVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name= LauncherConstant.MAIN_SERVER_NAME, path = "/inspectionpicturetagging")
public interface InspectionPictureTaggingClient {


        @PostMapping("/pvRemoveDefectDetailInfo")
        public R<List<DefectInfoOnlineReportingVO>> getDefectDetailInfo(@RequestBody InspectionTaskPicQueryDTO dto);



}
