package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
public class InspectionPictureTaggingDTO{

	private String id;
	/**
	 * 巡检照片guid
	 */
	@ApiModelProperty(value = "巡检照片guid")
	private String inspectionPictureId;
	/**
	 * 巡检任务guid
	 */
	@ApiModelProperty(value = "巡检任务guid")
	private String inspectionTaskId;
	@ApiModelProperty(value = "巡检任务编号")
	private String inspectionTaskNo;
	/**
	 * 识别任务guid
	 */
	@ApiModelProperty(value = "识别任务guid")
	private String recognitionTaskId;

	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private String deviceId;

	/**
	 * 设备类型【device_type字典】
	 */
	@ApiModelProperty(value = "设备类型【device_type字典】")
	private String deviceType;
//	/**
//	 * 部件
//	 */
//	@ApiModelProperty(value = "部件")
//	private String component;
//	/**
//	 * 部件类型
//	 */
//	@ApiModelProperty(value = "部件类型")
//	private String componentType;
//	/**
//	 * 部位
//	 */
//	@ApiModelProperty(value = "部位")
//	private String part;
	/**
	 * 缺陷描述
	 */
	@ApiModelProperty(value = "缺陷描述")
	private String defectDescription;
	@ApiModelProperty(value = "缺陷描述算法")
	private String defectDescriptionAlgorithm;
	/**
	 * 缺陷等级
	 */
	@ApiModelProperty(value = "缺陷等级")
	private String defectLevel;

	/**
	 * x最小值
	 */
	private Double xmin;
	/**
	 * y最小值
	 */
	private Double ymin;
	/**
	 * x最大值
	 */
	private Double xmax;
	/**
	 * y最大值
	 */
	private Double ymax;
	/**
	 * 缺陷小图id
	 */
	@ApiModelProperty(value = "缺陷小图id")
	private String fileGuid;

	@ApiModelProperty(value = "缺陷小图裁剪范围放大版id")
	private String bigFileGuid;

	/**
	 * 缺陷框名称
	 */
	@ApiModelProperty(value = "缺陷框名称")
	private String defectName;

	/**
	 * 类型【defect_type字典】
	 */
	@ApiModelProperty(value = "类型【defect_type字典】")
	private String defectType;
	/**
	 * 标注类型【tagging_type字典】
	 */
	@ApiModelProperty(value = "标注类型【tagging_type字典】")
	private String taggingType;
	/**
	 * 缺陷框的key【生成规则：xmin_ymin_xmax_ymax】
	 */
	@ApiModelProperty(value = "缺陷框的key【生成规则：xmin_ymin_xmax_ymax】")
	private String defectKey;
	/**
	 * 是否是算法识别生成【common_yes_no字典】
	 */
	@ApiModelProperty(value = "是否是算法识别生成【common_yes_no字典】")
	private String isAlgorithm;

	/**
	 * 消缺状态：【eliminate_status字典】
	 */
	@ApiModelProperty(value = "消缺状态：【eliminate_status字典】")
	private String eliminateStatus;


}
