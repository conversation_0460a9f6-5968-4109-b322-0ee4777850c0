package com.allcore.main.code.source.vo;

import com.allcore.main.code.source.entity.UavPlane;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UavPlaneVO对象", description = "UavPlaneVO对象")
public class UavPlaneVO extends UavPlane {

    private static final long serialVersionUID = 1L;
    /**
     * 无人机品牌
     */
    @ApiModelProperty(value = "无人机品牌")
    private String uavBrand;
    /**
     * 类别(数据字典)
     */
    @ApiModelProperty(value = "类别(数据字典)")
    private String uavType;

    @ApiModelProperty(value = "单位名称Zh")
    private String deptCodeZh;

    @ApiModelProperty(value = "无人机品牌Zh")
    private String uavBrandZh;

    @ApiModelProperty(value = "类别(数据字典)Zh")
    private String uavTypeZh;

    @ApiModelProperty(value = "资产属性(数据字典)Zh")
    private String assetAttributeZh;

    @ApiModelProperty(value = "制造厂商Zh")
    private String manufacturerZh;

    @ApiModelProperty(value = "型号名称")
    private String modelName;

    @ApiModelProperty(value = "飞行状态")
    private String flyState = "0";
    @ApiModelProperty(value = "飞行类型")
    private String flyType = "0";

}
