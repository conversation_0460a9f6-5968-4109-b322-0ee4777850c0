package com.allcore.main.code.flywatch.entity;

import com.allcore.common.base.ZxhcEntity;
import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@TableName("main_airspace")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Airspace对象", description = "Airspace对象")
public class Airspace extends ZxhcEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 空域类型 1临时空域  2年度空域
     */
    @ApiModelProperty(value = "空域类型 1临时空域  2年度空域")
    private String airspaceType;
    /**
     * 飞行真高（单位：米）
     */
    @ApiModelProperty(value = "飞行真高（单位：米）")
    private BigDecimal flyRealHigh;
    /**
     * 审核状态：1待提交 2待审核 3未通过 4已通过
     */
    @ApiModelProperty(value = "审核状态：1待提交 2待审核 3未通过 4已通过")
    private String auditStatus;
    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value = "批文状态")
    private String fileStatus;



}
