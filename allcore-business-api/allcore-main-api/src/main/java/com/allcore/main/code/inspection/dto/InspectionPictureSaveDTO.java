package com.allcore.main.code.inspection.dto;

import com.allcore.main.code.inspection.entity.InspectionPicture;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionPictureSaveDTO extends InspectionPicture {
	private static final long serialVersionUID = 1L;


}
