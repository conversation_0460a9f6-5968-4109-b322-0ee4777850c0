package com.allcore.main.code.source.vo;

import com.allcore.main.code.source.entity.Airport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AirportVO对象", description = "AirportVO对象")
public class AirportVO extends Airport {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "单位名称Zh")
	private String deptCodeZh;

	@ApiModelProperty(value = "无人机品牌Zh")
	private String airportBrandZh;

	@ApiModelProperty("搭载无人机品牌")
	private String planeBrandZh;

	@ApiModelProperty(value = "类别(数据字典)Zh")
	private String airportTypeZh;

	@ApiModelProperty(value = "资产属性(数据字典)Zh")
	private String assetAttributeZh;

	@ApiModelProperty(value = "制造厂商Zh")
	private String airportModelZh;

	@ApiModelProperty(value = "运维单位Zh")
	private String unitZh;

	@ApiModelProperty(value = "无人机品牌Zh")
	private String uavBrandZh;

	@ApiModelProperty(value = "类别(数据字典)Zh")
	private String uavTypeZh;

	/**
	 * 机巢状态
	 */
	private String nestStatus;
	private String nestStatusZh;


	/**
	 * 修改后经度
	 */
	private String longitude;
	/**
	 * 修改后纬度
	 */
	private String latitude;
	private String elevation;

	@ApiModelProperty("站点id - 第三方飞控平台")
	private String siteId;


}
