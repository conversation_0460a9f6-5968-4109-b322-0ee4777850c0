package com.allcore.main.code.source.feign;


import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.source.dto.DeviceForMainNameDTO;
import com.allcore.main.code.source.dto.QrCodeQueryDTO;
import com.allcore.main.code.source.vo.FanVO;
import com.allcore.main.code.source.vo.QrCodeDeviceVO;
import com.allcore.main.code.source.vo.QrCodeManagementVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


/**
 * <AUTHOR>
 * @description: 台账feign
 */
@FeignClient(value = LauncherConstant.MAIN_SERVER_NAME,fallback = IMainSourceFailBack.class)
public interface IMainSourceClient {

    String API_PREFIX = "/mainSource";

    String GET_DECVICE_NAME_BY_ID = API_PREFIX + "/getDeviceNameById";

    String GET_QRCODE_DEVICE_INFO = API_PREFIX + "/getQrcodeDeviceInfo";

    String GET_DEVICE_PAGE_BY_TYPE = API_PREFIX + "/getDevicePageByType";

    String GET_DEVICE_FAN_INFO_BY_ID = API_PREFIX + "/getDeviceFanInfoById";


    @PostMapping(GET_DECVICE_NAME_BY_ID)
    R<DeviceForMainNameDTO> getDeviceNameById(@RequestBody Map<String, String> map);

    @GetMapping(GET_QRCODE_DEVICE_INFO)
    R<QrCodeDeviceVO> getDeviceInfo(@RequestParam String deviceId, @RequestParam String deviceType);

    @GetMapping(GET_DEVICE_PAGE_BY_TYPE)
    R<Page<QrCodeManagementVO>> getDevicePageByType(@RequestParam(required = false) String current, @RequestParam(required = false) String size, @RequestParam String deviceType, @RequestParam String deptCode);


    @GetMapping(GET_DEVICE_FAN_INFO_BY_ID)
    R<FanVO> getDeviceFanInfoById(@RequestParam String fanId);
}
