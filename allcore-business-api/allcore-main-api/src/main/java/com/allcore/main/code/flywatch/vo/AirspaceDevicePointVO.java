package com.allcore.main.code.flywatch.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@ApiModel(value = "AirspaceDeviceDetailVO对象", description = "AirspaceDeviceDetailVO对象")
public class AirspaceDevicePointVO {
	private static final long serialVersionUID = 1L;



	private Double longitude;
	private Double latitude;
	private String deviceName;



}
