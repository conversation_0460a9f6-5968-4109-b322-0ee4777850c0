<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>allcore-business-api</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-main-api</artifactId>
    <name>${project.artifactId}</name>
    <version>${allcore.project.version}</version>
    <packaging>jar</packaging>
    <properties>
        <poi.version>4.1.1</poi.version>
        <easypoi.version>4.1.2</easypoi.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-common</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-system-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-file-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-external-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${easypoi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-app-api</artifactId>
            <version>PRODUCT.1.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


</project>
