package com.allcore.app.code.flightsorties.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * APP工单列表基类
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Data
@ApiModel(value = "AppWorkOrderVO对象", description = "工单列表基类")
public class AppWorkOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 工单guid
     */
    @ApiModelProperty(value = "工单guid")
    private String inspectGuid;

    /**
     * 工单名
     */
    @ApiModelProperty(value = "工单名")
    private String inspectName;

    /**
     * 工单创建单位名称
     */
    @ApiModelProperty(value = "工单创建单位名称")
    private String workerGroupName;

    /**
     * 签发人
     */
    @ApiModelProperty(value = "签发人")
    private String signer;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String appRover;

    /**
     * 计划工作许可时间
     */
    @ApiModelProperty(value = "计划工作许可时间")
    private Long planPermitTime;

    /**
     * 工单计划工作开始时间
     */
    @ApiModelProperty(value = "工单计划工作开始时间")
    private Long planStartTime;

    /**
     * 工单计划工作结束时间 可以为0 则 不限制超时
     */
    @ApiModelProperty(value = "工单计划工作结束时间")
    private Long planEndTime;

    /**
     * 无人机设备编号 即 当前工单授权的无人机设备编号
     * 可能允许多个设备接受工单
     * 如果为空，则为 可任意使用无人机
     */
    @ApiModelProperty(value = "无人机设备编号")
    private List<String> uavEquipmentNo;

    /**
     * 工作许可人 名字
     */
    @ApiModelProperty(value = "工作许可人")
    private String workPermitPeople;

    /**
     * 工作负责人 名字
     */
    @ApiModelProperty(value = "工作负责人")
    private String workerPeople;

    /**
     * 限制使用的无人机类型名 如 Phantom 4 RTK
     */
    @ApiModelProperty(value = "无人机类型名")
    private String uavCategory;

    /**
     * 作业性质
     */
    @ApiModelProperty(value = "作业性质")
    private String workTName;

    /**
     * 作业状态
     */
    @ApiModelProperty(value = "作业状态")
    private String inspecStatus;

    /**
     * 自主/非自主
     * 0: 自主
     * 1、非自主
     */
    @ApiModelProperty(value = "自主/非自主")
    private Integer autonomous;



    /**
     * 当前工单默认巡检方式
     */
    @ApiModelProperty(value = "当前工单默认巡检方式")
    private Integer inspectMode;

    /**
     * 当前工单巡检方式可选列表
     */
    @ApiModelProperty(value = "当前工单巡检方式可选列表")
    private List<String> inspectModeList;

    @ApiModelProperty(value = "配置的所有飞手的飞手名")
    private List<String> workers;

    @ApiModelProperty(value = "航线guid")
    private String routeGuid;

    @ApiModelProperty(value = "工单类型")
    private String orderType;

    @ApiModelProperty(value = "持票人")
    private String bearerGuid;
}
