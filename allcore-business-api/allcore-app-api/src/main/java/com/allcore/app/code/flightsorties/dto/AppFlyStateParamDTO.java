package com.allcore.app.code.flightsorties.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:在飞无人机信息
 * @author: wp
 * @date: 2022/4/25
 */
@Data
@ApiModel(value = "AppFlyStateParamDTO实体类",description = "无人机信息")
public class    AppFlyStateParamDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 无人机唯一识别码
     */
    @ApiModelProperty(value = "无人机唯一识别码")
    private String serialNumber;

    /**
     * 无人机型号  如：Phantom 4 RTK
     */
    @ApiModelProperty(value = "无人机型号")
    private String modelName;

    /**
     * 当前飞行信息：
     * 如 可以起飞(RTK)  正在飞行(RTK)
     * 注意：该字段长度会有很大差异 如果web端进行显示，注意控制显示宽度
     * 比如：恶劣环境时，会显示为： 风速很高,谨慎飞行并确保飞机保持在视线范围内 或 大风,谨慎飞行并确保飞机保持在视线范围内
     */
    @ApiModelProperty(value = "飞行信息",notes = "可以起飞(RTK)、正在飞行(RTK)")
    private String flyStr;
    /**
     * 飞行状态
     * 0 没飞、1 在飞、 2 飞完 3.飞航线  4.下载图 5.上传图
     */
    @ApiModelProperty(value = "飞行状态",notes = "0 没飞、1 在飞、 2 飞完 3.飞航线  4.下载图 5.上传图")
    private Integer flyState;

    /**
     * 经度
     */
    @ApiModelProperty(value = "无人机实时经度")
    private Double lo;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "无人机实时纬度")
    private Double la;

    /**
     * 海拔高度
     */
    @ApiModelProperty(value = "海拔高度")
    private Double al;
    /**
     * 返航点坐标 可能为0 即 无效值
     */
    @ApiModelProperty(value = "返航点经度")
    private Double hlo;

    /**
     * 返航点坐标 可能为0 即 无效值
     */
    @ApiModelProperty(value = "返航点纬度")
    private Double hla;

    /**
     * 返航点坐标 可能为0 即 无效值
     */
    @ApiModelProperty(value = "返航点高程")
    private Double hal;
    /**
     * gps卫星数量
     */
    @ApiModelProperty(value = "gps卫星数量")
    private Integer gpsCount;
    /**
     * hSpeed:当前飞机水平速度
     */
    @ApiModelProperty(value = "飞机水平速度")
    private Double hSpeed;

    /**
     * vSpeed:当前飞机垂直速度
     */
    @ApiModelProperty(value = "飞机垂直速度")
    private Double vSpeed;
    /**
     * 当前rtk是否已连接
     * -1 不支持 0 未打开开关 1 未连接 2 已连接使用
     */
    @ApiModelProperty(value = "rtk是否已连接",notes = "-1 不支持 0 未打开开关 1 未连接 2 已连接使用")
    private Integer rtkState;
    /**
     * 航向角 机遇正北方向的角度
     * 180 - -180
     */
    @ApiModelProperty(value = "航向角",notes = "机遇正北方向的角度180 - -180")
    private Double yaw;
    /**
     * 最近的架次 飞行的总距离
     */
    @ApiModelProperty(value = "当前架次飞行总距离")
    private Double distance;
    /**
     * 从最近一次起飞后，飞行的时长 （一个架次的总飞行时长）
     * 该字段不需要进行求和计算，始终为 最近起飞到当前的飞行时长
     * 单位：秒
     */
    @ApiModelProperty(value = "当前架次飞行总时长")
    private Long flySeconds;
}
