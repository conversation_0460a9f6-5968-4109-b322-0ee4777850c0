package com.allcore.app.code.flightsorties.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 消缺DTO
 *
 * <AUTHOR>
 * @date 2025/06/27 10:15
 **/
@Data
@ApiModel(value = "消缺DTO",description = "消缺DTO")
public class AppRemoveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
    @ApiModelProperty(value = "页码")
    private String pageNo;
    @ApiModelProperty(value = "单页数量")
    private String pageSize;
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    @ApiModelProperty(value = "任务名")
    private String inspectionTaskName;
    @ApiModelProperty(value = "任务id")
    private String inspectionTaskId;
    @ApiModelProperty(value = "任务状态")
    private String inspectionTaskStatus;
}
