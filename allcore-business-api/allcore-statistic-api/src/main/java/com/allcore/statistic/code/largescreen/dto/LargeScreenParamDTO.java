package com.allcore.statistic.code.largescreen.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 大屏展示访问参数
 *
 * <AUTHOR>
 * @date 2023/11/14 13:59
 **/
@Data
public class LargeScreenParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "部门code")
    private String deptCode;

    @ApiModelProperty(value = "年度")
    private String year;

    @ApiModelProperty(value = "飞行时长类型 1-标准时长;2-有效时长;")
    private String flyTimeType;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "任务id集合")
    private List<String> taskIdList;

}
