package com.allcore.statistic.code.largescreen.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 场站统计信息
 *
 * <AUTHOR>
 * @date 2023/11/14 15:25
 **/
@Data
@Builder
public class StationStatisticVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "场站id")
    private String stationId;

    @ApiModelProperty(value = "场站名称")
    private String stationName;

    @ApiModelProperty(value = "场站类型")
    private String stationType;

    @ApiModelProperty(value = "场站类型")
    private String stationTypeZh;

    @ApiModelProperty(value = "装机量")
    private String installedCapacity;

    @ApiModelProperty(value = "无人机总数")
    private String uavNum;

    @ApiModelProperty(value = "飞手总数")
    private String uavPlayerNum;
}
