package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
public class  CustomCommentVO<T> {
	private String option;
	private T detail;

	/**
	 * 返回简单的带操作和审核意见的实例
	 * @param option
	 * @param comment
	 * @return
	 */
	public static CustomCommentVO<?> simpleComment(String option, String comment){
		CustomCommentVO<SimpleComment> vo = new CustomCommentVO<>();
		vo.setOption(option);
		SimpleComment simpleComment = new SimpleComment();
		simpleComment.setComment(comment);
		vo.setDetail(simpleComment);
		return vo;
	}

	/**
	 * 返回简单的只有审核意见的实例
	 * @param comment
	 * @return
	 */
	public static CustomCommentVO<?> simpleComment(String comment){
		CustomCommentVO<Object> vo = new CustomCommentVO<>();
		vo.setOption(comment);
		return vo;
	}

	/**
	 * 带有详细信息的实例
	 * @param option
	 * @param detail
	 * @return
	 * @param <T>
	 */
	public static <T> CustomCommentVO<T> withDetail(String option, T detail){
		CustomCommentVO<T> vo = new CustomCommentVO<>();
		vo.setOption(option);
		vo.setDetail(detail);
		return vo;
	}

	@Data
	public static class SimpleComment {
		@ApiModelProperty(value = "审核意见")
		private String comment;
	}

	@Data
	public static class LossComment {
		@ApiModelProperty(value = "定损情况")
		private String lossDetermineSituation;
		@ApiModelProperty(value = "处理方式")
		private String solution;
		@ApiModelProperty(value = "维修意见")
		private String repairAdvice;
		@ApiModelProperty(value = "维修价格")
		private Double repairPrice;
	}

	@Data
	public static class LossCommentWithComment extends LossComment{
		@ApiModelProperty(value = "审批意见")
		private String comment;
	}

	@Data
	public static class MaintainComment {
		@ApiModelProperty(value = "定损情况")
		private String lossDetermineSituation;
		@ApiModelProperty(value = "处理方式")
		private String solution;
		@ApiModelProperty(value = "维修价格")
		private Double repairPrice;
		@ApiModelProperty(value = "是否维修")
		private String isRepair;
		@ApiModelProperty(value = "处理方式")
		private String maintainMethod;
		@ApiModelProperty(value = "维保结果")
		private String resultDetail;
		@ApiModelProperty(value = "寄送方式")
		private String resultSendMethod;
		@ApiModelProperty(value = "寄送清单")
		private String resultSendList;
		@ApiModelProperty(value = "交接人/物流公司")
		private String resultSendBy;
		@ApiModelProperty(value = "交接人联系方式/快递单号")
		private String resultSendNumber;
	}

	@Data
	public static class AcceptComment{

		@ApiModelProperty(value = "验收状态")
		private String checkStatus;

		@ApiModelProperty(value = "异常描述")
		private String checkProblemDisc;

		@ApiModelProperty(value = "寄送方式")
		private String checkReturnMethod;

		@ApiModelProperty(value = "交接人/物流公司")
		private String checkReturnBy;

		@ApiModelProperty(value = "交接人联系方式/快递单号")
		private String checkReturnNumber;
	}

	@Data
	public static class SpareSendComment{

		@ApiModelProperty(value = "备用机型号")
		private String spareModel;

		@ApiModelProperty(value = "备用机名称")
		private String spareName;

		@ApiModelProperty(value = "提供备用机方式")
		private String spareMethod;

		@ApiModelProperty(value = "提供备用机对接人/物流公司")
		private String spareBy;

		@ApiModelProperty(value = "提供备用机对接人号码/物流单号")
		private String spareNumber;

	}



}
