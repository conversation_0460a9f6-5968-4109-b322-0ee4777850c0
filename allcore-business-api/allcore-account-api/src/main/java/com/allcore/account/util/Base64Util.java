package com.allcore.account.util;

import java.io.UnsupportedEncodingException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2022-08-29 09:50:44
 */
public class Base64Util {
	public static final Base64.Decoder DECODER = Base64.getDecoder();
	public static final Base64.Encoder ENCODER = Base64.getEncoder();

	public static String encode(byte[] data) {
		return ENCODER.encodeToString(data);
	}

	public static String decode(String data)
			throws UnsupportedEncodingException {

		return new String(DECODER.decode(data), "UTF-8");
	}
}
