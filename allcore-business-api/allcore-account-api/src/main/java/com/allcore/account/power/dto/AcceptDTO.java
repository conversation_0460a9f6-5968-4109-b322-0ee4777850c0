package com.allcore.account.power.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AcceptDTO", description = "验收DTO")
public class AcceptDTO {

	@ApiModelProperty(value = "是否通过")
	private String pass;

	@ApiModelProperty(value = "维保GUID")
	private String maintainGuid;

	@ApiModelProperty(value = "验收状态")
	private String checkStatus;

	@ApiModelProperty(value = "异常描述")
	private String checkProblemDisc;

	@ApiModelProperty(value = "验收人")
	private String checkPeople;

	@ApiModelProperty(value = "验收人联系方式")
	private String checkPeoplePhone;

	@ApiModelProperty(value = "寄送方式")
	private String checkReturnMethod;

	@ApiModelProperty(value = "交接人/物流公司")
	private String checkReturnBy;

	@ApiModelProperty(value = "交接人联系方式/快递单号")
	private String checkReturnNumber;

}
