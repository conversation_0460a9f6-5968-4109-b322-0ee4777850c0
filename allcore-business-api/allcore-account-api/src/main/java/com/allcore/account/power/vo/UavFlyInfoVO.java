package com.allcore.account.power.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 飞手操作人员表
 *
 * <AUTHOR>
 * @Date 2022/04/19 16:13
 **/
@Data
@ApiModel(value = "无人机飞手", description = "无人机飞手")
public class UavFlyInfoVO {

	/**
	 * 飞手Guid
	 */
	@ApiModelProperty(value = "飞手Guid")
	private String operatorGuid;

	/**
	 * 是否飞手
	 */
	@ApiModelProperty(value = "是否飞手")
	private String flyer;

	/**
	 * 单位名称
	 */
	@ApiModelProperty(value = "单位名称")
	private String unitName;

	/**
	 * 机构id
	 */
	@ApiModelProperty(value = "机构id")
	private String deptId;

	/**
	 * 人员性质
	 */
	@ApiModelProperty(value = "人员性质")
	private String operatorNature;

	/**
	 * 人员名称
	 */
	@ApiModelProperty(value = "人员名称")
	private String operatorName;

	/**
	 * 人员性别
	 */
	@ApiModelProperty(value = "人员性别")
	private String operatorSex;

	/**
	 * 人员联系方式手机号码
	 */
	@ApiModelProperty(value = "人员联系方式手机号码")
	private String operatorTel;

	/**
	 * 是否外委人员：0否，1是
	 */
	@ApiModelProperty(value = "是否外委人员：no否，yes是")
	private String isOutEntrust;

	/**
	 * 是否外包
	 */
	@ApiModelProperty(value = "是否外包人员：no否，yes是")
	private String outSource;

	/**
	 * 开始工作时间
	 */
	@DateTimeFormat(
		pattern = "yyyy"
	)
	@JsonFormat(
		pattern = "yyyy"
	)
	@ApiModelProperty(value = "开始工作时间")
	private String startWorkTime;

	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String operatorUsername;

	@ApiModelProperty(value = "从事无人机巡检年限")
	private Integer uavFlyYear;

	@ApiModelProperty(value = "身份")
	@JsonIgnore
	private String identity;

}

