package com.allcore.account.power.vo;

import com.allcore.account.power.entity.GeoPoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Jiy
 * @Date: 2022-10-12 17:36
 */
@Data
public class TowerDataVO {
	@ApiModelProperty(value = "杆塔guid")
	private String towerGuid;

	@ApiModelProperty(value = "杆塔名称")
	private String towerName;

	@ApiModelProperty(value = "所属线路guid")
	private String towerLineGuid;

	@ApiModelProperty(value = "电压等级")
	private String voltageLevel;

	@ApiModelProperty(value = "坐标")
	private GeoPoint coordinate;

	@ApiModelProperty(value = "杆塔坐标经度")
	private String towerLongitude;

	@ApiModelProperty(value = "杆塔坐标纬度")
	private String towerLatitude;

	@ApiModelProperty(value = "海拔高度")
	private Double altitude;

	@ApiModelProperty(value = "杆塔排序号")
	private Integer towerSort;

	@ApiModelProperty(value = "专业类型")
	private String professionalType;

	@ApiModelProperty(value = "经度")
	private Double longitude;

	@ApiModelProperty(value = "纬度")
	private Double latitude;

	@ApiModelProperty(value = "线路名称")
	private String lineName;

	@ApiModelProperty(value = "电压等级名称")
	private String voltageName;
}
