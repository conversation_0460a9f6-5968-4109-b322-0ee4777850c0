package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2022/9/20 13:50
 * @description:
 */
@ApiModel("外包无人机电池概况")
@Data
public class UavBatterySurveyVO {

	@ApiModelProperty("电池id")
	private String id;

	@ApiModelProperty("运维单位")
	private String operationUnit;

	@ApiModelProperty("运维中心")
	private String operationCentre;

	@ApiModelProperty("运维班组")
	private String operationTeam;

	/**
	 * SN码
	 */
	@ApiModelProperty("SN码")
	private String snCode;

	/**
	 * 部门code
	 */
	@ApiModelProperty("部门code")
	private String deptCode;

	/**
	 * 部门id
	 */
	@ApiModelProperty("部门id")
	private String deptId;

	/**
	 * 电池逻辑编码
	 */
	@ApiModelProperty("电池逻辑编码")
	private String logicCoder;

	/**
	 * 飞行器类型
	 */
	@ApiModelProperty("飞行器类型")
	private String uavType;

	/**
	 * 飞行器型号
	 */
	@ApiModelProperty("飞行器型号")
	private String modelGuid;

	@ApiModelProperty("飞行器型号name")
	private String modelName;

	/**
	 * 电池品牌
	 */
	@ApiModelProperty("电池品牌")
	private String batteryBrand;

	/**
	 * 生产日期
	 */
	@ApiModelProperty("生产日期")
	private String productionDate;

	@ApiModelProperty("满电数量")
	private Integer fullPower;

	@ApiModelProperty("充电中数量")
	private Integer charging;

	@ApiModelProperty("放电中数量")
	private Integer inDischarge;

	@ApiModelProperty("不在库数量")
	private Integer notLibrary;

	@ApiModelProperty("总数量")
	private Integer total;
}
