package com.allcore.account.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("account_subsection_topology")
@ApiModel(value="SubsectionTopology对象", description="")
public class SubsectionTopologyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "线路分段guid_下标")
    private String subsectionIndex;

    @ApiModelProperty(value = "杆塔guid")
    private String towerGuid;

    @ApiModelProperty(value = "杆塔psrid")
    private String towerPsrId;

    @ApiModelProperty(value = "杆塔名称")
    private String towerName;

    @ApiModelProperty(value = "线路guid")
    private String lineGuid;

    @ApiModelProperty(value = "上一基杆塔psrid")
    private String preTowerGuid;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "高程")
    private BigDecimal elevation;

    @ApiModelProperty(value = "电压等级")
    private String voltageLevel;

    @ApiModelProperty(value = "单位code")
    private String deptCode;


}
