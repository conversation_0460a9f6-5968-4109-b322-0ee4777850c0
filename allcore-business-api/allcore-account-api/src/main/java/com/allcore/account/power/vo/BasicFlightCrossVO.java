package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 飞行交跨表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Data
public class BasicFlightCrossVO implements Serializable {

    private static final long serialVersionUID = 1L;


	private String id;

    @ApiModelProperty(value = "飞行交跨guid")
    private String flightCrossGuid;

    @ApiModelProperty(value = "电压等级")
    private String voltageLevel;

    @ApiModelProperty(value = "跨越段线路名称(线路guid)")
    private String crossSectionLineGuid;

    @ApiModelProperty(value = "跨越杆塔区间")
    private String crossLineArea;

	@ApiModelProperty(value = "跨越杆塔区间结尾")
	private String crossLineAreaEnd;

	@ApiModelProperty(value = "跨越杆塔guid")
	private String crossTowerGuid;

	@ApiModelProperty(value = "跨越杆塔guid结尾")
	private String crossTowerGuidEnd;

    @ApiModelProperty(value = "跨越点高度(m)")
    private String crossHeight;

    @ApiModelProperty(value = "跨越点经度")
    private String crossLongitude;

    @ApiModelProperty(value = "跨越点纬度")
    private String crossLatitude;

    @ApiModelProperty(value = "跨越类型")
    private String crossType;

    @ApiModelProperty(value = "交叉角度")
    private Double crossAngle;

    @ApiModelProperty(value = "跨越水平距离(m)")
    private String crossHorizontalDistance;

    @ApiModelProperty(value = "跨越垂直距离(m)")
    private String crossVerticalDistance;

    @ApiModelProperty(value = "跨越线路名称(线路guid)")
    private String crossLineGuid;

    @ApiModelProperty(value = "单位GUID")
    private String unitGuid;

    @ApiModelProperty(value = "单位级别（1：国网公司，2：省级，3：市区县级，4工区，5：班组）")
    private Integer unitLevel;

    @ApiModelProperty(value = "配电 dms 输电 tms")
    private String professionalType;

	@ApiModelProperty(value = "跨越段线路名称")
	private String crossSectionLineName;

	@ApiModelProperty(value = "跨越线路名称")
	private String crossLineName;

	@ApiModelProperty(value = "跨越类型")
	private String crossTypeName;

	@ApiModelProperty(value = "运维单位")
	private String unitName;

	@ApiModelProperty(value = "中心名称")
	private String workAreaName;

	@ApiModelProperty(value = "班组名称")
	private String workGroupName;

	@ApiModelProperty(value = "电压等级")
	private String voltageLevelName;

	private String towerGuid;

	private Date createTime;


}
