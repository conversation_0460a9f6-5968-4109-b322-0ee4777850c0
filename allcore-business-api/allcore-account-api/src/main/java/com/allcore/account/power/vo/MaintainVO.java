package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MaintainVO", description = "维保VO")
public class MaintainVO {

	@ApiModelProperty(value = "维保GUID")
	private String maintainGuid;

	@ApiModelProperty(value = "单位Id")
	private String deptId;

	@ApiModelProperty(value = "单位Code")
	private String deptCode;

	@ApiModelProperty(value = "产品GUID")
	private String productGuid;

	@ApiModelProperty(value = "无人机类别")
	private String uavType;
	@ApiModelProperty(value = "无人机品牌")
	private String uavBrand;
	@ApiModelProperty(value = "产品型号")
	private String productModel;
	@ApiModelProperty(value = "产品序列号")
	private String productSN;

	@ApiModelProperty(value = "是否有保险")
	private String hasInsurance;

	@ApiModelProperty(value = "保险列表")
	private List<MaintainInsuranceVO> insurances;

	@ApiModelProperty(value = "维保事宜联系人")
	private String contactPersonName;

	@ApiModelProperty(value = "联系人联系方式")
	private String contactPersonPhone;

	@ApiModelProperty(value = "服务类型")
	private String serviceType;

	@ApiModelProperty(value = "硬件问题")
	private List<String> hardwareProblem;

	@ApiModelProperty(value = "软件问题")
	private List<String> softwareProblem;

	@ApiModelProperty(value = "故障描述")
	private String problemDisc;

	@ApiModelProperty(value = "部件列表")
	private List<MaintainComponentVO> components;

	@ApiModelProperty(value = "寄送方式")
	private String sendMethod;

	@ApiModelProperty(value = "交接人/物流公司")
	private String sendBy;

	@ApiModelProperty(value = "交接人联系方式/快递单号")
	private String sendNumber;

	@ApiModelProperty(value = "是否申请备用机")
	private String isApplySpare;

	@ApiModelProperty(value = "备用机状态")
	private String spareStatus;

	@ApiModelProperty(value = "备用机型号GUID")
	private String spareModelGuid;
}
