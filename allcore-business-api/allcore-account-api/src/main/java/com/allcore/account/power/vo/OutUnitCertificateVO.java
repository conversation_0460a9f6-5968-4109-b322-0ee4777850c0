package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外包单位证书VO
 *
 * <AUTHOR>
 * @date 2022/09/06 21:23
 **/
@Data
@ApiModel(value = "单位证书VO", description = "单位证书VO")
public class OutUnitCertificateVO {

	/**
	 * 证书guid
	 */
	@ApiModelProperty(value = "证书guid")
	private String certificateGuid;

	/**
	 * 外包单位guid
	 */
	@ApiModelProperty(value = "外包单位guid")
	private String outUnitGuid;

	/**
	 * 证书编号
	 */
	@ApiModelProperty(value = "证书编号")
	private String cerNum;

	/**
	 * 证书名称
	 */
	@ApiModelProperty(value = "证书名称")
	private String cerName;


	/**
	 * 营业执照文件ID
	 */
	@ApiModelProperty(value = "营业执照文件ID")
	private String licenseFileGuid;

	/**
	 * 营业执照文件链接
	 */
	@ApiModelProperty(value = "营业执照文件链接")
	private String licenseFileLink;

	/**
	 * 有效期
	 */
	private String validity;

}
