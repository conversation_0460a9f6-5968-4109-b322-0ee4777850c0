package com.allcore.account.power.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/26 14:05
 */
@Data
@TableName("account_line_cross_info")
public class LineCrossInfoEntity extends ZxhcEntity implements Serializable {

	@ApiModelProperty(value = "线路交跨GUID")
	private String lineCrossGuid;
	@ApiModelProperty(value = "线路GUID")
	private String lineGuid;
	@ApiModelProperty(value = "所属单位ID")
	private String lineUnitId;
	@ApiModelProperty(value = "小号侧杆塔GUID")
	private String minTowerGuid;
	@ApiModelProperty(value = "大号侧杆塔GUID")
	private String maxTowerGuid;
	@ApiModelProperty(value = "线路投运时间")
	private String lineOperationTime;
	@ApiModelProperty(value = "跨越段建设时间")
	private String crossBuildTime;
	@ApiModelProperty(value = "跨越点地理位置所属区")
	private Integer crossPointLocation;
	@ApiModelProperty(value = "交跨类型")
	private String crossType;
	@ApiModelProperty(value = "跨越物名称")
	private String crossName;
	@ApiModelProperty(value = "距跨越物边基础水平距离")
	private Double crossHorizontalDistance;
	@ApiModelProperty(value = "垂直跨越距离")
	private Double crossVerticalDistance;
	@ApiModelProperty(value = "交叉角度")
	private Double crossAngle;
	@ApiModelProperty(value = "是否独立耐张段")
	private String isAloneStrain;
	@ApiModelProperty(value = "耐张段杆塔号")
	private String strainTowerNo;
	@ApiModelProperty(value = "耐张段直线塔数量")
	private Integer strainTowerNum;
	@ApiModelProperty(value = "跨越耐张段长度")
	private Double crossStrainLength;
	@ApiModelProperty(value = "耐张段内杆塔型号")
	private String strainTowerType;
	@ApiModelProperty(value = "导地线材料")
	private String groundWireMaterial;
	@ApiModelProperty(value = "导地线是否双挂点")
	private String isGroundWireDouble;
	@ApiModelProperty(value = "导地线有无光缆")
	private String isGroundWireCable;
	@ApiModelProperty(value = "导地线有无接口")
	private String isGroundWireContact;
	@ApiModelProperty(value = "是否有相间间隔棒")
	private String haveSpacerBar;
	@ApiModelProperty(value = "两侧绝缘子串是否双挂点")
	private String isInsulatorDouble;
	@ApiModelProperty(value = "跨越塔两侧档距之比不超过2:1")
	private String isNotProportion;
	@ApiModelProperty(value = "杆塔是否采用2011版通用版")
	private String isTowerCurren;
	@ApiModelProperty(value = "监控装置是否加装")
	private String isMonitorInstall;
	@ApiModelProperty(value = "附引流是否加装")
	private String isDrainageInstall;
	@ApiModelProperty(value = "耐张线夹X光是否检测")
	private String isStrainClampCheck;
	@ApiModelProperty(value = "耐张线夹X光是否存在缺陷")
	private String isStrainClampDefect;
	@ApiModelProperty(value = "校核结果是否合格(合格/不合格)")
	private String checkResult;
	@ApiModelProperty(value = "状态评价结果(正常/注意/严重/危急)")
	private String evaluationResult;

	private String professionalType;


}
