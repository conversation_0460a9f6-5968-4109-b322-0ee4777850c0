package com.allcore.account.power.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-11 09:39:16
 */
@Data
@TableName("account_wait_maintain_ledger")
public class WaitMaintainEntity extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "代维guid")
	private String waitMaintainGuid;

	@ApiModelProperty(value = "线路guid")
	private String lineGuid;

	@ApiModelProperty(value = "线路名称")
	private String lineName;

	@ApiModelProperty(value = "单位guid")
	private String deptGuid;

	@ApiModelProperty(value = "单位名称")
	private String deptName;

}
