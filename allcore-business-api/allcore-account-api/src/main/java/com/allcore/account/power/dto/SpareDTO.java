package com.allcore.account.power.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SpareDTO", description = "备用机DTO")
public class SpareDTO {

	@ApiModelProperty(value = "维保GUID")
	private String maintainGuid;

	@ApiModelProperty(value = "备用机GUID")
	private String spareUavGuid;

	@ApiModelProperty(value = "备用机GUID")
	private String spareModel;

	@ApiModelProperty(value = "提供备用机方式")
	private String spareMethod;

	@ApiModelProperty(value = "提供备用机对接人/物流公司")
	private String spareBy;

	@ApiModelProperty(value = "提供备用机对接人号码/物流单号")
	private String spareNumber;

	@ApiModelProperty(value = "是否提交")
	private String submit;

}
