package com.allcore.account.power.feign;

import com.allcore.account.power.dto.AppUavPlaneFlyDTO;
import com.allcore.account.power.dto.AppUavPlaneInfoDTO;
import com.allcore.account.power.dto.QjUavPlaneDto;
import com.allcore.account.power.dto.UavBatteryLoopTimesUpdateDTO;
import com.allcore.account.power.entity.UavPlaneInfoEntity;
import com.allcore.account.power.vo.QjUavPlaneVO;
import com.allcore.account.power.vo.UavModelInfoVO;
import com.allcore.account.power.vo.UavModelListVO;
import com.allcore.account.power.vo.UavPlaneInfoVO;
import com.allcore.common.base.FgPage;
import com.allcore.core.tool.api.R;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @author: ldh
 * @date: 2022/9/13 10:39
 * @description: 无人机feign接口失败返回
 */
@Component
public class UavClientFallBack implements IUavClient {

	@Override
	public R<List<UavPlaneInfoEntity>> getAllPlaneInfoByDept() {
		return R.fail("查询到无人机列表信息失败");
	}

	@Override
	public R<UavModelListVO> findListByUavTypeAndUavBrand(String deptCode, String uavType, String uavBrand) {
		return R.fail("根据无人机类型,品牌查询型号列表失败");
	}

	@Override
	public R<UavPlaneInfoVO> getPlaneDetail(String planeGuid) {
		return R.fail("查询无人机详情失败");
	}

	@Override
	public R<List<UavModelInfoVO>> getInfoByModelGuids(List<String> modelGuids) {
		return R.fail("查询无人机库详情列表失败");
	}

	@Override
	public R<List<UavPlaneInfoVO>> getPlaneListByGuids(List<String> planeGuids) {
		return R.fail("根据多个无人机guid查询无人机列表失败");
	}

	@Override
	public R<UavPlaneInfoEntity> getPlaneInfoBySnCode(String snCode) {
		return R.fail("未查询到无人机信息");
	}

	@Override
	public R updateLoopTimes(UavBatteryLoopTimesUpdateDTO infoDTO) {
		return R.fail("更新失败！");
	}

	@Override
	public R saveAppUavInfo(AppUavPlaneInfoDTO uavPlaneInfoDTO) {
		return R.fail("保存失败！");
	}

	@Override
	public R<UavPlaneInfoEntity> saveAppUavFly(AppUavPlaneFlyDTO uavPlaneFlyDTO) {
		return R.fail("保存失败！");
	}

	@Override
	public R<FgPage<QjUavPlaneVO>> getPlanePageList(QjUavPlaneDto dto) {
		return R.fail("未查询到无人机信息");
	}

	@Override
	public R<UavPlaneInfoEntity> updatePlaneName(String sn, String name) {
		return R.fail("修改失败");
	}
}
