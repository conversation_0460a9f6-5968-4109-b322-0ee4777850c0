package com.allcore.account.power.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 巡检航线库(RouteLineInfo)实体类
 *
 * <AUTHOR>
 * @since 2022-08-29 09:45:18
 */
@Data
public class DeviceRouteClientVo implements Serializable {
    private static final long serialVersionUID = -51414644534514944L;
	/**
	 * 航线guid
	 */
	private String routeInfoGuid;
	/**
	 * 设备识别码
	 */
	private String towerGuid;
	/**
	 * 设备名称
	 */
	private String towerName;
	/**
	 * 航线名称
	 */
	private String routeName;


}

