package com.allcore.account.power.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2022/9/19 18:54
 * @description: 业务字典code枚举类
 */
public enum DictBizCodeEnum {
	INSPECT_MODE("inspect_mode", "巡检方式"),
	VOLTAGE_LEVEL("010401", "电压等级代码"),
	UAV_BRAND("uav_brand", "无人机品牌"),
	UAV_TYPE("uav_type", "无人机类型"),
	IS_RTK("is_rtk", "是否有RTK功能"),
	UAV_PHOTO_TYPE("uav_photo_type", "无人机相机传感器类型"),
	UAV_CAMERA_MODEL("uav_camera_model", "无人机相机型号"),
	UAV_ASSET_ATTRIBUTES("uav_asset_attributes", "无人机资产属性"),
	UAV_STATE("uav_state", "无人机状态"),
	SPARE_PART_TYPE("spare_part_type", "备品备件类型"),
	UAV_FACTORY("uav_factory", "无人机制造厂商"),
	DEVICE_SOURCE("device_source", "设备来源"),
	OPERATOR_IDENTITY("operator_identity", "人员身份"),
	OPERATOR_NATURE("operator_nature", "人员性质"),
	spare_part_manufacturer("spare_part_manufacturer", "备品备件厂家");

	private String code;
	private String name;

	DictBizCodeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public static String getNameByCode(String code) {
		String name = null;
		DictBizCodeEnum[] types = DictBizCodeEnum.values();
		for (DictBizCodeEnum codeEnum : types) {
			if (codeEnum.code.equals(code)) {
				name = codeEnum.name;
				break;
			}
		}
		return name;
	}

	public static List<Map<String, String>> getEnumList() {
		List<Map<String, String>> list = new ArrayList<>();
		DictBizCodeEnum[] types = DictBizCodeEnum.values();
		for (DictBizCodeEnum codeEnum : types) {
			Map<String, String> map = new HashMap<>();
			map.put("code", codeEnum.code);
			map.put("name", codeEnum.name);
			list.add(map);
		}

		return list;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
