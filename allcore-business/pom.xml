<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>allcore-product-biz</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-business</artifactId>
    <name>${project.artifactId}</name>
    <version>PRODUCT.1.0.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>众芯汉创公司级产品底座 业务中台集合</description>

    <modules>
        <!--        <module>allcore-netty</module>-->
        <!--        <module>allcore-finger</module>-->
        <!--        <module>allcore-account</module>-->
        <!--        <module>allcore-portal</module>-->
        <!--        <module>allcore-platform</module>-->
        <!--        <module>allcore-isc</module>-->
        <module>allcore-main</module>
        <module>allcore-app</module>
        <module>allcore-statistic</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-common</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-api-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-file-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-tool</artifactId>
        </dependency>
    </dependencies>

</project>