package com.allcore.app.config;

import java.util.concurrent.CopyOnWriteArraySet;

import javax.annotation.PostConstruct;

import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

@Component
public class WebSocketManager {

    private ThreadPoolTaskScheduler taskScheduler;

    private Long onlineCount;

    private CopyOnWriteArraySet<String> onlines;

    private static final Integer POOL_MIN = 10;

    @PostConstruct
    public void init() {
        taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(POOL_MIN);
        taskScheduler.initialize();
        this.onlines = new CopyOnWriteArraySet<>();
        this.onlineCount = 0L;
    }

    public boolean isOnline(String username) {
        return onlines.contains(username);
    }

    public void addUser(String username) {
        onlines.add(username);
        onlineCount = Long.valueOf(onlines.size());
    }

	public CopyOnWriteArraySet<String> getAllUser(){
		return onlines;
	}

    public void deleteUser(String username) {
        onlines.remove(username);
        onlineCount = Long.valueOf(onlines.size());
    }
}
