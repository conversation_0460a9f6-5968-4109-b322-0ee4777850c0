package com.allcore.app.code.flyApp.controller;

import com.allcore.app.code.flightsorties.vo.AppPlaneMediaUrlResponseVO;
import com.allcore.app.code.flyApp.service.IAppMainService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.FolderUpLoadDTO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @description: APP主业务对接接口
 * @author: wp
 * @date: 2022/3/14
 */
@RestController
@RequestMapping("/android/main")
@Slf4j
@Api(value = "APP主业务对接模块",tags = "APP主业务对接模块")
public class AppMainController {

    @Resource
    private IAppMainService appMainService;

    /**
     *  接收移动端视频回传地址
     */
    @PostMapping("/streamMediaUrl")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "接收移动端视频回传地址", notes = "接收移动端视频回传地址")
    public R<AppPlaneMediaUrlResponseVO> pushMediaUrl(@ApiParam(value = "监控模块编号") @RequestParam(value = "param") String param) {
        return appMainService.pushMediaUrl(param);
    }
    /**
     *  接收移动端在飞无人机实时信息
     * @param param
     * @return
     */
    @PostMapping(value = "/appReceiveAircraft", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "接收移动端在飞无人机实时信息", notes = "接收移动端在飞无人机实时信息")
    public R appReceiveUavDataNew(@RequestParam(value = "param") String param) {
        return appMainService.receiveUavDataNew(true,param);
    }


    /**
     *  接收第三方移动端在飞无人机实时信息
     * @param param
     * @return
     */
    @PostMapping(value = "/thirdPartyAppReceiveAircraft", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "接收第三方移动端在飞无人机实时信息", notes = "接收第三方移动端在飞无人机实时信息")
    public R thirdPartyAppReceiveUavData(@RequestParam(value = "param") String param) {
        return appMainService.receiveUavDataNew(false,param);
    }


    /**
     *  查询巡检工单信息
     *  pageNo 第几页
     *  pageSize 每页多少条
     * @param param
     * @return
     */
    @PostMapping(value = "/orderList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询巡检工单信息", notes = "查询巡检工单信息")
    public R queryWorkOrderList(@RequestParam(value = "param") String param) {
        return appMainService.queryWorkOrderList(param);
    }

    /**
     *  查询巡检工单设备详情
     * @param param
     * @return
     */
    @PostMapping(value = "/orderDetails", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "查询巡检工单设备详情", notes = "查询巡检工单设备详情")
    public R queryWorkOrderDetails(@RequestParam(value = "param") String param) {
        return appMainService.queryWorkOrderDetails(param);
    }


    /**
     *  工单执行状态同步
     * @param param
     * @return
     */
    @PostMapping(value = "/updateInspectStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "工单执行状态同步", notes = "工单执行状态同步")
    @ApiIgnore
    public R updateInspectStatus(@RequestParam(value = "param") String param) {
        return appMainService.updateInspectStatus(param);
    }



    /**
     * 保存巡检图片信息
     * @param param json字符串
     * @param file 上传的图片
     * @return
     */
    @PostMapping(value = "/appReceivePic", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "保存巡检图片信息", notes = "保存巡检图片信息")
    public R appReceivePic(
            @RequestParam(value = "param") String param,
            @RequestPart("file") MultipartFile file) {
        return appMainService.appReceivePic(param,file);
    }
    /**
     * 保存风机巡检图片信息
     * @param param json字符串
     * @param file 上传的图片
     * @return
     */
    @PostMapping(value = "/appReceiveFanPic", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "保存风机巡检图片信息", notes = "保存风机巡检图片信息")
    public R appReceiveFanPic(
            @RequestParam(value = "param") String param,
            @RequestPart("file") MultipartFile file) {
        return appMainService.appReceiveFanPic(param,file);
    }
    /**
     * 文件夹上传
     */
    @PostMapping("/folderUpload")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "巡检图片文件夹上传", notes = "文件夹上传")
    public R folderUpload(@RequestParam(value = "param") String param) {
        return appMainService.folderUpload(param);
    }
    /**
     *  获取空域信息
     * @param param
     * @return
     */
    @PostMapping(value = "/flyAirSpace", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "获取空域信息", notes = "获取空域信息")
    @ApiIgnore
    public R flyAirSpace(@RequestParam(value = "param") String param) {
        return appMainService.flyAirSpace(param);
    }

    /**
     *  下载航线文件
     * @param param
     * @return
     */
    @PostMapping(value = "/deviceProtocolNew", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "下载航线文件", notes = "下载航线文件")
    public R deviceProtocol(@RequestParam(value = "param") String param, HttpServletRequest httpServletRequest) {
        return appMainService.deviceProtocol(param,httpServletRequest);
    }

    /**
     *  获取设备台账信息
     * @param param
     * @return
     */
    @PostMapping(value = "/groupInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "获取设备台账信息", notes = "获取设备台账信息")
    public R groupInfoList(@RequestParam(value = "param") String param) {
        return appMainService.groupInfoList(param);
    }

    /**
     *  获取设备台账详细信息
     * @param param
     * @return
     */
    @PostMapping(value = "/deviceInfoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "获取设备台账详细信息", notes = "获取设备台账详细信息")
    public R deviceInfoList(@RequestParam(value = "param") String param) {
        return appMainService.deviceInfoList(param);
    }



    /**
     * 上传精细学习数据 暂无业务流程
     * @param param
     * @return
     */
    @PostMapping("/upgradeProtocol")
    public R upgradeProtocol(@RequestParam String param){
        return appMainService.upgradeProtocol(param);
    }


    /**
     * 获取当前设备精细学习模板 暂无业务流程
     * @return
     */
    @PostMapping("/ledgerDeviceModel")
    public R ledgerDeviceModel(){
        return appMainService.ledgerDeviceModel();
    }

    /**
     * 获取当前工单杆塔下精细学习模板 暂无业务流程
     * @return
     */
    @PostMapping("/orderDeviceModel")
    public R orderDeviceModel(@RequestParam String param){
        return appMainService.orderDeviceModel(param);
    }

}
