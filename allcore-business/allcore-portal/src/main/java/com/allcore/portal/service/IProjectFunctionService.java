package com.allcore.portal.service;

import com.allcore.portal.entity.ProjectFunction;
import com.allcore.portal.vo.ProjectFunctionVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface IProjectFunctionService extends IService<ProjectFunction> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param projectFunction
	 * @return
	 */
	IPage<ProjectFunctionVO> selectProjectFunctionPage(IPage<ProjectFunctionVO> page, ProjectFunctionVO projectFunction);

}
