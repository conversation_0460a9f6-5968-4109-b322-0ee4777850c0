package com.allcore.portal.controller;

import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.portal.entity.ProjectFunction;
import com.allcore.portal.service.IProjectFunctionService;
import com.allcore.portal.vo.ProjectFunctionVO;
import com.allcore.portal.wrapper.ProjectFunctionWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/projectfunction")
@Api(value = "项目功能项", tags = "接口")
public class ProjectFunctionController extends AllcoreController {

	private final IProjectFunctionService projectFunctionService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入projectFunction")
	public R<ProjectFunctionVO> detail(ProjectFunction projectFunction) {
		ProjectFunction detail = projectFunctionService.getOne(Condition.getQueryWrapper(projectFunction));
		return R.data(ProjectFunctionWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入projectFunction")
	public R<IPage<ProjectFunctionVO>> list(ProjectFunction projectFunction, Query query) {
		IPage<ProjectFunction> pages = projectFunctionService.page(Condition.getPage(query), Condition.getQueryWrapper(projectFunction));
		return R.data(ProjectFunctionWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入projectFunction")
	public R<IPage<ProjectFunctionVO>> page(ProjectFunctionVO projectFunction, Query query) {
		IPage<ProjectFunctionVO> pages = projectFunctionService.selectProjectFunctionPage(Condition.getPage(query), projectFunction);
		return R.data(pages);
	}

	/**
	 * 新增 
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入projectFunction")
	public R save(@Valid @RequestBody ProjectFunction projectFunction) {
		return R.status(projectFunctionService.save(projectFunction));
	}

	/**
	 * 修改 
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入projectFunction")
	public R update(@Valid @RequestBody ProjectFunction projectFunction) {
		return R.status(projectFunctionService.updateById(projectFunction));
	}

	/**
	 * 新增或修改 
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入projectFunction")
	public R submit(@Valid @RequestBody ProjectFunction projectFunction) {
		return R.status(projectFunctionService.saveOrUpdate(projectFunction));
	}

	
	/**
	 * 删除 
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(projectFunctionService.removeBatchByIds(Func.toStrList(ids)));
	}

	
}
