<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>allcore-business</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-main</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <poi.version>4.1.2</poi.version>
        <easypoi.version>4.1.2</easypoi.version>
        <commons.httpclient.version>3.1</commons.httpclient.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>${commons.httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-swagger</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.ulisesbocchio</groupId>-->
<!--            <artifactId>jasypt-spring-boot</artifactId>-->
<!--            <version>2.1.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.6</version>  <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-common</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-main-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-system-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${easypoi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.3</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.9.1</version>
        </dependency>

        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.doc.free</artifactId>
            <version>3.9.0</version>
        </dependency>

        <!--压缩zip-->
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>1.3.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-external-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-app-api</artifactId>
            <version>PRODUCT.1.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.allcore.product</groupId>
            <artifactId>allcore-starter-algorithm</artifactId>
            <version>1.0.0.VERSION</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm7JdbcDriver18</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.26.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <username>${docker.username}</username>
                    <password>${docker.password}</password>
                    <repository>${docker.registry.url}/${docker.namespace}/${project.artifactId}</repository>
                    <tag>${docker.version}</tag>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.42.0</version>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <dockerHost>${docker.host}</dockerHost>
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${docker.username}</username>
                        <password>${docker.password}</password>
                    </authConfig>
                    <!-- harbor镜像仓库地址-->
                    <pushRegistry>http://${docker.registry.url}</pushRegistry>
                    <images>
                        <image>
                            <!--推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${docker.registry.url}/${docker.namespace}/${project.artifactId}:${docker.version}
                            </name>
                            <!--定义镜像构建行为-->

                            <build>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                            </build>
                        </image>
                    </images>
                </configuration>

                <executions>
                    <execution>
                        <id>docker-exec</id>
                        <!-- 绑定mvn install阶段，当执行mvn install时 就会执行docker build 和docker push-->
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>

    </build>
</project>