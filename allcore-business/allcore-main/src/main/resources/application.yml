#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.allcore.**.entity

#swagger扫描路径配置
swagger:
  base-packages:
    - com.allcore

#配置文件ENC加密
#jasypt:
#  encryptor:
#    bean: allcoreStringEncryptor


feign:
  client:
    config:
      default:
        loggerLevel: FULL

logging:
  level:
    com.allcore.external.feign.IAirportTaskClient: debug
    com.allcore.external.feign.IAlarmClient: debug
    com.allcore.system.feign.ISysClient: debug