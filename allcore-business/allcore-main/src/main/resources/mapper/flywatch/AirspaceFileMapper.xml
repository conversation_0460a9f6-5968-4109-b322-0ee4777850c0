<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceFileResultMap" type="com.allcore.main.code.flywatch.entity.AirspaceFile">
        <result column="id" property="id"/>
        <result column="airspace_id" property="airspaceId"/>
        <result column="file_type" property="fileType"/>
        <result column="file_guid" property="fileGuid"/>
    </resultMap>


    <select id="selectAirspaceFilePage" resultMap="airspaceFileResultMap">
        select * from main_airspace_file where is_deleted = 0
    </select>

</mapper>
