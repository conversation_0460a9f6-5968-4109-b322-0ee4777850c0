<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.RouteDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="routeDetailResultMap" type="com.allcore.main.code.flywatch.entity.RouteDetail">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="route_guid" property="routeId"/>
        <result column="sub_file_guid" property="subFileGuid"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_guid" property="deviceId"/>
        <result column="parent_device_guid" property="parentDeviceId"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectRouteDetailPage" resultMap="routeDetailResultMap">
        select *
        from main_route_detail
        where is_deleted = 0
    </select>

</mapper>
