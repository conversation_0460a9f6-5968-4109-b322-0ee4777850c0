<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceDeviceDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceDeviceDetailResultMap" type="com.allcore.main.code.flywatch.entity.AirspaceDeviceDetail">
        <result column="id" property="id"/>
        <result column="airspace_device_type_id" property="airspaceDeviceTypeId"/>
        <result column="airspace_id" property="airspaceId"/>
        <result column="device_id" property="deviceId"/>
        <result column="parent_device_id" property="parentDeviceId"/>
        <result column="device_level" property="deviceLevel"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="fly_start_date" property="flyStartDate"/>
        <result column="fly_end_date" property="flyEndDate"/>
    </resultMap>


    <select id="selectAirspaceDeviceDetailPage" resultMap="airspaceDeviceDetailResultMap">
        select * from main_airspace_device_detail where is_deleted = 0
    </select>

    <select id="selectAirspaceTowerLineDevices" resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceDetailVO">
        SELECT
            a.device_type,
            b.device_id,
            b.device_id as id,
            b.parent_device_id,
            b.parent_device_id as parentId,
            b.fly_start_date,
            b.fly_end_date,
            b.device_level,
            b.ancestors,
            CONCAT(b.fly_start_date,'~',b.fly_end_date) as flyDate,
            CASE
                WHEN b.device_level=1 THEN
                    (SELECT line_name FROM main_line WHERE id = b.device_id)
                WHEN b.device_level>1 THEN
                    (SELECT tower_name FROM main_tower WHERE id=b.device_id)
                END AS device_name,
            CASE
                WHEN b.device_level=1 THEN ''
                WHEN b.device_level=2 THEN
                     (SELECT line_name FROM main_line WHERE id = b.parent_device_id)
                WHEN b.device_level>2 THEN
                        (SELECT tower_name FROM main_tower WHERE id = b.parent_device_id)
                END AS parent_device_name
        FROM
            main_airspace_device_type a
            LEFT JOIN main_airspace_device_detail b
            ON a.id = b.airspace_device_type_id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType}
    </select>
    <select id="selectAirspaceFanDevices"
            resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceDetailVO">
        SELECT
            a.device_type,
            b.device_id,
            b.device_id as id,
            b.parent_device_id,
            b.parent_device_id as parentId,
            b.fly_start_date,
            b.fly_end_date,
            b.device_level,
            b.ancestors,
            CONCAT(b.fly_start_date,'~',b.fly_end_date) as flyDate,
            c.device_name
        FROM
            main_airspace_device_type a
                LEFT JOIN main_airspace_device_detail b
                          ON a.id = b.airspace_device_type_id
                left join main_fan c on b.device_id = c.id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType}
    </select>

    <select id="selectAirspacePvDevices" resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceDetailVO">
        SELECT
            a.device_type,
            b.device_id,
            b.device_id as id,
            b.parent_device_id,
            b.parent_device_id as parentId,
            b.fly_start_date,
            b.fly_end_date,
            b.device_level,
            b.ancestors,
            CONCAT(b.fly_start_date,'~',b.fly_end_date) as flyDate,
            c.device_name
        FROM
            main_airspace_device_type a
                LEFT JOIN main_airspace_device_detail b
                          ON a.id = b.airspace_device_type_id
                left join main_pv_area c on b.device_id = c.id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType}
    </select>


    <select id="selectFanDevices" resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceVO">
        SELECT
            a.device_type,
            b.device_id,
            b.device_level,
            c.longitude,
            c.latitude
        FROM
            main_airspace_device_type a
                LEFT JOIN main_airspace_device_detail b
                          ON a.id = b.airspace_device_type_id
                left join main_fan c on b.device_id = c.id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType}
    </select>
    <select id="selectPvDevices" resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceVO">
        SELECT
            a.device_type,
            b.device_id,
            b.device_level,
            c.longitude,
            c.latitude
        FROM
            main_airspace_device_type a
                LEFT JOIN main_airspace_device_detail b
                          ON a.id = b.airspace_device_type_id
                left join main_pv_area c on b.device_id = c.id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType}
    </select>
    <select id="selectTowerLineDevices" resultType="com.allcore.main.code.flywatch.vo.AirspaceDeviceVO">
        SELECT
            a.device_type,
            b.device_id,
            b.parent_device_id,
            b.device_level,
            c.longitude,
            c.latitude
        FROM
            main_airspace_device_type a
            LEFT JOIN main_airspace_device_detail b
            ON a.id = b.airspace_device_type_id
            left join main_tower c on b.device_id = c.id
        where a.airspace_id = #{airspaceId} and a.device_type = #{deviceType} and b.device_level>1
        order by c.line_id,c.tower_no
    </select>

</mapper>
