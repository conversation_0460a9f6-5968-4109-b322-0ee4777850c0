<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceUserResultMap" type="com.allcore.main.code.flywatch.entity.AirspaceUser">
        <result column="id" property="id"/>
        <result column="airspace_id" property="airspaceId"/>
        <result column="user_id" property="userId"/>
    </resultMap>


    <select id="selectAirspaceUserPage" resultMap="airspaceUserResultMap">
        select * from main_airspace_user where is_deleted = 0
    </select>

</mapper>
