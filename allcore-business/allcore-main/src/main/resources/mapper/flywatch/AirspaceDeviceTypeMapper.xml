<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceDeviceTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceDeviceTypeResultMap" type="com.allcore.main.code.flywatch.entity.AirspaceDeviceType">
        <result column="id" property="id"/>
        <result column="airspace_id" property="airspaceId"/>
        <result column="device_type" property="deviceType"/>

    </resultMap>


    <select id="selectAirspaceDeviceTypePage" resultMap="airspaceDeviceTypeResultMap">
        select * from main_airspace_device_type where is_deleted = 0
    </select>

</mapper>
