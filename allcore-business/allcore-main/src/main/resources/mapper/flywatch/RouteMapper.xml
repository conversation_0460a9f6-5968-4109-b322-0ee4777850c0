<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.RouteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="routeResultMap" type="com.allcore.main.code.flywatch.entity.Route">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="route_business_code" property="routeBusinessCode"/>
        <result column="imitation_flight_file_guid" property="imitationFlightFileGuid"/>
        <result column="route_type" property="routeType"/>
        <result column="route_protocol" property="routeProtocol"/>
        <result column="route_source" property="routeSource"/>
        <result column="route_description" property="routeDescription"/>
        <result column="remark" property="remark"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectRoutePage" resultType="com.allcore.main.code.flywatch.vo.RouteVO">
        select t1.*,
        t2.device_id,
        t2.parent_device_id
        from main_route t1
        left join main_route_detail t2
        on t1.id = t2.route_id and t2.is_deleted != 1
        where t1.is_deleted = 0
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and t1.dept_code like concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.routeBusinessCode != null and dto.routeBusinessCode != ''">
            and t1.route_business_code = #{dto.routeBusinessCode}
        </if>
        <if test="dto.deviceId != null and dto.deviceId != ''">
            and t2.device_id = #{dto.deviceId}
        </if>
        order by t1.create_time desc

    </select>

    <select id="selectRoutePageByParentDeviceId" resultType="com.allcore.main.code.flywatch.vo.RouteVO">
        select t1.*,
        t2.device_id,
        t2.parent_device_id
        from main_route t1
        left join main_route_detail t2
        on t1.id = t2.route_id and t2.is_deleted != 1
        where t1.is_deleted = 0
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and t1.dept_code like concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.routeBusinessCode != null and dto.routeBusinessCode != ''">
            and t1.route_business_code = #{dto.routeBusinessCode}
        </if>
        <if test="dto.deviceId != null and dto.deviceId != ''">
            and t2.parent_device_id = #{dto.deviceId}
        </if>
        order by t1.create_time desc

    </select>
    <select id="selectRouteList" resultType="com.allcore.main.code.flywatch.vo.RouteVO">
        select t1.*,
        t2.device_id,
        t2.parent_device_id
        from main_route t1
        left join main_route_detail t2
        on t1.id = t2.route_id
        where t1.is_deleted = 0 and t2.is_deleted = 0
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and t1.dept_code like concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.routeBusinessCode != null and dto.routeBusinessCode != ''">
            and t1.route_business_code = #{dto.routeBusinessCode}
        </if>
        <if test="dto.deviceId != null and dto.deviceId != ''">
            and t2.device_id = #{dto.deviceId}
        </if>
        order by t1.create_time desc

    </select>
    <select id="getTrackByTaskId" resultType="java.lang.String">
        select file_guid from machine_nest_task where id = #{taskId}
    </select>

    <select id="routeWithDetailList" resultType="com.allcore.main.code.flywatch.dto.RouteWithDetail">
        SELECT
        a.*,
        b.sub_file_guid,
        b.device_type,
        b.device_id,
        b.parent_device_id
        FROM
        main_route a
        left join main_route_detail b ON a.id = b.route_id
        <where>
            <if test="'TMS_LINE' == deviceType ">
                and b.device_id = #{deviceId}
            </if>
            <if test="'TMS_LINE' != deviceType">
                and b.parent_device_id = #{deviceId}
            </if>
        </where>
    </select>

</mapper>
