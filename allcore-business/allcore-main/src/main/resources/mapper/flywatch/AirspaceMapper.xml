<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceResultMap" type="com.allcore.main.code.flywatch.vo.AirspaceVO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="airspace_type" property="airspaceType"/>
        <result column="fly_real_high" property="flyRealHigh"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_opinion" property="auditOpinion"/>
        <result column="file_status" property="fileStatus"/>
        <result column="dept_code" property="deptCode"/>
        <result column="uavTypes" property="uavTypes"/>
    </resultMap>


    <select id="selectAirspacePage" resultMap="airspaceResultMap">
        SELECT
        a.id,
        a.create_time,
        a.airspace_type,
        a.fly_real_high,
        a.audit_status,
        a.audit_opinion,
        a.dept_code,
        a.file_status,
        GROUP_CONCAT( distinct d.uav_type ) AS uavTypes
        FROM
        (
        SELECT
        a.id,
        a.create_time,
        a.airspace_type,
        a.fly_real_high,
        a.audit_status,
        a.audit_opinion,
        a.dept_code,
        a.file_status
        FROM
        main_airspace a
        WHERE
        a.is_deleted = 0
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dept_code like concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.airspaceType != null and dto.airspaceType != ''">
            and a.airspace_type = #{dto.airspaceType}
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and date_format(a.create_time, '%Y-%m-%d %H:%i:%s') &gt;= #{dto.startDate}
            and date_format(a.create_time, '%Y-%m-%d %H:%i:%s') &lt;= #{dto.endDate}
        </if>
        ) a
        LEFT JOIN main_airspace_uav_plane b ON a.id = b.airspace_id
        LEFT JOIN main_uav_plane c ON b.plane_id = c.id
        LEFT JOIN main_uav_model d ON c.model_id = d.id
        GROUP BY
        a.id,
        a.create_time,
        a.airspace_type,
        a.fly_real_high,
        a.audit_status,
        a.audit_opinion,
        a.dept_code,
        a.file_status
        ORDER BY
        a.create_time DESC
    </select>
    <select id="queryDeviceAndFile"
            resultType="com.allcore.main.code.flywatch.vo.QueryDeviceAndFileVO">
        select
            a.device_id,
            a.file_guid,
            b.device_type,
            CASE
                WHEN b.device_type='TMS_LINE' THEN
                        (SELECT line_name FROM main_line WHERE id = a.device_id)
                WHEN b.device_type='FAN' THEN
                        (SELECT device_name FROM main_fan WHERE id = a.device_id)
                WHEN b.device_type='PV' THEN
                        (SELECT device_name FROM main_pv_area WHERE id = a.device_id)
                ELSE ''
                END AS device_name,
            (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    main_airspace_device_detail
                WHERE
                    parent_device_id = a.device_id
            ) AS "has_children"
        from main_airspace_device_detail a
        left join main_airspace_device_type b on a.airspace_device_type_id = b.id
        where a.parent_device_id = '0' and a.airspace_id = #{airspaceId}
    </select>
<!--    <select id="getLinePointInfo" resultType="com.allcore.main.code.airspace.vo.AirspaceDevicePointVO">-->
<!--        select a.longitude,-->
<!--               a.latitude,-->
<!--               a.line_name as deviceName-->
<!--        from main_line a where a.is_deleted = 0 and a.line_guid = #{deviceGuid}-->
<!--    </select>-->
    <select id="getFanPointInfo" resultType="com.allcore.main.code.flywatch.vo.AirspaceDevicePointVO">
        select a.longitude,
               a.latitude,
               a.device_name
        from main_fan a where a.is_deleted = 0 and a.id = #{deviceId}
    </select>
    <select id="getPvPointInfo" resultType="com.allcore.main.code.flywatch.vo.AirspaceDevicePointVO">
        select a.longitude,
               a.latitude,
               a.device_name
        from main_pv_area a where a.is_deleted = 0 and a.id = #{deviceId}
    </select>
    <select id="getTowerPointInfoByParentId"
            resultType="com.allcore.main.code.flywatch.vo.AirspaceDevicePointVO">
        select a.longitude,
               a.latitude,
               a.tower_name as deviceName
        from main_tower a
        where a.is_deleted = 0
        and a.id in (select device_id from main_airspace_device_detail where ancestors like concat(#{ancestors},'%'))
    </select>

</mapper>
