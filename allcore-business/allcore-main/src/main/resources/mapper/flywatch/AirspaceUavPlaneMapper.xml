<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.flywatch.mapper.AirspaceUavPlaneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airspaceUavPlaneResultMap" type="com.allcore.main.code.flywatch.entity.AirspaceUavPlane">
        <result column="id" property="id"/>
        <result column="airspace_id" property="airspaceId"/>
        <result column="plane_id" property="planeId"/>
    </resultMap>


    <select id="selectAirspaceUavPlanePage" resultMap="airspaceUavPlaneResultMap">
        select * from main_airspace_uav_plane where is_deleted = 0
    </select>
    <select id="selectUavModels" resultType="com.allcore.main.code.flywatch.vo.AirspaceUavPlaneVO">
        SELECT
            a.plane_id,
            b.plane_name,
            c.uav_brand,
            c.id as model_id,
            c.model_name,
            c.uav_type,
            c.empty_weight,
            c.endurance_time
        FROM
            (select airspace_id,plane_id from main_airspace_uav_plane where airspace_id = #{airspaceId}) a
                LEFT JOIN main_uav_plane b ON a.plane_id = b.id
                LEFT JOIN main_uav_model c ON b.model_id = c.id
        GROUP BY
            c.uav_brand,
            c.model_name,
            c.uav_type,
            c.empty_weight,
            c.endurance_time
    </select>
    <select id="selectWordUavModels" resultType="com.allcore.main.code.flywatch.vo.ExportWordPlaneVo">
        SELECT
            c.uav_brand,
            c.model_name,
            c.uav_type,
            c.empty_weight,
            c.endurance_time,
            c.resisting_wind,
            c.max_speed,
            c.fuselage_length,
            c.fuselage_height,
            c.fuselage_width,
            c.takeoff_and_landing_mode,
            c.control_mode,
            c.operating_radius,
            c.max_flight_altitude,
            GROUP_CONCAT(d.file_guid) as fileGuids
        FROM
                (select airspace_id,plane_id from main_airspace_uav_plane where airspace_id = #{airspaceId}) a
                    LEFT JOIN main_uav_plane b ON a.plane_id = b.id
                    LEFT JOIN main_uav_model c ON b.model_id = c.id
                    left join main_uav_model_file d on c.id = d.model_id
        GROUP BY
            c.uav_brand,
            c.model_name,
            c.uav_type,
            c.empty_weight,
            c.endurance_time,
            c.resisting_wind,
            c.max_speed,
            c.fuselage_length,
            c.fuselage_height,
            c.fuselage_width,
            c.takeoff_and_landing_mode,
            c.control_mode,
            c.operating_radius,
            c.max_flight_altitude
    </select>

</mapper>
