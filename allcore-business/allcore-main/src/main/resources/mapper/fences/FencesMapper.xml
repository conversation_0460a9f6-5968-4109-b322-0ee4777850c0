<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.fences.mapper.FencesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fencesResultMap" type="com.allcore.main.code.fences.entity.Fences">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="name" property="name"/>
        <result column="silence" property="silence"/>
        <result column="biz_name" property="bizName"/>
        <result column="space_type" property="spaceType"/>
        <result column="polygon" property="polygon"/>
        <result column="time_ranges" property="timeRanges"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectFencesPage" resultMap="fencesResultMap">
        SELECT * FROM main_fences 
        WHERE is_deleted = 0
        <if test="dto.id != null and dto.id != ''">
            and id = #{dto.id}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and dept_code like concat(#{dto.deptCode}, '%')
        </if>
        <if test="dto.name != null and dto.name != ''">
            and name like concat('%',#{dto.name}, '%')
        </if>
        <if test="dto.bizName != null and dto.bizName != ''">
            and biz_name like concat('%',#{dto.bizName}, '%')
        </if>
        <if test="dto.silence != null and dto.silence != ''">
            and silence = #{dto.silence}
        </if>
        <if test="dto.spaceType != null and dto.spaceType != ''">
            and space_type = #{dto.spaceType}
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and (#{dto.startDate} BETWEEN start_date AND end_date)
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and (#{dto.endDate} BETWEEN start_date AND end_date)
        </if>
        order by create_time desc

    </select>
    
</mapper>
