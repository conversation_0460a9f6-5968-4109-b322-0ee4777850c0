<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.defect.mapper.RecognitionTaskAlgorithmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="recognitionTaskAlgorithmResultMap" type="com.allcore.main.code.defect.entity.RecognitionTaskAlgorithm">
        <result column="id" property="id"/>
        <result column="recognition_task_guid" property="recognitionTaskGuid"/>
        <result column="algorithm_manufacturers_guid" property="algorithmManufacturersGuid"/>
    </resultMap>


    <select id="selectRecognitionTaskAlgorithmPage" resultMap="recognitionTaskAlgorithmResultMap">
        select * from main_recognition_task_algorithm where is_deleted = 0
    </select>

</mapper>
