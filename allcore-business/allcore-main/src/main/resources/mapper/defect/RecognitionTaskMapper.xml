<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.defect.mapper.RecognitionTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="recognitionTaskResultMap" type="com.allcore.main.code.defect.vo.RecognitionTaskVO">
        <result column="id" property="id"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_task_no" property="inspectionTaskNo"/>
        <result column="recognition_task_name" property="recognitionTaskName"/>
        <result column="recognition_task_status" property="recognitionTaskStatus"/>
        <result column="dept_code" property="deptCode"/>
        <result column="device_type" property="deviceType"/>
        <result column="algorithmName" property="algorithmName"/>
        <result column="pictureSum" property="pictureSum"/>
        <result column="defectNum" property="defectNum"/>
        <result column="bothDefectNum" property="bothDefectNum"/>
        <result column="bothNoDefectNum" property="bothNoDefectNum"/>
        <result column="algorithmDefectNum" property="algorithmDefectNum"/>
        <result column="algorithmNoDefectNum" property="algorithmNoDefectNum"/>

    </resultMap>


    <select id="selectRecognitionTaskPage" resultMap="recognitionTaskResultMap">
        SELECT
            a.id,
            a.inspection_task_id,
            a.inspection_task_no,
            a.dept_code,
            a.device_type,
            a.recognition_task_name,
            a.recognition_task_status,
            COUNT( b.id ) AS pictureSum,
            COUNT( CASE WHEN b.pic_defect_flg = 'already_annotation' THEN b.pic_defect_flg END ) AS defectNum,
            COUNT( CASE WHEN b.pic_defect_flg = 'already_annotation' and  b.pic_algorithm_flg = 'already_annotation' THEN b.pic_defect_flg END ) AS bothDefectNum,
            COUNT( CASE WHEN b.pic_defect_flg = 'no_defect' and  b.pic_algorithm_flg = 'no_defect' THEN b.pic_defect_flg END ) AS bothNoDefectNum,
            COUNT( CASE WHEN b.pic_algorithm_flg = 'already_annotation' THEN b.pic_algorithm_flg END ) AS algorithmDefectNum,
            COUNT( CASE WHEN b.pic_algorithm_flg = 'no_defect' THEN b.pic_algorithm_flg END ) AS algorithmNoDefectNum,
            COUNT( CASE WHEN b.pic_audit_status = 'audit_pass' THEN b.pic_audit_status END ) AS picAuditNum,
            a.algorithmName,
            a.update_time
        FROM
            (
                SELECT
                    a.id,
                    a.inspection_task_id,
                    a.inspection_task_no,
                    a.dept_code,
                    a.device_type,
                    a.recognition_task_name,
                    a.recognition_task_status,
                    WM_CONCAT( d.algorithm_name ) AS algorithmName,
                    a.update_time
                FROM
                    main_recognition_task a
                        LEFT JOIN main_recognition_task_algorithm c ON a.id = c.recognition_task_id
                        LEFT JOIN main_algorithm_manufacturers d ON c.algorithm_manufacturers_id = d.id
                WHERE
                    a.is_deleted = 0
                    <if test="dto.taskStatus != null and dto.taskStatus!=''">
                        and a.recognition_task_status=#{dto.taskStatus}
                    </if>
                    <if test="dto.taskName != null and dto.taskName!=''">
                        and a.recognition_task_name = #{dto.taskName}
                    </if>
                    <if test="dto.deptCode != null and dto.deptCode!=''">
                        and a.dept_code like concat(#{dto.deptCode},'%')
                    </if>
                    <if test="dto.algorithmManufacturersId != null and dto.algorithmManufacturersId!=''">
                        and d.id=#{dto.algorithmManufacturersId}
                    </if>
                GROUP BY
                    a.id,
                    a.inspection_task_id,
                    a.inspection_task_no,
                    a.dept_code,
                    a.device_type,
                    a.recognition_task_name,
                    a.recognition_task_status
            ) a
                LEFT JOIN main_inspection_picture b ON b.recognition_task_id = a.id
            where b.is_deleted = 0 and b.bind_flag = 'yes'
        GROUP BY
            a.id,
            a.inspection_task_id,
            a.inspection_task_no,
            a.dept_code,
            a.device_type,
            a.recognition_task_name,
            a.recognition_task_status,
            a.update_time
        ORDER BY
        a.update_time DESC
    </select>


</mapper>
