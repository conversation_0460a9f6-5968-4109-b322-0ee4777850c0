<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.UavModelFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="uavModelFileResultMap" type="com.allcore.main.code.source.entity.UavModelFile">
        <result column="id" property="id"/>
        <result column="model_id" property="modelId"/>
        <result column="file_guid" property="fileGuid"/>
    </resultMap>


    <select id="selectUavModelFilePage" resultMap="uavModelFileResultMap">
        select * from main_uav_model_file where is_deleted = 0
    </select>

</mapper>
