<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.LinePicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="linePicResultMap" type="com.allcore.main.code.source.entity.LinePic">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="line_id" property="lineId"/>
        <result column="line_pic_type" property="linePicType"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectLinePicPage" resultMap="linePicResultMap">
        select *
        from main_line_pic
        where is_deleted = 0
    </select>
    <select id="getLineFiles" resultType="com.allcore.main.code.source.vo.DeviceFileVO">
        SELECT
            a.line_pic_type as fileType,
            a.file_guid,
            a.line_id as deviceId,
            b.line_name as deviceName
        FROM
            main_line_pic a
                LEFT JOIN main_line b ON a.line_id = b.id
        WHERE
                a.line_id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
