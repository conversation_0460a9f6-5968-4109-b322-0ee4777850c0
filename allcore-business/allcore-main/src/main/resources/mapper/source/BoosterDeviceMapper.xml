<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.BoosterDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="boosterStationResultMap" type="com.allcore.main.code.source.entity.BoosterStation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="booster_station_guid" property="boosterStationGuid"/>
        <result column="booster_station_name" property="boosterStationName"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="unit_person_name" property="unitPersonName"/>
        <result column="tel" property="tel"/>
        <result column="remarks" property="remarks"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>
    <resultMap id="boosterInfoResultMap" type="com.allcore.main.code.source.vo.BoosterInfoVO">
        <result column="id" property="id"/>
        <result column="booster_station_id" property="boosterStationId"/>
        <result column="region_id" property="regionId"/>
        <result column="interval_id" property="intervalId"/>
        <result column="dept_name" property="boosterStationName"/>
        <result column="region_name" property="regionName"/>
        <result column="interval_name" property="intervalName"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_model" property="deviceModel"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="input_date" property="inputDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="device_nature" property="deviceNature"/>
    </resultMap>

    <select id="selectBoosterDevicePage" resultMap="boosterInfoResultMap">
        select a.*, b.dept_name, c.region_name, d.interval_name
        from main_booster_device a
        left join sys_dept b on a.booster_station_id = b.id
        left join main_booster_region c on a.region_id = c.id
        left join main_booster_interval d on a.interval_id = d.id
        where a.is_deleted = 0
        and b.dept_code like concat(#{dto.deptCode},'%')
        <if test="dto.boosterStationId!=null and dto.boosterStationId!=''">
            and a.booster_station_id = #{dto.boosterStationId}
        </if>
        <if test="dto.regionId!=null and dto.regionId!=''">
            and a.region_id = #{dto.regionId}
        </if>
        <if test="dto.intervalId!=null and dto.intervalId!=''">
            and a.interval_id = #{dto.intervalId}
        </if>
        <if test="dto.id!=null and dto.id!=''">
            and a.id = #{dto.id}
        </if>
        order by a.create_time desc
    </select>

</mapper>
