<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.DeviceQrCodeMapper">

    <!-- 二维码管理查询结果映射 -->
    <resultMap id="qrCodeManagementResultMap" type="com.allcore.main.code.source.vo.QrCodeManagementVO">
        <id column="device_id" property="deviceId"/>
        <result column="dept_name" property="deptName"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_model" property="deviceModel"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_no" property="deviceNo"/>
        <result column="dept_code" property="deptCode"/>
        <result column="qr_code_generated" property="qrCodeGenerated"/>
        <result column="qr_code_file_path" property="qrCodeFilePath"/>
        <result column="qr_code_file_name" property="qrCodeFileName"/>
        <result column="qr_code_file_guid" property="qrCodeFileGuid"/>
    </resultMap>

    <!-- 设备二维码管理表结果映射 -->
    <resultMap id="deviceQrCodeResultMap" type="com.allcore.main.code.source.entity.DeviceQrCode">
        <id column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_model" property="deviceModel"/>
        <result column="qr_code_file_name" property="qrCodeFileName"/>
        <result column="qr_code_file_guid" property="qrCodeFileGuid"/>
        <result column="dept_code" property="deptCode"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectQrCodePage" resultType="com.allcore.main.code.source.vo.QrCodeManagementVO">
        <choose>
            <when test="dto.deviceType=='fan_box'">
                select
                    fb.id as device_id,
                    d.dept_name,
                    'fan_box' as device_type,
                    fb.fan_box_number as device_no,
                    fb.fan_box_model as device_model,
                    fb.device_name,
                    fb.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from main_fan_box fb
                left join sys_dept d on d.dept_code =fb.dept_code
                left join main_device_qr_code qr on qr.device_id =fb.id and qr.is_deleted = 0
                where fb.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and fb.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceModel != null and dto.deviceModel != ''">
                    and fb.fan_box_model like concat('%',#{dto.deviceModel},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and fb.fan_box_number like concat('%',#{dto.deviceNo},'%')
                </if>
                order by fb.device_name asc
            </when>
            <when test="dto.deviceType =='pv_box'">
                select
                    b.id as device_id,
                    d.dept_name,
                    'pv_box' as device_type,
                    b.box_no as device_no,
                    b.box_model as device_model,
                    b.device_name,
                    b.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from main_box b
                left join sys_dept d on d.dept_code =b.dept_code
                left join main_device_qr_code qr on qr.device_id =b.id and qr.is_deleted = 0
                where b.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and b.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceModel != null and dto.deviceModel != ''">
                    and b.box_model like concat('%',#{dto.deviceModel},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and b.box_no like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <when test="dto.deviceType =='pv_inverter'">
                select
                    i.id as device_id,
                    d.dept_name,
                    i.inverter_number as device_no,
                    'pv_inverter' as device_type,
                    i.inverter_model as device_model,
                    i.device_name,
                    i.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from
                    main_inverter_info i
                left join sys_dept d on d.dept_code =i.dept_code
                left join main_device_qr_code qr on qr.device_id =i.id and qr.is_deleted = 0
                where i.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and i.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceModel != null and dto.deviceModel != ''">
                    and i.inverter_model like concat('%',#{dto.deviceModel},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and i.inverter_number like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <when test="dto.getDeviceType =='FAN'">
                select
                    f.id as device_id,
                    d.dept_name,
                    'FAN' as device_type,
                    f.fan_num as device_no,
                    f.model as device_model,
                    f.device_name as device_name,
                    f.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from main_fan f
                left join sys_dept d on d.dept_code =f.dept_code
                left join main_device_qr_code qr on qr.device_id =f.id and qr.is_deleted = 0
                where f.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and f.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceModel != null and dto.deviceModel != ''">
                    and f.model like concat('%',#{dto.deviceModel},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and f.fan_num like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <when test="dto.getDeviceType =='PV'">
                select
                    a.id as device_id,
                    d.dept_name,
                    a.area_number as device_no,
                    'PV' as device_type,
                    a.component_model as device_model,
                    a.device_name,
                    a.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from main_pv_area a
                left join sys_dept d on d.dept_code =a.dept_code
                left join main_device_qr_code qr on qr.device_id =a.id and qr.is_deleted = 0
                where a.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and a.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceModel != null and dto.deviceModel != ''">
                    and a.component_model like concat('%',#{dto.deviceModel},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and a.area_number like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <when test="dto.getDeviceType =='TMS_LINE'">
                select
                    l.id as device_id,
                    d.dept_name,
                    'TMS_LINE' as device_type,
                    l.line_name as device_name,
                    '-' as device_model,
                    l.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_url
                from main_line l
                left join sys_dept d on d.dept_code =l.dept_code
                left join main_device_qr_code qr on qr.device_id =l.id and qr.is_deleted = 0
                where l.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and l.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and l.line_name like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <when test="dto.getDeviceType =='CCL_LINE'">
                select
                cl.id as device_id,
                d.dept_name,
                'CCL_LINE' as device_type,
                cl.line_name as device_name,
                '-' as device_model,
                cl.dept_code,
                qr.qr_code_file_guid,
                qr.qr_code_url
                from main_collector_line cl
                left join sys_dept d on d.dept_code =cl.dept_code
                left join main_device_qr_code qr on qr.device_id =cl.id and qr.is_deleted = 0
                where cl.is_deleted = 0
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and cl.dept_code like concat(#{dto.deptCode},'%')
                </if>
                <if test="dto.deviceNo != null and dto.deviceNo != ''">
                    and cl.line_name like concat('%',#{dto.deviceNo},'%')
                </if>
                order by device_name asc
            </when>
            <otherwise>
                <!-- 当设备类型不匹配任何已定义类型时，返回空结果 -->
                select
                    NULL as device_id,
                    NULL as dept_name,
                    NULL as device_type,
                    NULL as device_model,
                    NULL as device_name,
                    NULL as dept_code,
                    NULL as qr_code_file_guid,
                    NULL as qr_code_url
                where 1=0
            </otherwise>
        </choose>
    </select>


    <select id="selectByDeviceIdsWithType" resultType="com.allcore.main.code.source.vo.QrCodeManagementVO">
            <choose>
                <!-- 风电场箱变 -->
                <when test="deviceType == 'fan_box'">
                    SELECT
                    fb.id as device_id,
                    d.dept_name,
                    'fan_box' as device_type,
                    fb.fan_box_model as device_model,
                    fb.fan_box_number as device_no,
                    fb.device_name,
                    fb.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_file_name
                    FROM main_fan_box fb
                    LEFT JOIN sys_dept d ON d.dept_code = fb.dept_code
                    LEFT JOIN main_device_qr_code qr ON qr.device_id = fb.id AND qr.is_deleted = 0
                    WHERE fb.is_deleted = 0
                    AND fb.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>

                <!-- 光伏区箱变 -->
                <when test="deviceType == 'pv_box'">
                    SELECT
                    b.id as device_id,
                    d.dept_name,
                    'pv_box' as device_type,
                    b.box_model as device_model,
                    b.box_no as device_no,
                    b.device_name,
                    b.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_file_name
                    FROM main_box b
                    LEFT JOIN sys_dept d ON d.dept_code = b.dept_code
                    LEFT JOIN main_device_qr_code qr ON qr.device_id = b.id AND qr.is_deleted = 0
                    WHERE b.is_deleted = 0
                    AND b.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>

                <!-- 光伏逆变器 -->
                <when test="deviceType == 'pv_inverter'">
                    SELECT
                    inv.id as device_id,
                    d.dept_name,
                    'pv_inverter' as device_type,
                    inv.inverter_model as device_model,
                    inv.inverter_number as device_no,
                    inv.device_name,
                    inv.dept_code,
                    qr.qr_code_file_guid,
                    qr.qr_code_file_name
                    FROM main_inverter_info inv
                    LEFT JOIN sys_dept d ON d.dept_code = inv.dept_code
                    LEFT JOIN main_device_qr_code qr ON qr.device_id = inv.id AND qr.is_deleted = 0
                    WHERE inv.is_deleted = 0
                    AND inv.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>
                <when test="deviceType == 'FAN'">
                    select
                        f.id as device_id,
                        d.dept_name,
                        'FAN' as device_type,
                        f.model as device_model,
                        f.fan_num as device_no,
                        f.device_name,
                        f.dept_code,
                        qr.qr_code_file_guid,
                        qr.qr_code_file_name
                    from main_fan f
                    left join sys_dept d on d.dept_code =f.dept_code
                    left join main_device_qr_code qr on qr.device_id =f.id and qr.is_deleted = 0
                    where f.is_deleted = 0
                    AND f.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>
                <when test="deviceType == 'PV'">
                    select
                        a.id as device_id,
                        d.dept_name,
                        'PV' as device_type,
                        a.component_model as device_model,
                        a.area_number as device_no,
                        a.device_name,
                        a.dept_code,
                        qr.qr_code_file_guid,
                        qr.qr_code_file_name
                    from main_pv_area a
                    left join sys_dept d on d.dept_code =a.dept_code
                    left join main_device_qr_code qr on qr.device_id =a.id and qr.is_deleted = 0
                    where a.is_deleted = 0
                    AND a.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>
                <when test="deviceType =='TMS_LINE' ">
                    select
                        l.id as device_id,
                        d.dept_name,
                        'TMS_LINE' as device_type,
                        l.line_name as device_name,
                        l.line_name as device_no,
                        l.dept_code,
                        qr.qr_code_file_guid,
                        qr.qr_code_file_name
                    from main_line l
                    left join sys_dept d on d.dept_code =l.dept_code
                    left join main_device_qr_code qr on qr.device_id =l.id and qr.is_deleted = 0
                    where l.is_deleted = 0
                    AND l.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>
                <when test="deviceType == 'TMS_TOWER'">
                    select
                        t.id as device_id,
                        t.tower_name as device_name,
                        l.line_name as device_no
                    from main_tower t
                    left join main_line l on l.id = t.line_id and l.is_deleted = 0
                    where t.is_deleted = 0
                    AND t.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>

                </when>
                <when test="deviceType == 'CCL_LINE'">
                    select
                        cl.id as device_id,
                        d.dept_name,
                        'CCL_LINE' as device_type,
                        cl.line_name as device_name,
                        cl.line_name as device_no,
                        cl.dept_code,
                        qr.qr_code_file_guid,
                        qr.qr_code_file_name
                    from main_collector_line cl
                    left join sys_dept d on d.dept_code =cl.dept_code
                    left join main_device_qr_code qr on qr.device_id =cl.id and qr.is_deleted = 0
                    where cl.is_deleted = 0
                    AND cl.id IN
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                        #{deviceId}
                    </foreach>
                </when>
                <otherwise>
                    <!-- 当设备类型不匹配任何已定义类型时，返回空结果 -->
                    SELECT
                        NULL as device_id,
                        NULL as dept_name,
                        NULL as device_type,
                        NULL as device_model,
                        NULL as device_name,
                        NULL as dept_code,
                        NULL as qr_code_file_guid,
                        NULL as qr_code_file_name
                    WHERE 1=0
                </otherwise>
            </choose>
        </select>
    <select id="selectBasicInfoByDeviceId"
            resultType="com.allcore.main.code.source.vo.QrCodeDeviceVO$DeviceBasicInfoVO">

            <choose>
                <when test="deviceType == 'fan_box'">
                    select
                    fb.id as device_id,
                    fb.device_name,
                    'fan_box' as device_type,
                    '风机场区-箱变' as device_type_zh,
                    fb.fan_box_model as device_model,
                    fb.fan_box_number as device_no,
                    fb.longitude,
                    fb.latitude,
                    fb.usage_time as usage_time,
                    fb.rated_power as rated_power,
                    d.dept_name as station_name,
                    NULL as manufacturer,
                    qr.qr_code_file_guid
                    from
                    main_fan_box fb
                    left join sys_dept d on d.dept_code =fb.dept_code
                    left join main_device_qr_code qr on qr.device_id =fb.id and qr.is_deleted = 0
                    where fb.id = #{deviceId} and fb.is_deleted = 0
                </when>
                    <when test="deviceType == 'pv_box'">
                    select
                    b.id as device_id,
                    b.device_name,
                    'pv_box' as device_type,
                    '光伏场区-箱变' as device_type_zh,
                    b.box_model as device_model,
                    b.box_no as device_no,
                    b.longitude,
                    b.latitude,
                    b.put_date as usage_time,
                    b.power_rating as rated_power,
                    d.dept_name as station_name,
                    b.factory as manufacturer,
                    qr.qr_code_file_guid
                    from
                    main_box b
                    left join sys_dept d on d.dept_code =b.dept_code
                    left join main_device_qr_code qr on qr.device_id =b.id and qr.is_deleted = 0
                    where b.id = #{deviceId} and b.is_deleted = 0
                </when>
                <when test="deviceType == 'pv_inverter'">
                    select
                    i.id as device_id,
                    i.device_name,
                    'pv_inverter' as device_type,
                    '光伏场区-逆变器' as device_type_zh,
                    i.inverter_model as device_model,
                    i.inverter_number as device_no,
                    i.longitude,
                    i.latitude,
                    i.usage_time as usage_time,
                    i.rated_power as rated_power,
                    d.dept_name as station_name,
                    i.manufacturer as manufacturer,
                    qr.qr_code_file_guid
                    from
                    main_inverter_info i
                    left join sys_dept d on d.dept_code =i.dept_code
                    left join main_device_qr_code qr on qr.device_id =i.id and qr.is_deleted = 0
                    where i.id = #{deviceId} and i.is_deleted = 0
                </when>
                    <when test="deviceType == 'FAN'">
                    select
                    f.id as device_id,
                    f.device_name,
                    'FAN' as device_type,
                    '风机' as device_type_zh,
                    f.model as device_model,
                    f.fan_num as device_no,
                    f.longitude,
                    f.latitude,
                    f.put_date as usage_time,
                    NULL as rated_power,
                    d.dept_name as station_name,
                    f.manufacturer as manufacturer,
                    qr.qr_code_file_guid
                    from
                    main_fan f
                    left join sys_dept d on d.dept_code =f.dept_code
                    left join main_device_qr_code qr on qr.device_id =f.id and qr.is_deleted = 0
                    where f.id = #{deviceId} and f.is_deleted = 0
                </when>
                <when test="deviceType == 'PV'">
                    select
                    a.id as device_id,
                    a.device_name,
                    'PV' as device_type,
                    '光伏场区-子阵' as device_type_zh,
                    a.component_model as device_model,
                    a.area_number as device_no,
                    a.longitude,
                    a.latitude,
                    a.put_date as usage_time,
                    a.power_rating as rated_power,
                    d.dept_name as station_name,
                    a.component_manufacturer as manufacturer,
                    qr.qr_code_file_guid
                    from
                    main_pv_area a
                    left join sys_dept d on d.dept_code =a.dept_code
                    left join main_device_qr_code qr on qr.device_id =a.id and qr.is_deleted = 0
                    where a.id = #{deviceId} and a.is_deleted = 0
                </when>
                <when test="deviceType == 'TMS_LINE'">
                    select
                    l.id as device_id,
                    l.line_name as device_name,
                    'TMS_LINE' as device_type,
                    '输电线路' as device_type_zh,
                    '-' as device_model,
                    l.line_name as device_no,
                    d.dept_name as station_name,
                    qr.qr_code_file_guid
                    from
                    main_line l
                    left join sys_dept d on d.dept_code =l.dept_code
                    left join main_device_qr_code qr on qr.device_id =l.id and qr.is_deleted = 0
                    where l.id = #{deviceId} and l.is_deleted = 0
                </when>
                <when test="deviceType == 'CCL_LINE'">
                    select
                    cl.id as device_id,
                    cl.line_name as device_name,
                    'CCL_LINE' as device_type,
                    '集电线路' as device_type_zh,
                    '-' as device_model,
                    cl.line_name as device_no,
                    d.dept_name as station_name,
                    qr.qr_code_file_guid
                    from
                    main_collector_line cl
                    left join sys_dept d on d.dept_code =cl.dept_code
                    left join main_device_qr_code qr on qr.device_id =cl.id and qr.is_deleted = 0
                    where cl.id = #{deviceId} and cl.is_deleted = 0
                </when>
                <otherwise>
                    <!-- 当设备类型不匹配任何已定义类型时，返回空结果 -->
                    select
                        NULL as device_id,
                        NULL as device_name,
                        NULL as device_type,
                        NULL as device_type_zh,
                        NULL as device_model,
                        NULL as device_no,
                        NULL as longitude,
                        NULL as latitude,
                        NULL as usage_time,
                        NULL as rated_power,
                        NULL as station_name,
                        NULL as manufacturer,
                        NULL as qr_code_file_guid
                    where 1=0
                </otherwise>
            </choose>


    </select>


</mapper>