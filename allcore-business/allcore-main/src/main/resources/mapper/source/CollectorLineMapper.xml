<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.CollectorLineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="lineResultMap" type="com.allcore.main.code.source.entity.CollectorLine">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="line_name" property="lineName"/>
        <result column="region_features" property="regionFeatures"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="erection_method" property="erectionMethod"/>
        <result column="line_total_length" property="lineTotalLength"/>
        <result column="put_date" property="putDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectLinePage" resultMap="lineResultMap">
        select * from main_collector_line where is_deleted = 0
        <if test="null != deviceQuery.lineName and '' != deviceQuery.lineName">
            and  line_name like concat('%',#{deviceQuery.lineName},'%')
        </if>
        <if test="null != deviceQuery.voltageLevel and '' != deviceQuery.voltageLevel">
            and  voltage_level like  concat('%',#{deviceQuery.voltageLevel},'%')
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like concat(#{deviceQuery.deptCode},'%')
        </if>
        order by sort_order desc,create_time desc
    </select>

</mapper>
