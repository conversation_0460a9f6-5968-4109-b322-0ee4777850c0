<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.AirportAirplaneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airportAirplaneResultMap" type="com.allcore.main.code.source.entity.AirportAirplane">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="airport_nest_guid" property="airportNestGuid"/>
        <result column="carry_guid" property="carryGuid"/>
        <result column="uav_type" property="uavType"/>
        <result column="uav_brand" property="uavBrand"/>
        <result column="uav_model_guid" property="uavModelGuid"/>
        <result column="uav_guid" property="uavGuid"/>
        <result column="uav_sn_code" property="uavSnCode"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectAirportAirplanePage" resultMap="airportAirplaneResultMap">
        select * from main_airport_airplane where is_deleted = 0
    </select>

</mapper>
