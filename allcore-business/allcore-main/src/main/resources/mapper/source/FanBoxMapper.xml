<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.FanBoxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fanBoxResultMap" type="com.allcore.main.code.source.entity.FanBox">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="fan_box_model" property="fanBoxModel"/>
        <result column="fan_box_number" property="fanBoxNumber"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="rated_power" property="ratedPower"/>
        <result column="usage_time" property="usageTime"/>
        <result column="fan_id" property="fanId"/>
        <result column="fan_name" property="fanName"/>
        <result column="dept_code" property="deptCode"/>
        <result column="device_name" property="deviceName"/>
    </resultMap>
    <select id="getAvailableFan" resultType="com.allcore.main.code.source.vo.FanVO">
        select fan.id,
               fan.device_name,
               fan.longitude,
               fan.latitude,
               fan.elevation,
               fan.model
        from main_fan fan
        where fan.is_deleted = 0
        <if test="deptCode != null and deptCode != ''">
            and fan.dept_code like concat(#{deptCode},'%')
        </if>
        order by fan.device_name asc
    </select>

    <select id="selectFanBoxPage" resultType="com.allcore.main.code.source.vo.FanBoxVO">
        select *
        from main_fan_box
        where is_deleted = 0
        <if test="fanBoxDTO.deptCode != null and fanBoxDTO.deptCode != ''">
            and dept_code like concat(#{fanBoxDTO.deptCode},'%')
        </if>
        <if test="fanBoxDTO.fanBoxNumber != null and fanBoxDTO.fanBoxNumber != ''">
            and fan_box_number like concat('%',#{fanBoxDTO.fanBoxNumber},'%')
        </if>
        <if test="fanBoxDTO.fanBoxModel != null and fanBoxDTO.fanBoxModel != ''">
            and fan_box_model like concat('%',#{fanBoxDTO.fanBoxModel},'%')
        </if>
    </select>


</mapper>