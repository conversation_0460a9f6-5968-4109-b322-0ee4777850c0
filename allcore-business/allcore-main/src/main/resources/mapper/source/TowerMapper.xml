<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.TowerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="towerResultMap" type="com.allcore.main.code.source.entity.Tower">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="line_id" property="lineId"/>
        <result column="tower_name" property="towerName"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="elevation" property="elevation"/>
        <result column="tower_no" property="towerNo"/>
        <result column="loop_number" property="loopNumber"/>
        <result column="strain_length" property="strainLength"/>
        <result column="regional_type" property="regionalType"/>
        <result column="normal_height" property="normalHeight"/>
        <result column="tower_height" property="towerHeight"/>
        <result column="tower_head_height" property="towerHeadHeight"/>
        <result column="is_same_pole" property="isSamePole"/>
        <result column="altitude" property="altitude"/>
        <result column="is_corner" property="isCorner"/>
        <result column="topography" property="topography"/>
        <result column="remark" property="remark"/>
        <result column="is_airworthy" property="isAirworthy"/>
        <result column="geology" property="geology"/>
        <result column="tower_nature" property="towerNature"/>
        <result column="is_terminal" property="isTerminal"/>
        <result column="rulingsp_span" property="rulingspSpan"/>
        <result column="span" property="span"/>
        <result column="tower_model" property="towerModel"/>
        <result column="tower_position" property="towerPosition"/>
        <result column="pms_code" property="pmsCode"/>
        <result column="sort" property="sort"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectTowerPage" resultMap="towerResultMap">
        SELECT * FROM main_tower WHERE is_deleted = 0
        <if test="null != deviceQuery.towerName and '' != deviceQuery.towerName">
            and tower_name like concat('%',#{deviceQuery.towerName},'%')
        </if>
        <if test="null != deviceQuery.towerNo and '' != deviceQuery.towerNo">
            and tower_no like concat('%',#{deviceQuery.towerNo},'%')
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like concat(#{deviceQuery.deptCode},'%')
        </if>
        <if test="deviceQuery.lineId != null and deviceQuery.lineId != ''">
            AND line_id = #{deviceQuery.lineId}
        </if>
        ORDER BY sort_order DESC, create_time DESC
    </select>

</mapper>
