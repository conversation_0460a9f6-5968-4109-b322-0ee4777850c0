<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.UavPlaneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="uavPlaneResultMap" type="com.allcore.main.code.source.entity.UavPlane">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="model_id" property="modelId"/>
        <result column="plane_name" property="planeName"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="real_certification_number" property="realCertificationNumber"/>
        <result column="sn_code" property="snCode"/>
        <result column="uav_dept_id" property="uavDeptId"/>
        <result column="registration_time" property="registrationTime"/>
        <result column="device_source" property="deviceSource"/>
        <result column="asset_attribute" property="assetAttribute"/>
        <result column="custodian" property="custodian"/>
        <result column="state" property="state"/>
        <result column="remark" property="remark"/>
        <result column="filing_number" property="filingNumber"/>
        <result column="filing_type" property="filingType"/>
        <result column="black_box_number" property="blackBoxNumber"/>
        <result column="video_number" property="videoNumber"/>
        <result column="service_interval" property="serviceInterval"/>
        <result column="warranty_start_time" property="warrantyStartTime"/>
        <result column="warranty_end_time" property="warrantyEndTime"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectUavPlanePage" resultType="com.allcore.main.code.source.vo.UavPlaneVO">
        select t1.* ,t2.uav_type from main_uav_plane t1
        left join main_uav_model t2
        on t1.model_id = t2.id
        where t1.is_deleted = 0
        <if test="dto.uavType != null and dto.uavType != ''">
            and t2.uav_type = #{dto.uavType}
        </if>
        <if test="dto.assetAttribute != null and dto.assetAttribute != ''">
            and t1.asset_attribute = #{dto.assetAttribute}
        </if>
        <if test="dto.snCode != null and dto.snCode != ''">
            and t1.sn_code like concat('%',#{dto.snCode},'%')
        </if>
        <if test="dto.deptCode!=null and dto.deptCode!=''">
            and t1.dept_code like concat(#{dto.deptCode},'%')
        </if>
        order by t1.create_time desc
    </select>
    <select id="selectUavPlaneWithAirport" resultType="com.allcore.main.code.source.vo.UavPlaneVO">
        select t1.* ,t2.uav_type from main_uav_plane t1
        left join main_uav_model t2
        on t1.model_id = t2.id
        where t1.is_deleted = 0
          and t1.id in (select distinct plane_id from main_airport_airplane where is_deleted = 0)
        <if test="dto.uavType != null and dto.uavType != ''">
            and t2.uav_type = #{dto.uavType}
        </if>
        <if test="dto.assetAttribute != null and dto.assetAttribute != ''">
            and t1.asset_attribute = #{dto.assetAttribute}
        </if>
        <if test="dto.snCode != null and dto.snCode != ''">
            and t1.sn_code like concat('%',#{dto.snCode},'%')
        </if>
        <if test="dto.deptCode!=null and dto.deptCode!=''">
            and t1.dept_code like concat(#{dto.deptCode},'%')
        </if>
        order by t1.create_time desc
    </select>

</mapper>
