<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allcore.main.code.source.mapper.CombinerBoxMapper">

    <resultMap id="combinerBoxResultMap" type="com.allcore.main.code.source.entity.CombinerBox">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="dept_code" property="deptCode"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="combiner_box_code" property="combinerBoxCode"/>
        <result column="usageTime" property="usageTime"/>
    </resultMap>
    <select id="selectCombinerBoxPage" resultType="com.allcore.main.code.source.vo.CombinerBoxVO">
        select
            id,
            dept_code,
            combiner_box_code,
            longitude,
            latitude,
            usage_time,
            manufacturer
        from main_combiner_box
        where is_deleted = 0
        <if test=" combinerBoxDTO.deptCode != null and combinerBoxDTO.deptCode != ''">
        and dept_code like #{combinerBoxDTO.deptCode} || '%'
        </if>
        <if test=" combinerBoxDTO.combinerBoxCode != null and combinerBoxDTO.combinerBoxCode != ''">
        and combiner_box_code like '%' || #{combinerBoxDTO.combinerBoxCode} || '%'
        </if>
    </select>


</mapper>