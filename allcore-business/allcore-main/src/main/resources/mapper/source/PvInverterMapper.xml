<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allcore.main.code.source.mapper.PvInverterMapper">

    <resultMap id="pvInverterResultMap" type="com.allcore.main.code.source.entity.PvInverter">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="dept_code" property="deptCode"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="device_name" property="deviceName"/>
        <result column="inverter_serial_number" property="inverterSerialNumber"/>
        <result column="inverter_model" property="inverterModel"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="height" property="height"/>
        <result column="rated_power" property="ratedPower"/>
        <result column="usage_time" property="usageTime"/>
    </resultMap>


    <select id="selectPvInverterPage" resultType="com.allcore.main.code.source.entity.PvInverter">
        select
            mii.id,
            mii.dept_code,
            mii.inverter_serial_number,
            mii.inverter_model,
            mii.longitude,
            mii.latitude,
            mii.height,
            mii.rated_power,
            mii.usage_time,
            mii.manufacturer,
            mii.inverter_number,
            mii.device_name,
            count(mis.inverter_id) as groupSerialNumber
        from main_inverter_info mii
        left join main_inverter_string mis on mis.inverter_id = mii.id
        where mii.is_deleted = 0
        <if test="pvInverterDTO.deviceName != null and pvInverterDTO.deviceName != ''">
            and mii.device_name like concat('%', #{pvInverterDTO.deviceName}, '%')
        </if>
        <if test="pvInverterDTO.inverterSerialNumber != null and pvInverterDTO.inverterSerialNumber != ''">
            and mii.inverter_serial_number like concat('%', #{pvInverterDTO.inverterSerialNumber}, '%')
        </if>
        <if test="pvInverterDTO.inverterModel != null and pvInverterDTO.inverterModel != ''">
            and mii.inverter_model like concat('%', #{pvInverterDTO.inverterModel}, '%')
        </if>
        <if test="pvInverterDTO.deptCode != null and pvInverterDTO.deptCode != ''">
            and mii.dept_code like concat(#{pvInverterDTO.deptCode}, '%')
        </if>
        <if test="pvInverterDTO.pvAreaId != null and pvInverterDTO.pvAreaId != ''">
            and mii.pv_area_id = #{pvInverterDTO.pvAreaId}
        </if>
        group by  mii.dept_code, mii.inverter_serial_number, mii.inverter_model, mii.longitude, mii.latitude, mii.rated_power, mii.usage_time,
                  mii.manufacturer, mii.inverter_number, mii.device_name, mii.height,mii.id
        order by mii.create_time desc
    </select>

    <select id="selectPvInverterTree" resultType="com.allcore.main.code.common.vo.PVTreeNode">
        select id as id,
               device_name as title
        from main_inverter_info where is_deleted = 0
        <if test="deptCode != null and deptCode.trim() !=''">
            and dept_code = #{deptCode}
        </if>
    </select>
</mapper>