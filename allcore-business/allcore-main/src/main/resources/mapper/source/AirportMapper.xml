<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.AirportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="airportResultMap" type="com.allcore.main.code.source.entity.Airport">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="airport_nest_name" property="airportNestName"/>
        <result column="airport_brand" property="airportBrand"/>
        <result column="airport_category" property="airportCategory"/>
        <result column="airport_model" property="airportModel"/>
        <result column="asset_attribute" property="assetAttribute"/>
        <result column="sn_code" property="snCode"/>

        <result column="plane_name" property="planeName"/>
        <result column="plane_brand" property="planeBrand"/>
        <result column="plane_category" property="planeCategory"/>
        <result column="plane_model" property="planeModel"/>
        <result column="plane_sn_code" property="planeSnCode"/>

        <result column="custodian" property="custodian"/>
        <result column="phone" property="phone"/>
        <result column="unit" property="unit"/>
        <result column="coordinates" property="coordinates"/>
        <result column="dept_code" property="deptCode"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
        <result column="inspection_radius" property="inspectionRadius"/>
    </resultMap>


    <select id="selectAirportPage" resultMap="airportResultMap">
        select * from main_airport where is_deleted = 0
        <if test="dto.airportCategory != null and dto.airportCategory != ''">
            and airport_category = #{dto.airportCategory}
        </if>
        <if test="dto.airportBrand != null and dto.airportBrand != ''">
            and airport_brand = #{dto.airportBrand}
        </if>
        <if test="dto.airportNestName != null and dto.airportNestName != ''">
            and airport_nest_name like concat('%',#{dto.airportNestName},'%')
        </if>
        <if test="dto.deptCode!=null and dto.deptCode!=''">
            and dept_code like concat(#{dto.deptCode},'%')
        </if>
        order by create_time desc
    </select>
    <select id="select" resultType="com.allcore.main.code.source.entity.Airport">
        select * from main_airport where is_deleted = 0
        <if test="dto.deptCode!=null and dto.deptCode!=''">
            and dept_code like concat(#{dto.deptCode},'%')
        </if>
        order by create_time desc
    </select>
    <select id="getRouteByAirport" resultType="com.allcore.external.dto.NestTaskDTO">
        select a.id as inspection_task_id,b.id,b.file_guid,b.airport_nest_id from
            (select a.id from main_inspection_task a,main_inspection_method_detail b
             where a.id = b.inspection_task_id and b.inspection_device_id = #{airportNestId}
               and a.is_deleted = 0 and inspection_type = #{deviceType}
             order by a.create_time desc limit 1) a
                left join machine_nest_task b on a.id = b.inspection_task_id
    </select>

</mapper>
