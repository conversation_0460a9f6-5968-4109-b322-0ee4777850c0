<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.BoxMapper">
    <resultMap id="boxResultMap" type="com.allcore.main.code.source.vo.BoxVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="device_name" property="deviceName"/>
        <result column="box_no" property="boxNo"/>
        <result column="box_model" property="boxModel"/>
        <result column="device_type" property="deviceType"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="height" property="height"/>
        <result column="factory" property="factory"/>
        <result column="power_rating" property="powerRating"/>
        <result column="put_date" property="putDate"/>
        <result column="inverter_num" property="inverterNum"/>
    </resultMap>

    <select id="selectBoxList" resultMap = "boxResultMap">
        select * from main_box where is_deleted = 0 AND pv_area_id = #{boxDTO.areaId}
        <if test="boxDTO.deviceName != null and boxDTO.deviceName != ''">
            and device_name = #{boxDTO.deviceName}
        </if>
        <if test= "boxDTO.boxNo != null and boxDTO.boxNo != ''">
            and box_no like concat('%',#{boxDTO.boxNo},'%')
        </if>
        <if test= "boxDTO.deviceType != null and boxDTO.deviceType != ''">
            and device_type like concat('%',#{boxDTO.deviceType},'%')
        </if>
        <if test= "boxDTO.deptCode != null and boxDTO.deptCode != ''">
            and dept_code like concat(#{boxDTO.deptCode},'%')
        </if>
        order by create_time desc
    </select>

    <select id="getDeviceName" resultType="java.lang.String">
        SELECT DISTINCT device_name FROM main_inverter_info WHERE is_deleted = 0
        <if test="deptCode!= null and deptCode != ''">
            AND dept_code LIKE CONCAT(#{deptCode},'%')
        </if>
    </select>
    <select id="getDeviceType" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT DISTINCT device_type FROM main_box WHERE is_deleted = 0
        <if test="deptCode!= null and deptCode != ''">
            AND dept_code LIKE CONCAT(#{deptCode},'%')
        </if>
    </select>
    <select id="selectBoxInfoPage" resultMap = "boxResultMap">
        select a.*,b.area_number from main_box a left join main_pv_area b on b.id = a.pv_area_id
        where a.is_deleted = 0
        <if test="boxDTO.deviceName != null and boxDTO.deviceName != ''">
            and a.device_name = #{boxDTO.deviceName}
        </if>
        <if test= "boxDTO.boxNo != null and boxDTO.boxNo != ''">
            and a.box_no like concat('%',#{boxDTO.boxNo},'%')
        </if>
        <if test= "boxDTO.deviceType != null and boxDTO.deviceType != ''">
            and a.device_type like concat('%',#{boxDTO.deviceType},'%')
        </if>
        <if test="boxDTO.deptCode!= null and boxDTO.deptCode != ''">
            and a.dept_code like concat(#{boxDTO.deptCode},'%')
        </if>
        order by create_time desc
    </select>

    <select id="selectPvBoxTree" resultType="com.allcore.main.code.common.vo.PVTreeNode">
        select id as id,
               device_name as title
        FROM main_box WHERE is_deleted = 0
        <if test="deptCode!= null and deptCode.trim() != ''">
            AND dept_code = #{deptCode}
        </if>
    </select>
</mapper>
