<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.UavModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="uavModelResultMap" type="com.allcore.main.code.source.entity.UavModel">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="uav_brand" property="uavBrand"/>
        <result column="model_name" property="modelName"/>
        <result column="uav_type" property="uavType"/>
        <result column="have_rtk" property="haveRtk"/>
        <result column="empty_weight" property="emptyWeight"/>
        <result column="max_takeoff_weight" property="maxTakeoffWeight"/>
        <result column="resisting_wind" property="resistingWind"/>
        <result column="max_speed" property="maxSpeed"/>
        <result column="max_flight_altitude" property="maxFlightAltitude"/>
        <result column="endurance_time" property="enduranceTime"/>
        <result column="fuselage_length" property="fuselageLength"/>
        <result column="fuselage_height" property="fuselageHeight"/>
        <result column="fuselage_width" property="fuselageWidth"/>
        <result column="control_mode" property="controlMode"/>
        <result column="operating_radius" property="operatingRadius"/>
        <result column="takeoff_and_landing_mode" property="takeoffAndLandingMode"/>
        <result column="camera_sensor_type" property="cameraSensorType"/>
        <result column="camera_model" property="cameraModel"/>
        <result column="scene" property="scene"/>
        <result column="pixel" property="pixel"/>
        <result column="ios_range" property="iosRange"/>
        <result column="ptz" property="ptz"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectUavModelPage" resultMap="uavModelResultMap">
        select * from main_uav_model where is_deleted = 0
        <if test="vo.uavType != null and vo.uavType !=''">
            and uav_type = #{vo.uavType}
        </if>
        <if test="vo.uavBrand != null and vo.uavBrand !=''">
            and uav_brand = #{vo.uavBrand}
        </if>
        <if test="vo.modelName != null and vo.modelName !=''">
            and model_name like  concat('%',#{vo.modelName},'%')
        </if>
        order by create_time desc
    </select>

</mapper>
