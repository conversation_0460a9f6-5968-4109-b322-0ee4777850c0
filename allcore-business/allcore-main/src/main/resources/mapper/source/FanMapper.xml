<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.FanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fanResultMap" type="com.allcore.main.code.source.entity.Fan">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="device_name" property="deviceName"/>
        <result column="fan_num" property="fanNum"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
        <result column="model" property="model"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="capacity" property="capacity"/>
        <result column="blade_length" property="bladeLength"/>
        <result column="blade_manufacturer" property="bladeManufacturer"/>
        <result column="height" property="height"/>
        <result column="tower_height" property="towerHeight"/>
        <result column="bending_blades" property="bendingBlades"/>
        <result column="province" property="province"/>
        <result column="put_date" property="putDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectFanPage" resultMap="fanResultMap">
        select * from main_fan where is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like concat('%',#{deviceQuery.deviceName},'%')
        </if>
        <if test="null != deviceQuery.fanNum and '' != deviceQuery.fanNum">
            and fan_num like concat('%',#{deviceQuery.fanNum},'%')
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like concat(#{deviceQuery.deptCode},'%')
        </if>
        <if test="null != deviceQuery.model and '' != deviceQuery.model">
            and model like concat('%',#{deviceQuery.model},'%')
        </if>
        order by sort_order asc,device_name asc,create_time desc
    </select>

    <select id="selectFan" resultMap="fanResultMap">
        select * from main_fan where is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like concat('%',#{deviceQuery.deviceName},'%')
        </if>
        <if test="null != deviceQuery.fanNum and '' != deviceQuery.fanNum">
            and fan_num like concat('%',#{deviceQuery.fanNum},'%')
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like concat(#{deviceQuery.deptCode},'%')
        </if>
        <if test="null != deviceQuery.model and '' != deviceQuery.model">
            and model like concat("%",#{deviceQuery.model},"%")
        </if>
        order by sort_order asc,device_name asc,create_time asc
    </select>

    <select id="getLastModelPath" resultType="java.lang.String">
        select file_path
        from main_three_data
        where is_deleted = 0
          and device_type = 'FAN'
          and file_type = '3'
        order by create_time desc limit 1
    </select>

</mapper>
