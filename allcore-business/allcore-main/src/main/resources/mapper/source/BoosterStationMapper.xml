<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.BoosterStationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="boosterStationResultMap" type="com.allcore.main.code.source.entity.BoosterStation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="booster_station_guid" property="boosterStationGuid"/>
        <result column="booster_station_name" property="boosterStationName"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="unit_person_name" property="unitPersonName"/>
        <result column="tel" property="tel"/>
        <result column="remarks" property="remarks"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectBoosterStationPage" resultMap="boosterStationResultMap">
        select * from main_booster_station where is_deleted = 0
    </select>

</mapper>
