<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.DeviceForMainMapper">

    <resultMap id="info" type="com.allcore.main.code.source.dto.AllDeviceInfoForDefectDTO">
        <result column="device_guid" property="deviceGuid"/>
        <result column="lo" property="lo"/>
        <result column="la" property="la"/>
        <result column="al" property="al"/>
        <result column="deviceAltitude" property="deviceAltitude"/>
        <result column="bladeLength" property="bladeLength"/>
        <result column="deviceName" property="deviceName"/>
        <result column="groupGuid" property="groupGuid"/>
        <result column="groupName" property="groupName"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <resultMap id="name" type="com.allcore.main.code.source.dto.DeviceForMainNameDTO">
        <result column="device_guid" property="deviceGuid"/>
        <result column="device_name" property="deviceName"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="deviceType" property="deviceType"/>
        <collection property="towerList" ofType="com.allcore.main.code.source.dto.DeviceForMainNameDTO">
            <result column="tower_guid" property="deviceGuid"/>
            <result column="tower_name" property="deviceName"/>
            <result column="tower_is_deleted" property="isDeleted"/>
        </collection>
    </resultMap>

    <select id="getDeviceInformationForDefect" resultMap="info">
        <foreach collection="deviceGuid.keys" item="key" separator="union all">
            <choose>
                <when test="null != key and 'TMS_LINE'.toString() == key.toString()">
                    SELECT
                    a.id AS device_guid,
                    a.tower_name as deviceName,
                    a.longitude AS lo,
                    a.latitude AS la,
                    a.altitude AS al,
                    '' AS deviceAltitude,
                    '' AS bladeLength,
                    b.id AS groupGuid,
                    b.line_name AS groupName,
                    b.voltage_level,
                    a.sort as sort
                    FROM
                    main_tower a left join main_line b on b.id = a.line_id
                    where a.id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
                <when test="null != key and 'FAN'.toString() == key.toString()">
                    SELECT
                    id AS device_guid,
                    fan_num as deviceName,
                    longitude AS lo,
                    latitude as la,
                    elevation as  al,
                    height as deviceAltitude,
                    blade_length AS bladeLength,
                    '' as   groupGuid,
                    '' as groupName,
                    '' as voltage_level,
                    1 as sort
                    FROM
                    main_fan
                    WHERE id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
                <when test="null != key and 'PV'.toString() == key.toString()">
                    SELECT
                    id AS device_guid,
                    area_number as deviceName,
                    longitude AS lo,
                    latitude AS la,
                    elevation AS al,
                    '' as deviceAltitude,
                    '' as  bladeLength,
                    '' as   groupGuid,
                    '' as groupName,
                    '' as voltage_level,
                    1 as sort
                    FROM
                    main_pv_area
                    WHERE id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
<!--                <when test="'BOOSTER'.toString() == key.toString()">-->
<!--                    SELECT-->
<!--                    booster_station_guid AS device_guid,-->
<!--                    booster_station_name AS deviceName,-->
<!--                    longitude AS lo,-->
<!--                    latitude as la,-->
<!--                    booster_station_guid AS groupGuid,-->
<!--                    '0' AS tower_guid,-->
<!--                    '0' AS tower_name-->
<!--                    FROM-->
<!--                    basic_booster_station-->
<!--                    WHERE-->
<!--                    booster_station_guid in-->
<!--                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">-->
<!--                        #{i}-->
<!--                    </foreach>-->
<!--                </when>-->
                <!--                <when test="'OTHER'.toString() == key.toString()">-->
                <!--                    SELECT-->
                <!--                    guid AS device_guid,-->
                <!--                    device_name AS device_name,-->
                <!--                    '0' AS tower_guid,-->
                <!--                    '0' AS tower_name-->
                <!--                    FROM-->
                <!--                    basic_device_other-->
                <!--                    WHERE-->
                <!--                    guid in-->
                <!--                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">-->
                <!--                        #{i}-->
                <!--                    </foreach>-->
                <!--                </when>-->
            </choose>
        </foreach>
    </select>

    <select id="getDeviceNameForOrderPic" resultMap="name">
        <foreach collection="deviceGuid.keys" item="key" separator="union all">
            <choose>
                <when test="null != key and 'TMS_LINE'.toString() == key.toString()">
                    SELECT
                    a.id AS device_guid,
                    a.line_name AS device_name,
                    b.tower_guid,
                    b.tower_name
                    FROM
                    main_line a
                    LEFT JOIN main_tower b ON a.id = b.line_id
                    where b.id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
                <when test="null != key and 'FAN'.toString() == key.toString()">
                    SELECT
                    id AS device_guid,
                    fan_num AS device_name,
                    '0' as tower_guid,
                    '0' as tower_name
                    FROM
                    main_fan
                    WHERE id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
                <when test="null != key and 'PV'.toString() == key.toString()">
                    SELECT
                    id AS device_guid,
                    area_number AS device_name,
                    '0' AS tower_guid,
                    '0' AS tower_name
                    FROM
                    main_pv_area
                    WHERE id in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
                <when test="null != key and 'BOOSTER'.toString() == key.toString()">
                    SELECT
                    booster_station_guid AS device_guid,
                    booster_station_name AS device_name,
                    '0' AS tower_guid,
                    '0' AS tower_name
                    FROM
                    main_booster_station
                    WHERE
                    booster_station_guid in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
<!--
                <when test="null != key and 'OTHER'.toString() == key.toString()">
                    SELECT
                    guid AS device_guid,
                    device_name AS device_name,
                    '0' AS tower_guid,
                    '0' AS tower_name
                    FROM
                    basic_device_other
                    WHERE
                    guid in
                    <foreach collection="deviceGuid[key]" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </when>
-->
            </choose>
        </foreach>
    </select>

    <select id="getDeviceNameById" resultMap="name">
        <choose>
            <when test="'LINE'.toString() == key.toString()">
                SELECT
                a.id AS device_guid,
                a.line_name AS device_name,
                b.tower_guid,
                b.tower_name
                FROM
                main_line a
                LEFT JOIN main_tower b ON a.id = b.line_id
                WHERE
                b.id = #{deviceId}
            </when>
            <when test="'FAN'.toString() == key.toString()">
                SELECT
                id AS device_guid,
                fan_num AS device_name,
                '0' AS tower_guid,
                '0' AS tower_name
                FROM
                main_fan
                WHERE
                id = #{deviceId}
            </when>
            <when test="'PV'.toString() == key.toString()">
                SELECT
                id AS device_guid,
                area_number AS device_name,
                '0' AS tower_guid,
                '0' AS tower_name
                FROM
                main_pv_area
                WHERE
                id = #{deviceId}
            </when>
            <when test="'BOOSTER'.toString() == key.toString()">
                SELECT
                booster_station_guid AS device_guid,
                booster_station_name AS device_name,
                '0' AS tower_guid,
                '0' AS tower_name
                FROM
                main_booster_station
                WHERE
                booster_station_guid = #{deviceId}
            </when>
        </choose>
    </select>

    <select id="getDeviceByDeptId" resultType="com.allcore.main.code.source.vo.AppLedgerDetailVO">
        SELECT
            id AS groupGuid,
            line_name AS groupName,
            0 AS groupType
        FROM
            main_line
        where dept_code like concat(#{deptCode},'%') and is_deleted = 0
        union all
        SELECT
            id AS groupGuid,
            fan_num AS groupName,
            1 AS groupType
        FROM
            main_fan
        where dept_code like concat(#{deptCode},'%') and is_deleted = 0
        union all
        SELECT
            id AS groupGuid,
            area_number AS groupName,
            2 AS groupType
        FROM
            main_pv_area
        where dept_code like concat(#{deptCode},'%') and is_deleted = 0
    </select>

    <select id="getDeviceByGroupGuid" resultType="com.allcore.main.code.source.vo.AppDeviceVO">
        SELECT
            b.id AS groupGuid,
            b.line_name AS groupName,
            b.voltage_level,
            0 AS groupType,
            a.id AS deviceGuid,
            a.tower_name AS deviceName,
            a.tower_no AS deviceNumber,
            0 AS deviceType,
            a.altitude AS al,
            a.latitude AS la,
            a.longitude AS lo,
            a.update_time,
            a.update_user as updater
        FROM
            main_tower a
                LEFT JOIN main_line b ON a.line_id = b.id
        where b.id = #{deviceGuid}
            union all
        SELECT
            id AS groupGuid,
            fan_num AS groupName,
            '' as voltage_level,
            1 AS groupType,
            id AS deviceGuid,
            device_name AS deviceName,
            fan_num AS deviceNumber,
            1 AS deviceType,
            elevation AS al,
            latitude AS la,
            longitude AS lo,
            update_time,
            update_user as updater
        FROM
            main_fan
        where id = #{deviceGuid}
        union all
        SELECT
            id AS groupGuid,
            area_number AS groupName,
            '' as voltage_level,
            2 AS groupType,
            id AS deviceGuid,
            device_name AS deviceName,
            area_number AS deviceNumber,
            2 AS deviceType,
            elevation AS al,
            latitude AS la,
            longitude AS lo,
            update_time,
            update_user as updater
        FROM
            main_pv_area
        where id = #{deviceGuid}
    </select>

    <select id="getUavInformationForApp" resultType="java.util.Map">
        SELECT
            a.id as uavId,
            a.plane_name AS droneName,
            b.id as model,
            b.uav_brand as brand,
            b.model_name as modelName
        FROM
            main_uav_plane a
                LEFT JOIN main_uav_model b ON a.model_id = b.id
        WHERE
            a.sn_code = #{sn} and a.is_deleted = 0
    </select>

</mapper>
