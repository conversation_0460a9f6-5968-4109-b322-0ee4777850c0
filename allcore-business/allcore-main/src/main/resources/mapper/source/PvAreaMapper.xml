<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.PvAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pvAreaResultMap" type="com.allcore.main.code.source.entity.PvArea">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="device_name" property="deviceName"/>
        <result column="area_number" property="areaNumber"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
        <result column="coordinates" property="coordinates"/>
        <result column="power_rating" property="powerRating"/>
        <result column="put_date" property="putDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="component_manufacturer" property="componentManufacturer"/>
        <result column="component_model" property="componentModel"/>
        <result column="support_type" property="supportType"/>
        <result column="support_arrangement_method" property="supportArrangementMethod"/>
    </resultMap>


    <select id="selectPvAreaPage" resultMap="pvAreaResultMap">
        select * from main_pv_area where is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like '%' || #{deviceQuery.deviceName} || '%'
        </if>
        <if test="null != deviceQuery.powerRating and '' != deviceQuery.powerRating">
            and power_rating like  '%' || #{deviceQuery.powerRating} || '%'
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like #{deviceQuery.deptCode} || '%'
        </if>
        order by device_name
    </select>

</mapper>
