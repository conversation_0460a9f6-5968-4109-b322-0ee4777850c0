<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.PvAreaPicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pvAreaPicResultMap" type="com.allcore.main.code.source.entity.PvAreaPic">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="pv_area_pic_type" property="pvAreaPicType"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectPvAreaPicPage" resultMap="pvAreaPicResultMap">
        select *
        from main_pv_area_pic
        where is_deleted = 0
    </select>

    <select id="getPvFiles" resultType="com.allcore.main.code.source.vo.DeviceFileVO">
        SELECT
        a.pv_area_pic_type as fileType,
        a.file_guid,
        a.pv_area_id as deviceId,
        b.device_name
        FROM
        main_pv_area_pic a
        LEFT JOIN main_pv_area b ON a.pv_area_id = b.id
        WHERE
        a.pv_area_id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
