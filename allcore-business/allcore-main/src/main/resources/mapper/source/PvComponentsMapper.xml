<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.PvComponentsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pvComponentsResultMap" type="com.allcore.main.code.source.entity.PvComponents">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="pv_area_id" property="pvAreaId"/>
        <result column="pv_string_id" property="pvStringId"/>
        <result column="device_name" property="deviceName"/>
        <result column="component_number" property="componentNumber"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
        <result column="coordinates" property="coordinates"/>
        <result column="put_date" property="putDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectPvComponentsPage" resultMap="pvComponentsResultMap">
        select  id,device_name,component_number,longitude, latitude ,elevation,sort_order from main_pv_components where is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like #{deviceQuery.deviceName}
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like #{deviceQuery.deptCode}
        </if>
        <if test="null != deviceQuery.pvStringId and '' != deviceQuery.pvStringId">
            and pv_string_id = #{deviceQuery.pvStringId}
        </if>
        order by device_name
        LIMIT #{limit} OFFSET #{offset}
    </select>
    <select id="countComponents" resultType="int">
        SELECT COUNT(*)
        FROM main_pv_components
        WHERE is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like #{deviceQuery.deviceName}
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like #{deviceQuery.deptCode}
        </if>
        <if test="null != deviceQuery.pvStringId and '' != deviceQuery.pvStringId">
            and pv_string_id = #{deviceQuery.pvStringId}
        </if>
    </select>
    <select id="getPvDeviceDetails"
            resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            b.pv_string_id as parent_device_id,
            b.id as device_id,
            a.device_name AS parent_device_name,
            b.device_name
        FROM
            main_pv_string a
                LEFT JOIN main_pv_components b ON a.id = b.pv_string_id
        WHERE
            a.is_deleted = 0 and b.is_deleted = 0
            and a.pv_area_id IN
            <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
    <select id="componentsNum" resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
        pv_area_id,
        pv_string_id,
        count( 1 ) AS num
        FROM
        main_pv_components
        WHERE
        is_deleted = 0
        AND pv_area_id IN
        <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY
        pv_area_id,pv_string_id
    </select>
    <select id="componentsTagNum" resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            a.pv_area_id,
            a.pv_string_id,
            count( 1 ) AS num
        FROM
            main_pv_components a
                inner join main_inspection_picture_tagging b on a.id = b.pv_component_id
        WHERE
            a.is_deleted = 0
          and b.is_deleted = 0
          AND a.pv_area_id IN
        <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY
            a.pv_area_id,a.pv_string_id
    </select>

</mapper>
