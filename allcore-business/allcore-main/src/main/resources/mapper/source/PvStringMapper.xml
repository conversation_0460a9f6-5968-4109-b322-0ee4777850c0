<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.PvStringMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pvStringResultMap" type="com.allcore.main.code.source.entity.PvString">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="pv_area_id" property="pvAreaId"/>
        <result column="device_name" property="deviceName"/>
        <result column="string_number" property="stringNumber"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
        <result column="coordinates" property="coordinates"/>
        <result column="put_date" property="putDate"/>
        <result column="dept_code" property="deptCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectPvStringPage" resultMap="pvStringResultMap">
        select * from main_pv_string where is_deleted = 0
        <if test="null != deviceQuery.deviceName and '' != deviceQuery.deviceName">
            and device_name like '%' || #{deviceQuery.deviceName} || '%'
        </if>
        <if test="null != deviceQuery.deptCode and '' != deviceQuery.deptCode">
            and dept_code like #{deviceQuery.deptCode} || '%'
        </if>
        <if test="null != deviceQuery.pvAreaId and '' != deviceQuery.pvAreaId">
            and pv_area_Id = #{deviceQuery.pvAreaId}
        </if>
        order by device_name
    </select>
    <select id="selectPvstringVoList" resultType="com.allcore.main.code.source.vo.PvStringVO">
       select mpt.*,mpa.device_name as pvAreaName
       from main_pv_string mpt
       left join main_pv_area mpa on mpa.id = mpt.pv_area_id and mpa.is_deleted = 0
       where  mpt.is_deleted = 0
       <if test="dto.pvStringIds != null and dto.pvStringIds.size() > 0">
           and mpt.id in
           <foreach collection="dto.pvStringIds" item="pvStringId" open="("  close=")" separator=",">
               #{pvStringId}
           </foreach>
       </if>
    </select>

    <select id="selectPvStringTree" resultType="com.allcore.main.code.common.vo.PVTreeNode">
        select id as id,
               device_name as title
        from main_pv_string where is_deleted = 0
        <if test="deptCode != null and deptCode.trim() !='' ">
            and dept_code = #{deptCode}
        </if>
    </select>


</mapper>
