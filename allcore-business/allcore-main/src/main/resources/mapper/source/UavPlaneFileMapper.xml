<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.source.mapper.UavPlaneFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="uavPlaneFileResultMap" type="com.allcore.main.code.source.entity.UavPlaneFile">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="plane_id" property="planeId"/>
        <result column="plane_file_type" property="planeFileType"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectUavPlaneFilePage" resultMap="uavPlaneFileResultMap">
        select * from main_uav_plane_file where is_deleted = 0
    </select>

</mapper>
