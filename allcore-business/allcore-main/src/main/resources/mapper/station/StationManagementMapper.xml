<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.station.mapper.StationManagementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="stationManagementResultMap" type="com.allcore.main.code.station.entity.StationManagement">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="station_install" property="stationInstall"/>
        <result column="ave_station_height" property="aveStationHeight"/>
        <result column="power_station_type" property="powerStationType"/>
        <result column="component_manufacturer_power" property="componentManufacturerPower"/>
        <result column="component_angle" property="componentAngle"/>
        <result column="connection_time" property="connectionTime"/>
        <result column="install_quantity" property="installQuantity"/>
        <result column="station_address" property="stationAddress"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_code" property="deptCode"/>
        <result column="camera_height" property="cameraHeight"/>
        <result column="terrain_exaggeration" property="terrainExaggeration"/>
    </resultMap>


    <select id="selectStationManagementPage" resultType="com.allcore.main.code.station.vo.StationManagementVO">
        SELECT t1.id,
        t1.id as deptId,
        t1.dept_code,
        t1.dept_name,
        t1.dept_name as shortName,
        t1.full_name,
        t1.capacity as stationInstall,
        t2.connection_time,
        t2.install_quantity,
        t2.power_station_type,
        t2.component_manufacturer_power,
        t2.component_angle,
        t2.ave_station_height,
        t2.terrain_exaggeration,
        t2.camera_height,
        t1.address as stationAddress
        FROM sys_dept t1
        LEFT JOIN main_station_management t2 ON t1.id = t2.dept_id and t2.is_deleted = 0
        where t1.is_deleted = 0
         <if test=" dto.deptCategory != null and dto.deptCategory != ''">
          and t1.dept_category = #{dto.deptCategory}
         </if>
        <if test="null != dto.connectionTime and '' != dto.connectionTime">
            and t2.connection_time = #{dto.connectionTime}
        </if>
        <if test="null != dto.fullName and '' != dto.fullName">
            and t1.full_name like CONCAT('%', #{dto.fullName}, '%')
        </if>
        <if test="null != dto.deptCode and '' != dto.deptCode">
            and t1.dept_code like CONCAT(#{dto.deptCode}, '%')
        </if>
        <if test="null != dto.stationInstall and '' != dto.stationInstall">
            and t1.capacity = #{dto.stationInstall}
        </if>
        <if test="null != dto.installQuantity and '' != dto.installQuantity">
            and t2.install_quantity = #{dto.installQuantity}
        </if>
    </select>
    <select id="selectRealName" resultType="com.allcore.main.code.source.vo.RealNameVO">
        SELECT t1.dept_id as deptId,
        LISTAGG(t1.real_name, ', ') WITHIN GROUP (ORDER BY t1.real_name) as stationHead
        FROM sys_user t1
        LEFT JOIN sys_role t2 ON t1.role_id = t2.id
        WHERE 1 = 1
        AND t1.is_deleted = 0
        AND t2.role_name = '总部管理员'
        AND t1.dept_id IN
        <foreach collection="deptIds" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
        GROUP BY t1.dept_id
    </select>
    <select id="getAveStationHeight" resultType="java.lang.String">
        select ave_station_height from main_station_management where dept_code = #{deptCode} and is_deleted = 0
    </select>

</mapper>
