<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionTaskResultMap" type="com.allcore.main.code.inspection.vo.InspectionTaskVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_name" property="planName"/>
        <result column="inspection_task_name" property="inspectionTaskName"/>
        <result column="inspection_task_no" property="inspectionTaskNo"/>
        <result column="inspection_type" property="inspectionType"/>
        <result column="inspection_method" property="inspectionMethod"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="use_airspace" property="useAirspace"/>
        <result column="inspection_area_guid" property="inspectionAreaGuid"/>
        <result column="inspection_task_status" property="inspectionTaskStatus"/>
        <result column="plan_not_flag" property="planNotFlag"/>
        <result column="other_workers" property="otherWorkers"/>
        <result column="dept_code" property="deptCode"/>
        <result column="device_type" property="deviceType"/>
        <result column="responsible_user" property="responsibleUser"/>
        <result column="smart_process" property="smartProcess"/>
        <result column="process_content" property="processContent"/>
        <result column="cancel_time" property="cancelTime"/>
        <!--<collection property="deviceDetailList" javaType="java.util.List" resultMap="inspectionDeviceDetailResultMap"/>
        <collection property="inspectionUserList" javaType="java.util.List" resultMap="inspectionUserResultMap"/>-->
        <collection property="inspectionMethodDetailList" javaType="java.util.List"
                    resultMap="inspectionMethodDetailResultMap"/>
    </resultMap>
    <resultMap id="inspectionResultTaskResultMap" type="com.allcore.main.code.inspection.vo.InspectionTaskVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="plan_id" property="planId"/>
        <result column="inspection_task_name" property="inspectionTaskName"/>
        <result column="inspection_task_no" property="inspectionTaskNo"/>
        <result column="inspection_type" property="inspectionType"/>
        <result column="inspection_method" property="inspectionMethod"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="use_airspace" property="useAirspace"/>
        <result column="inspection_task_status" property="inspectionTaskStatus"/>
        <result column="plan_not_flag" property="planNotFlag"/>
        <result column="other_workers" property="otherWorkers"/>
        <result column="dept_code" property="deptCode"/>
        <result column="device_type" property="deviceType"/>
        <result column="recognitionTaskId" property="recognitionTaskId"/>
        <result column="recognition_task_status" property="recognitionTaskStatus"/>
        <result column="responsible_user" property="responsibleUser"/>
        <!--<collection property="inspectionUserList" javaType="java.util.List" resultMap="inspectionUserResultMap"/>
        <collection property="inspectionPicList" javaType="java.util.List" resultMap="inspectionPictureResultVoMap"/>-->
    </resultMap>

    <resultMap id="inspectionPictureResultVoMap" type="com.allcore.main.code.inspection.vo.InspectionPictureVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="recognition_task_id" property="recognitionTaskId"/>
        <result column="group_id" property="groupId"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="thumbnail_file_guid" property="thumbnailFileGuid"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_part" property="devicePart"/>
        <result column="pic_audit_status" property="picAuditStatus"/>
        <result column="pic_defect_flg" property="picDefectFlg"/>
        <result column="defect_pic_type" property="defectPicType"/>
        <result column="is_export" property="isExport"/>
        <result column="export_time" property="exportTime"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>
    <resultMap id="inspectionMethodDetailResultMap"
               type="com.allcore.main.code.inspection.vo.InspectionMethodDetailVO">
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_device_id" property="inspectionDeviceId"/>
        <result column="model_id" property="modelId"/>
        <result column="uav_type" property="uavType"/>
        <result column="inspectionDeviceName" property="inspectionDeviceName"/>
    </resultMap>

    <resultMap id="inspectionUserResultMap" type="com.allcore.main.code.inspection.vo.InspectionUserVO">
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="user_id" property="userId"/>
        <result column="userName" property="userName"/>
    </resultMap>

    <resultMap id="inspectionDeviceDetailResultMap"
               type="com.allcore.main.code.inspection.vo.InspectionDeviceDetailVO">
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_device_type_id" property="inspectionDeviceTypeId"/>
        <result column="device_id" property="deviceId"/>
        <result column="parent_device_id" property="parentDeviceId"/>
        <result column="device_level" property="deviceLevel"/>
        <result column="route_file_id" property="routeFileId"/>
    </resultMap>


    <select id="selectInspectionTaskPage" resultMap="inspectionTaskResultMap">
        select *,
        CASE
        WHEN a.inspection_method = 'UAV' THEN (SELECT t2.plane_name FROM main_uav_plane t2 WHERE t2.id =
        e.inspection_device_id)
        ELSE (SELECT t3.airport_nest_name FROM main_airport t3 WHERE t3.id = e.inspection_device_id)
        END AS inspectionDeviceName
        from main_inspection_task a
        left join main_inspection_device_type b ON a.id = b.inspection_task_id
        left join main_inspection_method_detail e ON a.id = e.inspection_task_id
        where a.is_deleted = 0
        <if test="info.inspectionType != null and info.inspectionType != ''">
            and a.inspection_type = #{info.inspectionType}
        </if>
        <if test="info.deptCode != null and info.deptCode != ''">
            and a.dept_code like concat(#{info.deptCode}, '%')
        </if>
        <if test="info.inspectionTaskName != null and info.inspectionTaskName != ''">
            and a.inspection_task_name like concat('%',#{info.inspectionTaskName}, '%')
        </if>
        <if test="info.startDate != null and info.startDate != ''">
            and #{info.startDate} &lt;= a.start_date
            and #{info.endDate} &gt;= a.start_date
        </if>
        <if test="info.inspectionMethod != null and info.inspectionMethod != ''">
            and a.inspection_method = #{info.inspectionMethod}
        </if>
        <if test="info.inspectionTaskNo != null and info.inspectionTaskNo != ''">
            and a.inspection_task_no = #{info.inspectionTaskNo}
        </if>
        <if test="info.inspectionTaskStatus != null and info.inspectionTaskStatus != ''">
            and a.inspection_task_status = #{info.inspectionTaskStatus}
        </if>
        <if test="info.deviceType != null and info.deviceType != ''">
            and b.device_type = #{info.deviceType}
        </if>
        order by a.create_time desc

    </select>
    <select id="selectInspectionTaskOne" resultMap="inspectionTaskResultMap">
        select *,f.plan_name ,
               CASE
                   WHEN a.inspection_method = 'UAV' THEN (SELECT t2.plane_name FROM main_uav_plane t2 WHERE t2.id =
                                                                                                            e.inspection_device_id)
                   ELSE (SELECT t3.airport_nest_name FROM main_airport t3 WHERE t3.id = e.inspection_device_id)
                   END AS inspectionDeviceName
        from main_inspection_task a
                 left join main_inspection_device_type b ON a.id = b.inspection_task_id
                 left join main_inspection_method_detail e ON a.id = e.inspection_task_id
                 left join main_plan f ON f.id = a.plan_id
        where a.is_deleted = 0
          and a.id = #{id}

    </select>
    <select id="selectInspectionResultTaskPage" resultMap="inspectionResultTaskResultMap">
        select a.*,b.*,c.id as recognitionTaskId,c.recognition_task_status
        from main_inspection_task a
        left join main_inspection_device_type b ON a.id = b.inspection_task_id
        inner join main_recognition_task c on a.id = c.inspection_task_id
        where a.is_deleted = 0
        <if test="info.inspectionType != null and info.inspectionType != ''">
            and a.inspection_type = #{info.inspectionType}
        </if>
        <if test="info.deptCode != null and info.deptCode != ''">
            and a.dept_code like concat(#{info.deptCode}, '%')
        </if>
        <if test="info.inspectionTaskName != null and info.inspectionTaskName != ''">
            and a.inspection_task_name like concat('%',#{info.inspectionTaskName}, '%')
        </if>
        <if test="info.startDate != null and info.startDate != ''">
            and #{info.startDate} &lt;= a.start_date
            and #{info.endDate} &gt;= a.start_date
        </if>
        <if test="info.inspectionMethod != null and info.inspectionMethod != ''">
            and a.inspection_method = #{info.inspectionMethod}
        </if>
        <if test="info.inspectionTaskNo != null and info.inspectionTaskNo != ''">
            and a.inspection_task_no = #{info.inspectionTaskNo}
        </if>
        <if test="info.inspectionTaskStatus != null and info.inspectionTaskStatus != ''">
            and a.inspection_task_status = #{info.inspectionTaskStatus}
        </if>
        <if test="info.deviceType != null and info.deviceType != ''">
            and b.device_type = #{info.deviceType}
        </if>
        order by a.create_time desc

    </select>

    <select id="selectForProgress" resultMap="inspectionTaskResultMap">
        select *
        from main_inspection_task a
        where a.is_deleted = 0
          and a.inspection_task_status = '3'
          and ((a.start_date <![CDATA[ <= ]]> #{dto.endDate}
            and a.start_date >= #{dto.startDate})
            or (
                           a.end_date <![CDATA[ <= ]]> #{dto.endDate}
                       and a.end_date >= #{dto.startDate}
                   ))
          and a.id in (select b.inspection_task_id
                       from main_inspection_device_detail b
                       where 1 = 1
                         and b.device_id = #{dto.deviceId})

    </select>
    <select id="checkTrack" resultType="java.lang.Integer">
        select count(1) from machine_nest_task a left join machine_nest_task_detail b on a.id = b.nest_task_id
                        where a.is_deleted = 0 and a.airport_nest_id !=#{airportNestId}
                          and a.task_status = '1'
                          and b.device_id = #{deviceId}
    </select>

    <select id="getLastTask" resultType="java.lang.String">
        select a.id from main_inspection_task a,main_inspection_method_detail b
        where a.id = b.inspection_task_id and b.inspection_device_id = #{airportNestId}
          and a.is_deleted = 0 and inspection_type = #{deviceType}
        order by a.create_time desc limit 1
    </select>
<!--    <select id="getNearByWorkOrder" resultType="com.allcore.main.code.solve.vo.NearByWorkOrderVO">-->
<!--        SELECT-->
<!--        t.id AS inspectionTaskId,-->
<!--        t.inspection_task_name,-->
<!--        t.inspection_task_no,-->
<!--        t.inspection_task_status,-->
<!--        t.start_date,-->
<!--        t.end_date,-->
<!--        latlon.device_type,-->
<!--        latlon.device_name AS device_name,-->
<!--        latlon.latitude,-->
<!--        latlon.longitude,-->
<!--        u.user_id AS inspectionUserId-->
<!--        FROM-->
<!--        main_inspection_task t-->
<!--        LEFT JOIN-->
<!--        (-->
<!--        SELECT-->
<!--        t.id AS id,-->
<!--        dt.device_type AS device_type,-->
<!--        dd.device_id AS device_id,-->
<!--        (-->
<!--        CASE-->
<!--        WHEN (dt.device_type = 'TMS_LINE' AND tw.latitude IS NOT NULL AND tw.longitude IS NOT NULL)-->
<!--        THEN tw.latitude-->
<!--        WHEN (dt.device_type = 'FAN' AND fan.latitude IS NOT NULL AND fan.longitude IS NOT NULL)-->
<!--        THEN fan.latitude-->
<!--        WHEN (dt.device_type = 'PV' AND pv.latitude IS NOT NULL AND pv.longitude IS NOT NULL)-->
<!--        THEN pv.latitude-->
<!--        ELSE NULL-->
<!--        END-->
<!--        ) AS latitude,-->
<!--        (-->
<!--        CASE-->
<!--        WHEN (dt.device_type = 'TMS_LINE' AND tw.latitude IS NOT NULL AND tw.longitude IS NOT NULL)-->
<!--        THEN tw.longitude-->
<!--        WHEN (dt.device_type = 'FAN' AND fan.latitude IS NOT NULL AND fan.longitude IS NOT NULL)-->
<!--        THEN fan.longitude-->
<!--        WHEN (dt.device_type = 'PV' AND pv.latitude IS NOT NULL AND pv.longitude IS NOT NULL)-->
<!--        THEN pv.longitude-->
<!--        ELSE NULL-->
<!--        END-->
<!--        ) AS longitude,-->
<!--        (-->
<!--        CASE-->
<!--        WHEN (dt.device_type = 'TMS_LINE' AND tw.tower_name IS NOT NULL)-->
<!--        THEN tw.tower_name-->
<!--        WHEN (dt.device_type = 'FAN' AND fan.device_name IS NOT NULL)-->
<!--        THEN fan.device_name-->
<!--        WHEN (dt.device_type = 'PV' AND pv.device_name IS NOT NULL)-->
<!--        THEN pv.device_name-->
<!--        ELSE NULL-->
<!--        END-->
<!--        ) AS device_name-->
<!--        FROM-->
<!--        main_inspection_task t-->

<!--        left JOIN-->
<!--        main_inspection_device_detail dd on t.id=dd.inspection_task_id-->
<!--        left JOIN-->
<!--        main_inspection_device_type dt on dd.inspection_device_type_id=dt.id-->
<!--        LEFT JOIN-->
<!--        main_tower tw ON dd.device_id = tw.id AND dt.device_type = 'TMS_LINE'-->
<!--        LEFT JOIN-->
<!--        main_fan fan ON dd.device_id = fan.id AND dt.device_type = 'FAN'-->
<!--        LEFT JOIN-->
<!--        main_pv_area pv ON dd.device_id = pv.id AND dt.device_type = 'PV'-->
<!--        GROUP BY-->
<!--        t.id-->
<!--        ) latlon ON latlon.id = t.id-->

<!--        left join main_inspection_user u on t.id=u.inspection_task_id-->
<!--        WHERE-->
<!--            t.is_deleted = 0-->
<!--        and-->
<!--            t.inspection_task_status = 1-->
<!--        <if test="deviceInfoVO.removeUser != null and deviceInfoVO.removeUser != ''">-->
<!--          and u.user_id=#{deviceInfoVO.removeUser}-->
<!--        </if>-->
<!--        AND latlon.latitude IS NOT NULL-->
<!--        AND latlon.longitude IS NOT NULL-->
<!--        AND <![CDATA[-->
<!--    (-->
<!--        6371000 * ACOS(-->
<!--            COS(RADIANS(#{deviceInfoVO.latitude})) * COS(RADIANS(latlon.latitude)) * COS(RADIANS(#{deviceInfoVO.longitude}) - RADIANS(latlon.longitude)) +-->
<!--            SIN(RADIANS(#{deviceInfoVO.latitude})) * SIN(RADIANS(latlon.latitude))-->
<!--        )-->
<!--    ) <= 500-->
<!--]]>-->
<!--    </select>-->


    <!-- ... 现有代码 ... -->
    <select id="getNearByWorkOrder" resultType="com.allcore.main.code.solve.vo.NearByWorkOrderVO">
        SELECT DISTINCT
        t.id AS inspectionTaskId,
        t.inspection_task_name,
        t.inspection_task_no,
        t.inspection_task_status,
        t.dept_code,
        t.start_date,
        t.end_date,
        latlon.device_type,
        latlon.device_name AS device_name,
        latlon.latitude,
        latlon.longitude,
        t.responsible_user,
        md.inspection_device_id
        FROM
        main_inspection_task t
        LEFT JOIN
        (
        SELECT
        t.id AS id,
        dt.device_type AS device_type,
        dd.device_id AS device_id,
        (
        CASE
        WHEN (dt.device_type = 'TMS_LINE' AND tw.latitude IS NOT NULL AND tw.longitude IS NOT NULL)
        THEN tw.latitude
        WHEN (dt.device_type = 'FAN' AND fan.latitude IS NOT NULL AND fan.longitude IS NOT NULL)
        THEN fan.latitude
        WHEN (dt.device_type = 'PV' AND pv.latitude IS NOT NULL AND pv.longitude IS NOT NULL)
        THEN pv.latitude
        ELSE NULL
        END
        ) AS latitude,
        (
        CASE
        WHEN (dt.device_type = 'TMS_LINE' AND tw.latitude IS NOT NULL AND tw.longitude IS NOT NULL)
        THEN tw.longitude
        WHEN (dt.device_type = 'FAN' AND fan.latitude IS NOT NULL AND fan.longitude IS NOT NULL)
        THEN fan.longitude
        WHEN (dt.device_type = 'PV' AND pv.latitude IS NOT NULL AND pv.longitude IS NOT NULL)
        THEN pv.longitude
        ELSE NULL
        END
        ) AS longitude,
        (
        CASE
        WHEN (dt.device_type = 'TMS_LINE' AND tw.tower_name IS NOT NULL)
        THEN tw.tower_name
        WHEN (dt.device_type = 'FAN' AND fan.device_name IS NOT NULL)
        THEN fan.device_name
        WHEN (dt.device_type = 'PV' AND pv.device_name IS NOT NULL)
        THEN pv.device_name
        ELSE NULL
        END
        ) AS device_name
        FROM
        main_inspection_task t
        LEFT JOIN
        main_inspection_device_detail dd on t.id=dd.inspection_task_id
        LEFT JOIN
        main_inspection_device_type dt on dd.inspection_device_type_id=dt.id
        LEFT JOIN
        main_tower tw ON dd.device_id = tw.id AND dt.device_type = 'TMS_LINE'
        LEFT JOIN
        main_fan fan ON dd.device_id = fan.id AND dt.device_type = 'FAN'
        LEFT JOIN
        main_pv_area pv ON dd.device_id = pv.id AND dt.device_type = 'PV'
        ) latlon ON latlon.id = t.id
        LEFT JOIN
        main_inspection_method_detail md on t.id=md.inspection_task_id
        WHERE
        t.is_deleted = 0
        AND
        t.inspection_task_status = '0'
        AND latlon.latitude IS NOT NULL
        AND latlon.longitude IS NOT NULL
        AND EXISTS (
        SELECT 1 FROM (
        <foreach collection="deviceInfoVOs" item="deviceInfoVO" separator="UNION ALL">
            SELECT #{deviceInfoVO.removeUser} as removeUser, #{deviceInfoVO.latitude} as lat, #{deviceInfoVO.longitude} as lng
        </foreach>
        ) as device_info
        WHERE
        ( t.responsible_user = device_info.removeUser)
        AND <![CDATA[
            (
                6371000 * ACOS(
                    COS(RADIANS(device_info.lat)) * COS(RADIANS(latlon.latitude)) * COS(RADIANS(device_info.lng) - RADIANS(latlon.longitude)) +
                    SIN(RADIANS(device_info.lat)) * SIN(RADIANS(latlon.latitude))
                )
            ) <= 500
            ]]>
        )
    </select>

    <!-- ... 现有代码 ... -->
</mapper>
