<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionUserResultMap" type="com.allcore.main.code.inspection.entity.InspectionUser">
        <result column="id" property="id"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="user_id" property="userId"/>
    </resultMap>


    <select id="selectInspectionUserPage" resultMap="inspectionUserResultMap">
        select * from main_inspection_user where is_deleted = 0
    </select>

</mapper>
