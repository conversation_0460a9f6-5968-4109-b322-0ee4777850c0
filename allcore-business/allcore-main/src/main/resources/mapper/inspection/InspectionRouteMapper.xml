<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionRouteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionRouteResultMap" type="com.allcore.main.code.inspection.entity.InspectionRoute">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="dept_code" property="deptCode"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="route_name" property="routeName"/>
        <result column="route_code" property="routeCode"/>
        <result column="station_id" property="stationId"/>
        <result column="station_name" property="stationName"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_count" property="deviceCount"/>
        <result column="route_description" property="routeDescription"/>
        <result column="dept_id" property="deptId"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="checkRouteNameExists" resultType="java.lang.Integer">
        select count(1)
        from main_inspection_route
        where
            route_name = #{routeName}
          and station_id = #{stationId}
          and device_type = #{deviceType}
          and is_deleted = 0
        <if test="excludeId != null and excludeId != ''">
            and id != #{excludeId}
        </if>
    </select>
    <select id="getMaxRouteCode" resultType="java.lang.String">
        select max(route_code)
        FROM main_inspection_route
        where is_deleted=0
        and route_code like #{prefix}
    </select>
    <select id="selectInspectionRoutePage" resultType="com.allcore.main.code.inspection.vo.InspectionRouteVO">
        select mir.*
        from main_inspection_route mir
        where is_deleted = 0
        <if test="dto.routeName != null and dto.routeName != ''">
            and route_name like concat('%',#{dto.routeName},'%')
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            and device_type = #{dto.deviceType}
        </if>
    </select>


</mapper>