<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.PlanDeviceDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="planDeviceDetailResultMap" type="com.allcore.main.code.inspection.entity.PlanDeviceDetail">
        <result column="id" property="id"/>
        <result column="plan_guid" property="planId"/>
        <result column="plan_device_type_guid" property="planDeviceTypeId"/>
        <result column="device_guid" property="deviceId"/>
        <result column="parent_device_guid" property="parentDeviceId"/>
        <result column="device_level" property="deviceLevel"/>
    </resultMap>


    <select id="selectPlanDeviceDetailPage" resultMap="planDeviceDetailResultMap">
        select * from main_plan_device_detail where is_deleted = 0
    </select>

</mapper>
