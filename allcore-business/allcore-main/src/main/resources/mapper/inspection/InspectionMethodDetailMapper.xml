<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionMethodDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionMethodDetailResultMap" type="com.allcore.main.code.inspection.entity.InspectionMethodDetail">
        <result column="id" property="id"/>
        <result column="inspection_id" property="inspectionId"/>
        <result column="device_id" property="deviceId"/>
    </resultMap>


    <select id="selectInspectionMethodDetailPage" resultMap="inspectionMethodDetailResultMap">
        select * from main_inspection_method_detail where is_deleted = 0
    </select>

</mapper>
