<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.PlanDeviceTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="planDeviceTypeResultMap" type="com.allcore.main.code.inspection.entity.PlanDeviceType">
        <result column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>


    <select id="selectPlanDeviceTypePage" resultMap="planDeviceTypeResultMap">
        select * from main_plan_device_type where is_deleted = 0
    </select>

</mapper>
