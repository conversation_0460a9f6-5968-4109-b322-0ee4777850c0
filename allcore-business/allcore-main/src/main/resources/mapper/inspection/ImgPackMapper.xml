<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.ImgPackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="imgPackResultMap" type="com.allcore.main.code.inspection.entity.ImgPack">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="remark" property="remark"/>
        <result column="type" property="type"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectImgPackPage" resultMap="imgPackResultMap">
        select * from main_img_pack where is_deleted = 0 and status = 1
        <if test="info.deviceType != null and info.deviceType != ''">
            and device_type = #{info.deviceType}
        </if>
        <if test="info.uploadTime != null and info.uploadTime != ''">
            and upload_time = #{info.uploadTime}
        </if>
        <if test="info.deviceType != null and info.deviceType != ''">
            and device_type = #{info.deviceType}
        </if>
        <if test="null != info.deptCode and '' != info.deptCode">
            and dept_code like concat(#{info.deptCode},'%')
        </if>
        ORDER BY
        create_time DESC
    </select>

</mapper>
