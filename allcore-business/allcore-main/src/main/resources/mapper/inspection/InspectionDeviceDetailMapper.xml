<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionDeviceDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionDeviceDetailResultMap" type="com.allcore.main.code.inspection.entity.InspectionDeviceDetail">
        <result column="id" property="id"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_device_type_id" property="inspectionDeviceTypeId"/>
        <result column="device_id" property="deviceId"/>
        <result column="parent_device_id" property="parentDeviceId"/>
        <result column="device_level" property="deviceLevel"/>
        <result column="route_file_id" property="routeFileId"/>
    </resultMap>


    <select id="selectInspectionDeviceDetailPage" resultMap="inspectionDeviceDetailResultMap">
        select * from main_inspection_device_detail where is_deleted = 0
    </select>
    <select id="getLineInspectionDeviceDetails"
            resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            a.device_id,
            b.id AS parent_device_id,
            c.tower_name AS device_name,
            b.line_name AS parent_device_name,
            a.device_level
        FROM
            main_inspection_device_detail a
            LEFT JOIN main_tower c ON a.device_id = c.id
            LEFT JOIN main_line b ON c.line_id = b.id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}
        order by c.tower_name
    </select>

    <select id="getFanInspectionDeviceDetails"
            resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            a.device_id,
            b.device_name,
            b.manufacturer,
            b.model,
            b.blade_manufacturer
        FROM
            main_inspection_device_detail a
                LEFT JOIN main_fan b ON a.device_id = b.id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}

    </select>

    <select id="getPvInspectionDeviceDetails"
            resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            a.device_id,
            b.device_name,
            b.coordinates,
            a.inspection_report_pic
        FROM
            main_inspection_device_detail a
                LEFT JOIN main_pv_area b ON a.device_id = b.id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}
        order by b.device_name
    </select>

    <select id="listWithType" resultType="com.allcore.main.code.inspection.dto.InspectionDeviceDetailWithType">
        SELECT b.line_id AS parentId,
               c.inspection_type AS inspectionType,
               d.device_type AS deviceType,
               a.*
        FROM main_inspection_device_detail a
                 LEFT JOIN main_tower b ON a.device_id = b.id
                 LEFT JOIN main_inspection_task c ON a.inspection_task_id = c.id
                 LEFT JOIN main_inspection_device_type d ON a.inspection_task_id = d.inspection_task_id
        WHERE a.inspection_task_id = #{inspectionTaskId};
    </select>

    <select id="countDeviceByPlanIdAndRemoveIds" resultType="long">
        select count(1) from main_inspection_device_detail b
        left join main_inspection_task a
        on a.id = b.inspection_task_id
        where
        a.is_deleted = 0 and
        a.plan_id = #{planId,jdbcType=VARCHAR}
        and b.device_id in
        <foreach item="item" index="index" collection="removeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
