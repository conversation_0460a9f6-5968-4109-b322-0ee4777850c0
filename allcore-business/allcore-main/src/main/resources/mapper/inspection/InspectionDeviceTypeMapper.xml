<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionDeviceTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionDeviceTypeResultMap" type="com.allcore.main.code.inspection.entity.InspectionDeviceType">
        <result column="id" property="id"/>
        <result column="inspection_task_guid" property="inspectionTaskGuid"/>
        <result column="inspection_device_type_guid" property="inspectionDeviceTypeGuid"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>


    <select id="selectInspectionDeviceTypePage" resultMap="inspectionDeviceTypeResultMap">
        select * from main_inspection_device_type where is_deleted = 0
    </select>

</mapper>
