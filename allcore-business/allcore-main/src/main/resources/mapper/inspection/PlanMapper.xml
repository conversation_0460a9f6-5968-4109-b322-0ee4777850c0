<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.PlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="planResultMap" type="com.allcore.main.code.inspection.vo.PlanVO">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="plan_name" property="planName"/>
        <result column="parent_plan_id" property="parentPlanId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="plan_type" property="planType"/>
        <result column="device_type" property="deviceType"/>
        <result column="plan_status" property="planStatus"/>
        <result column="dept_code" property="deptCode"/>
        <result column="progress" property="progress"/>
        <collection  property="deviceDetailList"  javaType="java.util.List"  resultMap="GenTableColumnResult" />
    </resultMap>
    <resultMap id="GenTableColumnResult" type="com.allcore.main.code.inspection.vo.PlanDeviceDetailVO">
        <result column="plan_id" property="planId"/>
        <result column="plan_device_type_id" property="planDeviceTypeId"/>
        <result column="device_id" property="deviceId"/>
        <result column="parent_device_id" property="parentDeviceId"/>
        <result column="device_level" property="deviceLevel"/>
    </resultMap>


    <select id="selectPlanPage" resultMap="planResultMap">
        SELECT
        a.id,
        a.dept_code ,
        a.start_date,
        a.end_date ,
        a.plan_name ,
        b.device_type,
        a.plan_status,
        a.create_user,
        a.progress
        FROM
        main_plan a
        left JOIN main_plan_device_type b ON a.id = b.plan_id
        WHERE
        a.is_deleted = 0 and a.id in(
        SELECT
        t1.id
        from main_plan t1
        INNER JOIN main_plan_device_type t2 on t1.id = t2.plan_id
        INNER JOIN main_plan_device_detail t3 on t1.id = t3.plan_id
        where t1.is_deleted = 0
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and t1.dept_code like concat(#{dto.deptCode}, '%')
        </if>
        <if test="dto.endDate != null">
            and t1.start_date &lt;= #{dto.endDate}
        </if>
        <if test="dto.startDate != null">
            and t1.end_date &gt;=  #{dto.startDate}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            and t2.device_type = #{dto.deviceType}
        </if>
        <if test="dto.planName != null and dto.planName != ''">
            and t1.plan_name like concat('%',#{dto.planName},'%')
        </if>
        <if test="dto.deviceId != null and dto.deviceId != ''">
            and t3.device_id like concat('%',#{dto.deviceId},'%')
        </if>
        )
        ORDER BY
        a.create_time DESC
    </select>
    <select id="selectPlanList" resultMap="planResultMap">
        SELECT a.id,
               a.dept_code,
               a.plan_name
        FROM main_plan a
                 left JOIN main_plan_device_type b ON a.id = b.plan_id
        WHERE a.is_deleted = 0
          and a.dept_code like concat(#{dto.deptCode}, '%')
          and b.device_type = #{dto.deviceType}
          and a.plan_status not in ('9','8')
    </select>
</mapper>
