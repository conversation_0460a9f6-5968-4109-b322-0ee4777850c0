<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionPictureTaggingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionPictureTaggingResultMap"
               type="com.allcore.main.code.inspection.entity.InspectionPictureTagging">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="inspection_picture_id" property="inspectionPictureId"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_task_no" property="inspectionTaskNo"/>
        <result column="recognition_task_id" property="recognitionTaskId"/>
        <result column="component" property="component"/>
        <result column="component_type" property="componentType"/>
        <result column="part" property="part"/>
        <result column="defect_description" property="defectDescription"/>
        <result column="defect_level" property="defectLevel"/>
        <result column="xmin" property="xmin"/>
        <result column="ymin" property="ymin"/>
        <result column="xmax" property="xmax"/>
        <result column="ymax" property="ymax"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="big_file_guid" property="bigFileGuid"/>
        <result column="defect_name" property="defectName"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="defect_type" property="defectType"/>
        <result column="tagging_type" property="taggingType"/>
        <result column="defect_key" property="defectKey"/>
        <result column="light_defect_key" property="lightDefectKey"/>
        <result column="is_algorithm" property="isAlgorithm"/>
        <result column="move_position" property="movePosition"/>
        <result column="eliminate_status" property="eliminateStatus"/>
        <result column="system_type" property="systemType"/>
        <result column="examine" property="examine"/>
        <result column="pv_component_id" property="pvComponentId"/>
        <result column="is_create_report" property="isCreateReport"/>
        <result column="temperature" property="temperature"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectInspectionPictureTaggingPage" resultMap="inspectionPictureTaggingResultMap">
        select *
        from main_inspection_picture_tagging
        where is_deleted = 0
    </select>
    <select id="getLineRemoveTaskDetail"
            resultType="com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO">
        SELECT a.id,
               a.defect_description,
               a.defect_level,
               a.big_file_guid as file_guid,
               b.device_id,
               c.tower_name as device_name
        FROM main_inspection_picture_tagging a,
             main_inspection_picture b,
             main_tower c
        WHERE a.id = #{inspectionPictureTaggingId}
          and a.inspection_picture_id = b.id
          and b.device_id = c.id
    </select>
    <select id="getPvRemoveTaskDetail"
            resultType="com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO">
        SELECT
            a.id,
            a.defect_description,
            a.defect_level,
            a.big_file_guid AS file_guid,
            b.device_id,
            c.device_name,
            a.lightFileGuid
        FROM
            (
                SELECT
                    a.id,
                    a.defect_description,
                    a.defect_level,
                    a.big_file_guid,
                    a.inspection_picture_id,
                    e.big_file_guid AS lightFileGuid
                FROM
                    main_inspection_picture_tagging a
                        LEFT JOIN (select distinct defect_key,big_file_guid from main_inspection_picture_tagging where device_type = 'PV'  GROUP BY defect_key) e ON a.light_defect_key = e.defect_key
                WHERE
                    a.id = #{inspectionPictureTaggingId}
            ) a,
            main_inspection_picture b,
            main_pv_area c
        WHERE
            a.inspection_picture_id = b.id
          AND b.device_id = c.id

    </select>
    <select id="getFanRemoveTaskDetail"
            resultType="com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO">
        SELECT a.id,
               a.defect_description,
               a.defect_level,
               a.big_file_guid as file_guid,
               b.device_id,
               c.device_name
        FROM main_inspection_picture_tagging a,
             main_inspection_picture b,
             main_fan c
        WHERE a.id = #{inspectionPictureTaggingId}
          and a.inspection_picture_id = b.id
          and b.device_id = c.id
    </select>
    <select id="getPvDefectDeviceDetails" resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
        b.pv_string_id as parent_device_id,
        b.id as device_id,
        a.device_name AS parent_device_name,
        b.device_name
        FROM
        main_pv_string a
        LEFT JOIN main_pv_components b ON a.id = b.pv_string_id
        inner join main_inspection_picture_tagging c on b.id = c.pv_component_id
        WHERE
        a.is_deleted = 0 and b.is_deleted = 0
        and a.pv_area_id IN
        <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getDeptDefectList" resultType="java.lang.String">
        SELECT distinct dept_code
        FROM main_inspection_picture_tagging
        WHERE device_type = #{deviceType}
          and is_deleted = 0
          and dept_code like concat(#{deptCode}, '%')
    </select>
    <select id="getLineDefectList" resultType="com.allcore.main.code.solve.vo.LineDefectVO">
        SELECT distinct c.id,
                        c.line_name,
                        c.dept_code,
                        c.sort_order,
                        c.create_time
        FROM main_inspection_picture_tagging a
                 inner join main_tower b on a.device_id = b.id
                 inner join main_line c on b.line_id = c.id
        WHERE a.device_type = 'TMS_LINE'
          and a.is_deleted = 0
          and a.dept_code like concat(#{deptCode}, '%')
        order by c.sort_order desc,c.create_time desc
    </select>
    <select id="getTowerDefectList" resultType="com.allcore.main.code.solve.vo.TowerDefectVO">
        SELECT distinct b.id,
                        b.tower_name,
                        b.line_id
        FROM main_inspection_picture_tagging a
                 inner JOIN main_tower b ON a.device_id = b.id
        WHERE a.device_type = 'TMS_LINE'
          AND a.is_deleted = 0
          AND a.dept_code LIKE concat(#{deptCode}, '%')
    </select>
    <select id="getFanDefectList" resultType="com.allcore.main.code.solve.vo.FanDefectVO">
        SELECT DISTINCT b.id,
                        b.device_name,
                        b.dept_code,
                        b.sort_order,
                        b.create_time
        FROM main_inspection_picture_tagging a
                 inner JOIN main_fan b ON a.device_id = b.id
        WHERE a.device_type = 'FAN'
          AND a.is_deleted = 0
          AND a.dept_code LIKE concat(#{deptCode}, '%')
        order by b.sort_order desc,b.create_time desc
    </select>
    <select id="getPvDefectList" resultType="com.allcore.main.code.solve.vo.PvDefectVO">
        SELECT DISTINCT b.id,
                        b.device_name,
                        b.dept_code
        FROM main_inspection_picture_tagging a
                 inner JOIN main_pv_area b ON a.device_id = b.id
        WHERE a.device_type = 'PV'
          AND a.is_deleted = 0 and b.is_deleted = 0
          AND a.dept_code LIKE concat(#{deptCode}, '%')
        order by b.device_name
    </select>
    <select id="getDefectTaggingIds" resultMap="inspectionPictureTaggingResultMap">
        select a.*
        from main_inspection_picture_tagging a
        where a.is_deleted = 0
          and a.defect_type = #{defectType}
          and a.inspection_picture_id in
              (select id
               from main_inspection_picture
               where recognition_task_id = #{recognitionTaskId}
                <if test="picAuditStatus != null and picAuditStatus != ''">
                    and pic_audit_status = #{picAuditStatus}
                </if>
                 and is_deleted = 0
                 and pic_defect_flg = #{picDefectFlg})
    </select>
    <select id="getPvDefectTaggingIds" resultMap="inspectionPictureTaggingResultMap">
        select a.*
        from main_inspection_picture_tagging a
        where a.is_deleted = 0
        and a.pv_component_id is not null
        and a.defect_type = #{defectType}
        and a.inspection_picture_id in
        (select id
        from main_inspection_picture
        where recognition_task_id = #{recognitionTaskId}
        <if test="picAuditStatus != null and picAuditStatus != ''">
            and pic_audit_status = #{picAuditStatus}
        </if>
        and is_deleted = 0
        and pic_defect_flg = #{picDefectFlg})
    </select>

    <select id="getDefectTaggingIdsNotInRemove" resultMap="inspectionPictureTaggingResultMap">
        select a.*
        from main_inspection_picture_tagging a
        where a.is_deleted = 0
        and a.defect_type = #{defectType}
        and a.inspection_picture_id in
        (select id
        from main_inspection_picture
        where recognition_task_id = #{recognitionTaskId}
        <if test="picAuditStatus != null and picAuditStatus != ''">
            and pic_audit_status = #{picAuditStatus}
        </if>
        and is_deleted = 0
        and pic_defect_flg = #{picDefectFlg})
        and not exists (select * from main_remove_picture_tagging b where b.inspection_picture_tagging_id = a.id)
    </select>


    <select id="selectRemoveNameMap" resultType="java.util.Map">
        select
        a.id inspectionPictureTaggingId,
        b.remove_time removeTime
        from
        main_inspection_picture_tagging a LEFT JOIN
        main_remove_picture_tagging b on a.id = b.inspection_picture_tagging_id
        WHERE b.is_deleted = 0 and
        a.id IN
        <foreach collection="picTaggingIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="groupByTask" resultType="com.allcore.main.code.defect.vo.RecognitionTaskVO">
        SELECT
            a.recognition_task_id AS id,
            COUNT( 1 ) AS defectTagNum
        FROM (
            SELECT
                a.recognition_task_id
            FROM (
                SELECT
                    *
                FROM
                    main_inspection_picture_tagging a
                WHERE
                    a.is_deleted = 0
                    AND a.defect_type = 'defect'
                    AND a.recognition_task_id IN
                    <foreach collection="ids" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    AND a.defect_key NOT IN (
                        SELECT
                            a.light_defect_key
                        FROM
                            main_inspection_picture_tagging a
                        WHERE
                            a.is_deleted = 0
                            AND a.light_defect_key IS NOT NULL
                            AND a.defect_type = 'defect'
                            AND a.recognition_task_id IN
                            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                    )
            ) a
            INNER JOIN main_inspection_picture b ON a.inspection_picture_id = b.id AND b.bind_flag = 'yes'
            GROUP BY
                a.recognition_task_id,
                a.pv_component_id,
                a.pv_component_name,
                a.defect_description
        ) a
        GROUP BY
            a.recognition_task_id
    </select>


    <select id="groupByTaskNormal" resultType="com.allcore.main.code.defect.vo.RecognitionTaskVO">
        SELECT
        a.recognition_task_id AS id,
        count( 1 ) AS defectTagNum
        FROM
        main_inspection_picture_tagging a
        INNER JOIN main_inspection_picture b ON a.inspection_picture_id = b.id
        and b.bind_flag = 'yes'
        WHERE
        a.is_deleted = 0
        AND a.defect_type = 'defect'
        AND a.recognition_task_id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY
        a.recognition_task_id
    </select>


    <select id="getLastModelPath" resultType="java.lang.String">
        select file_path
        from main_three_data
        where is_deleted = 0
          and device_type = #{deviceType}
          and file_type = '3'
        order by create_time desc limit 1
    </select>
    <select id="stationFanInfo" resultType="com.allcore.main.code.common.vo.StationFanInfoVO">
        SELECT a.device_name,
               a.fan_num,
               a.capacity,
               a.model,
               a.height,
               a.longitude,
               a.latitude,
               a.elevation,
               b.device_id,
               b.create_time,
               b.defect_level,
               b.id as inspectionPictureTaggingId
        FROM main_fan a
                 LEFT JOIN (SELECT a.id,
                                   a.device_id,
                                   a.defect_level,
                                   a.create_time
                            FROM main_inspection_picture_tagging a
                            WHERE a.is_deleted = 0
                              AND a.device_id = #{id}
                            ORDER BY a.create_time DESC LIMIT 1) b ON a.id = b.device_id
        WHERE a.is_deleted = 0
          AND a.id = #{id}
    </select>
    <select id="pvExportTagInfos" resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
        *
        FROM
        (
        SELECT
        a.pv_area_id,
        b.is_z,
        b.is_algorithm,
        d.device_name AS pvAreaName,
        b.pv_component_id AS deviceId,
        a.device_name,
        a.pv_string_id AS parentDeviceId,
        c.device_name AS parentDeviceName,
        b.defect_description,
        b.big_file_guid,
        e.big_file_guid AS lightFileGuid,
        b.temperature,
        b.defect_level,
        ROW_NUMBER () OVER ( PARTITION BY b.pv_component_id,b.defect_description ORDER BY e.defect_key DESC ) AS group_idx
        FROM
        main_pv_components a
        INNER JOIN (
        SELECT
        a.recognition_task_id,
        a.pv_component_id,
        a.pv_component_name,
        a.defect_description,
        a.big_file_guid,
        a.light_defect_key,
        a.temperature,
        a.is_z,
        a.is_algorithm,
        a.defect_level
        FROM
        (
        SELECT
        *
        FROM
        main_inspection_picture_tagging a
        WHERE
        a.is_deleted = 0
        AND a.defect_type = 'defect'
        AND a.recognition_task_id = #{id}
        AND a.pv_component_id IS NOT NULL
        AND a.defect_key NOT IN (
        SELECT
        a.light_defect_key
        FROM
        main_inspection_picture_tagging a
        WHERE
        a.is_deleted = 0
        AND a.light_defect_key IS NOT NULL
        AND a.defect_type = 'defect'
        AND a.recognition_task_id = #{id}
        )) a
        INNER JOIN main_inspection_picture b ON a.inspection_picture_id = b.id
        AND b.bind_flag = 'yes'
        GROUP BY
        a.recognition_task_id,
        a.pv_component_id,
        a.pv_component_name,
        a.defect_description
        ORDER BY
        a.light_defect_key desc
        ) b ON a.id = b.pv_component_id
        INNER JOIN main_pv_string c ON a.pv_string_id = c.id
        INNER JOIN main_pv_area d ON a.pv_area_id = d.id
        LEFT JOIN (
        SELECT
        a.recognition_task_id,
        a.pv_component_id,
        a.pv_component_name,
        a.defect_description,
        a.big_file_guid,
        a.defect_key
        FROM
        (
        SELECT
        a.*
        FROM
        main_inspection_picture_tagging a
        WHERE
        a.is_deleted = 0
        AND a.light_defect_key IS NULL
        AND a.defect_type = 'defect'
        AND a.recognition_task_id = #{id}
        ) a
        INNER JOIN main_inspection_picture b ON a.inspection_picture_id = b.id
        AND b.bind_flag = 'yes'
        ) e ON b.light_defect_key = e.defect_key
        AND b.pv_component_id = e.pv_component_id
        WHERE
        a.is_deleted = 0
        AND c.is_deleted = 0
        AND d.is_deleted = 0
        AND a.pv_area_id IN
        <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY
        a.device_name
        ) a
        WHERE
        a.group_idx = 1
    </select>
    <select id="fanExportTagInfos" resultType="com.allcore.main.code.source.vo.DeviceWithParentVO">
        SELECT
            a.device_id,
            a.defect_description,
            a.defect_level,
            a.big_file_guid,
            a.file_guid,
            b.file_guid as picFileGuid
        FROM
            (
                SELECT
                    a.inspection_picture_id,
                    a.device_id,
                    a.defect_description,
                    a.defect_level,
                    a.big_file_guid,
                    a.file_guid
                FROM
                    main_inspection_picture_tagging a
                WHERE
                    a.is_deleted = 0
                  AND a.defect_type = 'defect'
                  AND a.recognition_task_id = #{id}
            ) a
                INNER JOIN main_inspection_picture b ON a.inspection_picture_id = b.id
                AND b.bind_flag = 'yes'
    </select>

</mapper>
