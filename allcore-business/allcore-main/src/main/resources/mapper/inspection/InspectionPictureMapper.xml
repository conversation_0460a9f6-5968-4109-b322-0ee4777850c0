<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.inspection.mapper.InspectionPictureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectionPictureResultMap" type="com.allcore.main.code.inspection.entity.InspectionPicture">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="recognition_task_id" property="recognitionTaskId"/>
        <result column="group_id" property="groupId"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="thumbnail_file_guid" property="thumbnailFileGuid"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_part" property="devicePart"/>
        <result column="pic_audit_status" property="picAuditStatus"/>
        <result column="pic_defect_flg" property="picDefectFlg"/>
        <result column="defect_pic_type" property="defectPicType"/>
        <result column="is_export" property="isExport"/>
        <result column="export_time" property="exportTime"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>
    <update id="updateReceivePic">
        update machine_receive_pic set status = #{dto.status} where task_id = #{dto.taskId} and task_type = #{dto.taskType} and is_deleted = 0
    </update>


    <select id="selectInspectionPicturePage" resultMap="inspectionPictureResultMap">
        select * from main_inspection_picture where is_deleted = 0
    </select>



    <resultMap id="inspectionPictureWithTagResultMap" type="com.allcore.main.code.inspection.vo.InspectionPictureVO">
        <result column="id" property="id"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="recognition_task_id" property="recognitionTaskId"/>
        <result column="group_id" property="groupId"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="thumbnail_file_guid" property="thumbnailFileGuid"/>
        <result column="device_id" property="deviceId"/>
        <result column="deviceName" property="deviceName"/>
        <result column="parentDeviceName" property="parentDeviceName"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_part" property="devicePart"/>
        <result column="pic_audit_status" property="picAuditStatus"/>
        <result column="pic_defect_flg" property="picDefectFlg"/>
        <result column="defect_pic_type" property="defectPicType"/>
        <result column="is_export" property="isExport"/>
        <result column="export_time" property="exportTime"/>
        <collection property="inspectionPictureTaggings" javaType="java.util.List" resultMap="inspectionPictureTaggingResultMap"/>
    </resultMap>

    <resultMap id="inspectionPictureTaggingResultMap" type="com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO">
        <result column="inspectionPictureTaggingId" property="id"/>
        <result column="tagFileGuid" property="fileGuid"/>
        <result column="device_type" property="deviceType"/>
        <result column="deviceName" property="deviceName"/>
        <result column="parentDeviceName" property="parentDeviceName"/>
        <result column="pvComponentName" property="pvComponentName"/>
        <result column="xmin" property="xmin"/>
        <result column="ymin" property="ymin"/>
        <result column="xmax" property="xmax"/>
        <result column="ymax" property="ymax"/>
        <result column="is_algorithm" property="isAlgorithm"/>
        <result column="tagging_type" property="taggingType"/>
        <result column="defect_description" property="defectDescription"/>
        <result column="defect_level" property="defectLevel"/>
        <result column="defect_name" property="defectName"/>
    </resultMap>

    <select id="getLinePictureTagAll" resultMap="inspectionPictureWithTagResultMap">
        SELECT
            b.line_name as parentDeviceName,
            c.tower_name as deviceName,
            d.*,
            e.id as inspectionPictureTaggingId,
            e.file_guid as tagFileGuid,
            e.xmin,e.ymin,e.xmax,e.ymax,e.is_algorithm,e.tagging_type,e.defect_description,e.defect_level,e.defect_name
        FROM
            main_inspection_device_detail a
                LEFT JOIN main_tower c ON a.device_id = c.id
                LEFT JOIN main_line b ON c.line_id = b.id
                INNER JOIN main_inspection_picture d ON c.id = d.device_id
                INNER join main_inspection_picture_tagging e on d.id = e.inspection_picture_id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}
          AND d.inspection_task_id = #{inspectionTaskId} and d.is_deleted = 0
          AND e.inspection_task_id = #{inspectionTaskId} AND e.is_deleted = 0
          and e.defect_type = 'defect'
        ORDER BY
            c.tower_name
    </select>

    <select id="getFanPictureTagAll" resultMap="inspectionPictureWithTagResultMap">
        SELECT
            c.device_name as deviceName,
            d.*,
            e.id as inspectionPictureTaggingId,
            e.file_guid as tagFileGuid,
            e.xmin,e.ymin,e.xmax,e.ymax,e.is_algorithm,e.tagging_type,e.defect_description,e.defect_level,e.defect_name
        FROM
            main_inspection_device_detail a
                LEFT JOIN main_fan c ON a.device_id = c.id
                INNER JOIN main_inspection_picture d ON c.id = d.device_id
                INNER join main_inspection_picture_tagging e on d.id = e.inspection_picture_id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}
          AND d.inspection_task_id = #{inspectionTaskId} and d.is_deleted = 0
          AND e.inspection_task_id = #{inspectionTaskId}
          and e.defect_type = 'defect'
        ORDER BY
            c.device_name
    </select>

    <select id="getPvPictureTagAll" resultMap="inspectionPictureWithTagResultMap">
        SELECT
            c.device_name as deviceName,
            d.*,
            e.id as inspectionPictureTaggingId,
            e.file_guid as tagFileGuid,
            f.device_name as pvComponentName,
            e.xmin,e.ymin,e.xmax,e.ymax,e.is_algorithm,e.tagging_type,e.defect_description,e.defect_level,e.defect_name
        FROM
            main_inspection_device_detail a
                LEFT JOIN main_pv_area c ON a.device_id = c.id
                INNER JOIN main_inspection_picture d ON c.id = d.device_id
                INNER join main_inspection_picture_tagging e on d.id = e.inspection_picture_id
                left join main_pv_components f on e.pv_component_id = f.id
        WHERE
            a.inspection_task_id = #{inspectionTaskId}
          AND d.inspection_task_id = #{inspectionTaskId} and d.is_deleted = 0
          AND e.inspection_task_id = #{inspectionTaskId}
          and e.defect_type = 'defect'
        ORDER BY
            c.device_name
    </select>
    <select id="checkRecognitionTaskStatus" resultType="java.lang.Integer">
        select count(1) from main_recognition_task where is_deleted = 0 and recognition_task_status = 'to_identify_the'
        and id in
            (select distinct recognition_task_id from main_inspection_picture where id in
                <foreach collection="strList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
    </select>
    <select id="checkRecognitionTaskStatusByFileGuid" resultType="java.lang.Integer">
        select count(1) from main_recognition_task where is_deleted = 0 and recognition_task_status = 'to_identify_the'
        and id = (select recognition_task_id from main_inspection_picture where file_guid = #{fileGuid})
    </select>
    <select id="getInspectionTaskByTaskId" resultType="java.lang.String">
        select inspection_task_id from machine_nest_task where id = #{taskId}
    </select>
    <select id="getAllNum" resultType="java.lang.Integer">
        SELECT
            sum( sum_pic )
        FROM
            machine_receive_pic
        WHERE
                task_id IN (
                SELECT
                    id
                FROM
                    machine_nest_task
                WHERE
                    inspection_task_id = #{inspectionTaskId}
            )
    </select>

    <select id="selectImageFileByDeviceId" resultType="com.allcore.main.code.inspection.vo.ImagesFileVO">
        SELECT p.file_guid as fileGuid,
               f.name as filePath,
               f.dynamic_thumb_path as staticThumbPath,
               f.create_time as shootTime,
               f.original_name as fileName
        FROM "main_inspection_picture" p INNER JOIN "file_attach_info" f ON f.file_guid = p.file_guid
        WHERE p.is_deleted = 0
          AND p.device_id = #{dto.deviceId}
        <if test="dto.startDate != null and dto.startDate !=''">
            and f.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate !=''">
            and f.create_time &lt;= #{dto.endDate}
        </if>
    </select>

</mapper>
