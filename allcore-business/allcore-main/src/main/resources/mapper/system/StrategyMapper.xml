<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.system.mapper.StrategyMapper">

    <select id="selectStrategyPage" resultType="com.allcore.main.code.system.vo.StrategyVO">
        select * from main_strategy where is_deleted = 0
        <if test="strategyName != null and strategyName != ''">
            and strategy_name like concat(concat('%', #{strategyName}), '%')
        </if>
    </select>

    <select id="getList" resultType="com.allcore.main.code.system.vo.StrategyVO">
        select * from main_strategy where is_deleted = 0
        and (dept_code=1000 or dept_code=#{deptCode} )
    </select>

    <update id="updateUsageTimes">
        UPDATE main_strategy SET usage_times = usage_times + 1 WHERE id = #{strategyId};
    </update>

    <update id="updateSatus">
        UPDATE main_strategy SET is_enable = #{isEnable} WHERE id = #{id};
    </update>

</mapper>
