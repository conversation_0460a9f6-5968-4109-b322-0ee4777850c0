<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.system.mapper.AlgorithmManufacturersMapper">

    <select id="selectAlgorithmManufacturersPage" resultType="com.allcore.main.code.system.vo.AlgorithmManufacturersVO">
        SELECT
        m.manufacturersId,
        m.manufacturers_abbreviate,
        m.manufacturers_name,
        WM_CONCAT(a.algorithm_model) AS algorithm_model
        FROM
        (SELECT id AS manufacturersId, manufacturers_abbreviate, manufacturers_name FROM main_manufacturers WHERE is_deleted = 0) m
        LEFT JOIN (SELECT manufacturers_id, algorithm_model FROM main_algorithm_manufacturers WHERE is_deleted = 0) a ON m.manufacturersId = a.manufacturers_id
        <where>
            <if test="dto.manufacturersAbbreviate != null and dto.manufacturersAbbreviate != ''">
                AND m.manufacturers_abbreviate LIKE '%' || #{dto.manufacturersAbbreviate} || '%'
            </if>
            <if test="dto.manufacturersName != null and dto.manufacturersName != ''">
                AND m.manufacturers_name LIKE '%' || #{dto.manufacturersName} || '%'
            </if>
            <if test="dto.algorithmModel != null and dto.algorithmModel != ''">
                AND a.algorithm_model LIKE '%' || #{dto.algorithmModel} || '%'
            </if>
        </where>
        GROUP BY
        m.manufacturersId,
        m.manufacturers_abbreviate,
        m.manufacturers_name
    </select>
    <select id="getAlgorithmManufacturers" resultType="java.lang.String">
        SELECT
            c.id
        FROM
            main_strategy a
                LEFT JOIN main_strategy_detail b ON a.id = b.strategy_id
                LEFT JOIN main_algorithm_manufacturers c ON b.algorithm_manufacturers_id = c.id
        WHERE
            a.is_deleted = 0
          AND a.is_enable = 'yes'
          AND b.is_deleted = 0
          AND b.is_enable = 'yes'
          AND INSTR(',' || c.device_type || ',', ',' || #{deviceType} || ',') > 0
    </select>

</mapper>
