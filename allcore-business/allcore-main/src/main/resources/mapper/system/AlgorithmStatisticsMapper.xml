<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.system.mapper.AlgorithmStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="algorithmStatisticsResultMap" type="com.allcore.main.code.system.entity.AlgorithmStatistics">
        <result column="id" property="id"/>
        <result column="algorithm_manufacturer" property="algorithmManufacturer"/>
        <result column="algorithm_model" property="algorithmModel"/>
        <result column="algorithm_category" property="algorithmCategory"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>
    <select id="selectAlgorithmStatisticsPage" resultType="com.allcore.main.code.system.vo.AlgorithmStatisticsVO">
        select * from main_algorithm_statistics
        <where>
            <if test="dto.deviceType != null and dto.deviceType != ''">
                and device_type = #{dto.deviceType}
            </if>
            <if test="dto.algorithmManufacturer != null and dto.algorithmManufacturer != ''">
                and algorithm_manufacturer like concat('%',#{dto.algorithmManufacturer},'%')
            </if>
            <if test="dto.algorithmModel != null and dto.algorithmModel != ''">
                and algorithm_model like concat('%',#{dto.algorithmModel},'%')
            </if>
            <if test="dto.algorithmCategory != null and dto.algorithmCategory != ''">
                and algorithm_category like concat('%',#{dto.algorithmCategory},'%')
            </if>
        </where>
    </select>
</mapper>
