<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.system.mapper.StrategyDetailMapper">


    <select id="getStrategyDetailAndAlgorithmManufacturersByStrategyId" resultType="com.allcore.main.code.system.vo.StrategyDetailVO">
        SELECT
            a.id,
            a.strategy_id,
            a.algorithm_manufacturers_id,
            a.strategy_detail_model,
            b.manufacturers_id,
            b.algorithm_name,
            b.algorithm_image_name,
            b.algorithm_versions,
            b.algorithm_support_gpu,
            b.algorithm_model,
            b.algorithm_address,
            b.device_type,
            b.algorithm_type
        FROM
            main_strategy_detail a
            LEFT JOIN main_algorithm_manufacturers b ON a.algorithm_manufacturers_id = b.id
        WHERE
            a.strategy_id = #{strategyId}
            AND b.device_type = #{deviceType}
            AND a.is_deleted = 0
            AND a.is_enable = 'yes'
            AND b.is_deleted = 0
            AND b.is_enable = 'yes'
    </select>

    <select id="getQueryList" resultType="com.allcore.main.code.system.vo.StrategyDetailVO">
        SELECT
            a.manufacturers_name,
            b.manufacturers_id,
            b.algorithm_name,
            c.strategy_detail_model,
            c.is_enable,
            c.algorithm_manufacturers_id,
            c.id
        FROM
            main_manufacturers a,
            main_algorithm_manufacturers b,
            main_strategy_detail c
        WHERE
            c.algorithm_manufacturers_id = b.id
          AND b.manufacturers_id = a.id
          AND c.strategy_id = #{strategyId}
    </select>
    <select id="selectAlgorithmList" resultType="com.allcore.main.code.system.vo.SelectBoxVo">
        SELECT
            c.id,c.algorithm_name as name
        FROM
            main_strategy a
                LEFT JOIN main_strategy_detail b ON a.id = b.strategy_id
                left join main_algorithm_manufacturers c on b.algorithm_manufacturers_id  = c.id
        WHERE
            a.is_deleted = 0
          AND a.is_enable = 'yes'
          AND b.is_deleted = 0
          AND b.is_enable = 'yes'
    </select>
</mapper>
