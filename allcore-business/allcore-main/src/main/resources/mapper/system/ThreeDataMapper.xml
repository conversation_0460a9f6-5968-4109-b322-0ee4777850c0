<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.system.mapper.ThreeDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="threeDataResultMap" type="com.allcore.main.code.system.vo.ThreeDataVO">
        <result column="id" property="id"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_id" property="deviceId"/>
        <result column="start_id" property="startId"/>
        <result column="end_id" property="endId"/>
        <result column="file_type" property="fileType"/>
        <result column="file_name" property="fileName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_size" property="fileSize"/>
        <result column="upload_status" property="uploadStatus"/>
        <result column="data_factory" property="dataFactory"/>
        <result column="gather_time" property="gatherTime"/>
        <result column="work_time" property="workTime"/>
        <result column="remark" property="remark"/>
        <result column="is_show" property="isShow"/>
        <result column="deviceRangeZh" property="deviceRangeZh"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="elevation" property="elevation"/>
    </resultMap>


    <select id="selectThreeDataPage" resultMap="threeDataResultMap">
        select t.* from main_three_data t where t.is_deleted = 0
        <if test="dto.deviceType != '' and dto.deviceType != null ">
            and t.device_type = #{dto.deviceType}
        </if>
        <if test="dto.fileType != '' and dto.fileType != null">
            and t.file_type = #{dto.fileType}
        </if>
        <if test="dto.deviceId != '' and dto.deviceId != null ">
            and t.device_id = #{dto.deviceId}
        </if>
        <if test="dto.startId != '' and dto.startId != null ">
            and t.start_id = #{dto.startId}
        </if>
        <if test="dto.deptCode != '' and dto.deptCode != null ">
            and t.dept_code like concat( #{dto.deptCode},'%')
        </if>
    </select>

    <select id="selectThreeDataFanPage" resultMap="threeDataResultMap">
        select t.*,
               b.longitude,
               b.latitude,
               b.elevation
                   from main_three_data t,main_fan b
                   where t.device_id = b.id
                   and t.is_deleted = 0
        <if test="dto.deviceType != '' and dto.deviceType != null ">
            and t.device_type = #{dto.deviceType}
        </if>
        <if test="dto.fileType != '' and dto.fileType != null">
            and t.file_type = #{dto.fileType}
        </if>
        <if test="dto.deviceId != '' and dto.deviceId != null ">
            and t.device_id = #{dto.deviceId}
        </if>
        <if test="dto.startId != '' and dto.startId != null ">
            and t.start_id = #{dto.startId}
        </if>
        <if test="dto.deptCode != '' and dto.deptCode != null ">
            and t.dept_code like concat( #{dto.deptCode},'%')
        </if>
    </select>

    <select id="selectThreeDataLinePage" resultMap="threeDataResultMap">
        select a.*,
        CONCAT( b.tower_name, '-', c.tower_name ) AS deviceRangeZh
               from
        ( select t.* from main_three_data t where t.is_deleted = 0
        <if test="dto.deviceType != '' and dto.deviceType != null ">
            and t.device_type = #{dto.deviceType}
        </if>
        <if test="dto.fileType != '' and dto.fileType != null">
            and t.file_type = #{dto.fileType}
        </if>
        <if test="dto.deviceId != '' and dto.deviceId != null ">
            and t.device_id = #{dto.deviceId}
        </if>
        <if test="dto.startId != '' and dto.startId != null ">
            and t.start_id = #{dto.startId}
        </if>
        <!--<if test="dto.deptCode != '' and dto.deptCode != null ">
            and t.dept_code like concat( #{dto.deptCode},'%')
        </if> -->) a,
        main_tower b,
        main_tower c
        WHERE
        a.start_id = b.id
        AND a.end_id = c.id
    </select>



    <select id="lineSelectList" resultType="com.allcore.main.code.system.vo.SelectBoxVo">
        SELECT
        a.device_id as id,
        b.line_name as name
        FROM
        main_three_data a
        LEFT JOIN main_line b ON a.device_id = b.id
        WHERE
        a.is_deleted = 0 and a.device_type = 'TMS_LINE'
        <if test="deptCode != '' and deptCode != null">
            and a.dept_code like concat( #{deptCode},'%')
        </if>
        GROUP BY
        a.device_id,
        b.line_name
    </select>
    <select id="fanModelSelectList" resultType="com.allcore.main.code.system.vo.SelectBoxVo">
        SELECT
        a.device_id as id,
        a.device_id as name
        FROM
        main_three_data a
        WHERE
        a.is_deleted = 0 and a.device_type = 'FAN'
        <if test="deptCode != '' and deptCode != null">
            and a.dept_code like concat( #{deptCode},'%')
        </if>
        group by
        a.device_id

    </select>
    <select id="pvSelectList" resultType="com.allcore.main.code.system.vo.SelectBoxVo">
        SELECT
        a.device_id as id,
        b.device_name as name
        FROM
        main_three_data a
        LEFT JOIN main_pv_area b ON a.device_id = b.id
        WHERE
        a.is_deleted = 0 and a.device_type = 'PV'
        <if test="deptCode != '' and deptCode != null">
            and a.dept_code like concat( #{deptCode},'%')
        </if>
        GROUP BY
        a.device_id,
        b.device_name
    </select>
    <select id="towerSelectList" resultType="com.allcore.main.code.system.vo.SelectBoxVo">
        SELECT
            a.start_id AS id,
            CONCAT( b.tower_name, '-', c.tower_name ) AS NAME
        FROM
            (
                SELECT
                    a.start_id,
                    a.end_id
                FROM
                    main_three_data a
                WHERE
                    a.is_deleted = 0
                  AND a.device_type = 'TMS_LINE'
                    <if test="deviceId != '' and deviceId != null">
                        and a.device_id = #{deviceId}
                    </if>
                    <if test="deptCode != '' and deptCode != null">
                        and a.dept_code like concat( #{deptCode},'%')
                    </if>
                GROUP BY
                    a.start_id,
                    a.end_id
            ) a,
            main_tower b,
            main_tower c
        WHERE
            a.start_id = b.id
          AND a.end_id = c.id
    </select>

</mapper>
