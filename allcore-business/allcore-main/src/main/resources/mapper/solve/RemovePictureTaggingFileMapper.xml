<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.solve.mapper.RemovePictureTaggingFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="removePictureTaggingFileResultMap" type="com.allcore.main.code.solve.entity.RemovePictureTaggingFile">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="inspection_picture_tagging_id" property="inspectionPictureTaggingId"/>
        <result column="remove_tagging_file_type" property="removeTaggingFileType"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectRemovePictureTaggingFilePage" resultMap="removePictureTaggingFileResultMap">
        select * from main_remove_picture_tagging_file where is_deleted = 0
    </select>

</mapper>
