<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.solve.mapper.RemovePictureTaggingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="removePictureTaggingResultMap" type="com.allcore.main.code.solve.entity.RemovePictureTagging">
        <result column="id" property="id"/>
        <result column="remove_task_id" property="removeTaskId"/>
        <result column="inspection_picture_tagging_id" property="inspectionPictureTaggingId"/>
    </resultMap>


    <select id="selectRemovePictureTaggingPage" resultMap="removePictureTaggingResultMap">
        select * from main_remove_picture_tagging where is_deleted = 0
    </select>
    <select id="getMaxNoTag" resultType="com.allcore.main.code.solve.entity.RemovePictureTagging">
        select * from main_remove_picture_tagging
        where is_deleted = 0
        and remove_task_id = #{removeTaskId}
        order by
<!--            SUBSTRING_INDEX(remove_task_no,"-",-1) * 1 desc-->
        CAST(SUBSTR(
            remove_task_no,
            LENGTH(remove_task_no) - INSTR(REVERSE(remove_task_no), '-') + 2
        ) AS INT) DESC
        limit 1
    </select>
    <select id="filterPvRemoveTag" resultType="java.lang.String">
        SELECT
            id
        FROM
            main_inspection_picture_tagging
        WHERE
                pv_component_id NOT IN
                ( SELECT b.pv_component_id FROM main_remove_picture_tagging a, main_inspection_picture_tagging b
                                           WHERE a.inspection_picture_tagging_id = b.id AND a.remove_task_id = #{removeTaskId} )
          AND inspection_task_id = #{inspectionTaskId}
            and id in
        <foreach collection="inspectionPictureTaggingIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
