<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.solve.mapper.RemoveLibraryDownLoadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="removeLibraryDownLoadResultMap" type="com.allcore.main.code.solve.vo.RemoveLibraryDownLoadVO">

        <result column="create_time" property="createTime"/>
        <result column="device_id" property="deviceId"/>
        <result column="file_guid" property="fileGuid"/>
        <result column="video_status" property="videoStatus"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectRemoveLibraryDownLoadPage" resultMap="removeLibraryDownLoadResultMap">
        select * from main_remove_library_down_load where is_deleted = 0
        <if test="dto.startTime != null and dto.startTime != ''">
            and create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and create_time &lt;= #{dto.endTime}
        </if>
        order by create_time desc
    </select>

</mapper>
