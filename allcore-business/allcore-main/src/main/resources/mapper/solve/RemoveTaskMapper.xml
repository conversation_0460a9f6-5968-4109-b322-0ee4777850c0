<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.main.code.solve.mapper.RemoveTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="removeTaskResultMap" type="com.allcore.main.code.solve.entity.RemoveTask">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="remove_task_name" property="removeTaskName"/>
        <result column="inspection_task_id" property="inspectionTaskId"/>
        <result column="inspection_task_no" property="inspectionTaskNo"/>
        <result column="work_nature" property="workNature"/>
        <result column="work_charger" property="workCharger"/>
        <result column="work_licensor" property="workLicensor"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="remove_user" property="removeUser"/>
        <result column="remove_time" property="removeTime"/>
        <result column="defect_level" property="defectLevel"/>
        <result column="station_inspectioner" property="stationInspectioner"/>
        <result column="other_operators" property="otherOperators"/>
        <result column="remove_data_source" property="removeDataSource"/>
        <result column="remove_task_status" property="removeTaskStatus"/>
        <result column="is_issue" property="isIssue"/>
        <result column="scene_situation" property="sceneSituation"/>
        <result column="is_out_entrust" property="isOutEntrust"/>
        <result column="examine_opinion" property="examineOpinion"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>


    <select id="selectRemoveTaskPage" resultMap="removeTaskResultMap">
        select * from main_remove_task where is_deleted = 0
    </select>
    <select id="selectLineTaskPage" resultType="com.allcore.main.code.solve.vo.RemoveTaskPageVO">
        SELECT
            a.id,
            a.dept_code,
            a.remove_task_name,
            c.device_id,
            d.tower_name AS device_name,
            d.tower_no,
            c.defect_description,
            c.defect_level,
            c.big_file_guid as file_guid,
            b.create_time,
            b.remove_time,
            b.remove_user,
            b.remove_task_status,
            b.inspection_picture_tagging_id,
            b.remove_task_no,
            c.device_type,
            a.inspection_task_no
        FROM
            main_remove_task a
                LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
                LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
	            LEFT JOIN main_tower d ON c.device_id = d.id
                left join main_line e on d.line_id = e.id
        where a.is_deleted = 0
        <if test="dto.removeTaskName != null and dto.removeTaskName != ''">
            and a.remove_task_name = #{dto.removeTaskName}
        </if>
        <if test="dto.removeTaskNo != null and dto.removeTaskNo != ''">
            and b.remove_task_no = #{dto.removeTaskNo}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            and c.device_type = #{dto.deviceType}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dept_code like concat(#{dto.deptCode}, '%')
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and d.tower_name like concat('%',#{dto.deviceName}, '%')
        </if>
        <if test="dto.towerNo != null and dto.towerNo != ''">
            and d.tower_no like concat('%',#{dto.towerNo}, '%')
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            and c.defect_level like concat(#{dto.defectLevel}, '%')
        </if>
        <if test="dto.defectDescription != null and dto.defectDescription != ''">
            and c.defect_description like concat(#{dto.defectDescription}, '%')
        </if>
        <if test="dto.removeTaskStatus != null and dto.removeTaskStatus != ''">
            and b.remove_task_status = #{dto.removeTaskStatus}
        </if>
        <if test="dto.removeUser != null and dto.removeUser != ''">
            and b.remove_user = #{dto.removeUser}
        </if>
        <if test="dto.treeType != null and dto.treeType != '0'.toString()">
            <if test="dto.treeType == 'TMS_LINE'">
                and e.id = #{dto.treeId}
            </if>
            <if test="dto.treeType == 'TMS_TOWER'">
                and d.id = #{dto.treeId}
            </if>
        </if>
        <if test="dto.treeId != null  and dto.treeId != '' and dto.treeId != '0'.toString()">
            and c.device_id = #{dto.treeId}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            and a.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        <if test="dto.createTime != null and dto.createTime != ''">
            and DATE_FORMAT(b.create_time,'%Y-%m-%d') = #{dto.createTime}
        </if>
        <if test="dto.updateTime != null and dto.updateTime != ''">
            and DATE_FORMAT(b.update_time,'%Y-%m-%d') = #{dto.updateTime}
        </if>
        order by c.inspection_task_no desc

    </select>
    <select id="selectFanTaskPage" resultType="com.allcore.main.code.solve.vo.RemoveTaskPageVO">
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_fan d ON c.device_id = d.id
        where a.is_deleted = 0
        <if test="dto.removeTaskName != null and dto.removeTaskName != ''">
            and a.remove_task_name = #{dto.removeTaskName}
        </if>
        <if test="dto.removeTaskNo != null and dto.removeTaskNo != ''">
            and b.remove_task_no = #{dto.removeTaskNo}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            and c.device_type = #{dto.deviceType}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dept_code like concat(#{dto.deptCode}, '%')
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and d.device_name like concat('%',#{dto.deviceName}, '%')
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            and c.defect_level like concat(#{dto.defectLevel}, '%')
        </if>
        <if test="dto.defectDescription != null and dto.defectDescription != ''">
            and c.defect_description like concat(#{dto.defectDescription}, '%')
        </if>
        <if test="dto.removeTaskStatus != null and dto.removeTaskStatus != ''">
            and b.remove_task_status = #{dto.removeTaskStatus}
        </if>
        <if test="dto.removeUser != null and dto.removeUser != ''">
            and b.remove_user = #{dto.removeUser}
        </if>
        <if test="dto.treeId != null  and dto.TreeId != ''  and dto.treeId != '0'.toString()">
            and c.device_id = #{dto.treeId}
        </if>
        <if test="dto.treeType != null and dto.treeType != ''">
            and a.device_type = #{dto.treeType}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
          and a.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        <if test="dto.createTime != null and dto.createTime != ''">
            and DATE_FORMAT(b.create_time,'%Y-%m-%d') = #{dto.createTime}
        </if>
        <if test="dto.updateTime != null and dto.updateTime != ''">
            and DATE_FORMAT(b.update_time,'%Y-%m-%d') = #{dto.updateTime}
        </if>
        order by c.inspection_task_no desc,SUBSTR(b.remove_task_no,INSTR(b.remove_task_no,'-',-1,1) + 1) * 1
    </select>
    <select id="selectPvTaskPage" resultType="com.allcore.main.code.solve.vo.RemoveTaskPageVO">
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        e.big_file_guid AS lightFileGuid,
        a.inspection_task_no
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_pv_area d ON c.device_id = d.id
        left join (select distinct defect_key,big_file_guid from main_inspection_picture_tagging where device_type = 'PV'  GROUP BY defect_key) e on c.light_defect_key = e.defect_key
        where a.is_deleted = 0
        <if test="dto.removeTaskName != null and dto.removeTaskName != ''">
            and a.remove_task_name = #{dto.removeTaskName}
        </if>
        <if test="dto.removeTaskNo != null and dto.removeTaskNo != ''">
            and b.remove_task_no = #{dto.removeTaskNo}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            and c.device_type = #{dto.deviceType}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dept_code like concat(#{dto.deptCode}, '%')
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            and d.device_name like concat('%',#{dto.deviceName}, '%')
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            and c.defect_level like concat(#{dto.defectLevel}, '%')
        </if>
        <if test="dto.defectDescription != null and dto.defectDescription != ''">
            and c.defect_description like concat(#{dto.defectDescription}, '%')
        </if>
        <if test="dto.removeTaskStatus != null and dto.removeTaskStatus != ''">
            and b.remove_task_status = #{dto.removeTaskStatus}
        </if>
        <if test="dto.removeUser != null and dto.removeUser != ''">
            and b.remove_user = #{dto.removeUser}
        </if>
        <if test="dto.treeId != null   and dto.treeId != '' and dto.treeId != '0'.toString()">
            and c.device_id = #{dto.treeId}
        </if>
        <if test="dto.treeType != null and dto.treeType != ''">
            and c.device_type = #{dto.treeType}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            and a.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        <if test="dto.createTime != null and dto.createTime != ''">
            and DATE_FORMAT(b.create_time,'%Y-%m-%d') = #{dto.createTime}
        </if>
        <if test="dto.updateTime != null and dto.updateTime != ''">
            and DATE_FORMAT(b.update_time,'%Y-%m-%d') = #{dto.updateTime}
        </if>
        order by c.inspection_task_no desc,b.remove_task_no
    </select>

    <select id="getLineImageLibrary" resultType="com.allcore.main.code.solve.vo.ImageLibraryVO">
        SELECT
        distinct a.file_guid,
        d.id AS parentDeviceId,
        d.line_name as parentDeviceName,
        c.id AS deviceId,
        c.tower_name as deviceName,
        DATE_FORMAT( a.create_time, '%Y-%m-%d' ) AS date,
        a.remove_tagging_file_type as fileType
        FROM
        main_remove_picture_tagging_file a
        inner JOIN main_inspection_picture_tagging b ON a.inspection_picture_tagging_id = b.id
        inner JOIN main_tower c ON b.device_id = c.id
        inner JOIN main_line d ON c.line_id = d.id
        WHERE
        a.is_deleted = 0
        <if test="dto.deviceName != null and dto.deviceName != ''">
            AND d.line_name LIKE concat('%',#{dto.deviceName},'%')
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            AND a.dept_code LIKE concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND a.create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND a.create_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            AND b.defect_level = #{dto.defectLevel}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            AND b.device_type = #{dto.deviceType}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            AND b.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        order by d.line_name,c.tower_name,a.create_time
    </select>


    <select id="getFanImageLibrary" resultType="com.allcore.main.code.solve.vo.ImageLibraryVO">
        SELECT
        distinct a.file_guid,
        c.id AS deviceId,
        c.device_name as deviceName,
        DATE_FORMAT( a.create_time, '%Y-%m-%d' ) AS date,
        a.remove_tagging_file_type as fileType
        FROM
        main_remove_picture_tagging_file a
        inner JOIN main_inspection_picture_tagging b ON a.inspection_picture_tagging_id = b.id
        inner JOIN main_fan c ON b.device_id = c.id
        WHERE
        a.is_deleted = 0
        <if test="dto.deviceName != null and dto.deviceName != ''">
            AND c.device_name LIKE concat('%',#{dto.deviceName},'%')
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            AND a.dept_code LIKE concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND a.create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND a.create_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            AND b.defect_level = #{dto.defectLevel}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            AND b.device_type = #{dto.deviceType}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            AND b.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        order by c.device_name,a.create_time
    </select>


    <select id="getPvImageLibrary" resultType="com.allcore.main.code.solve.vo.ImageLibraryVO">
        SELECT
        distinct a.file_guid,
        c.id AS deviceId,
        c.device_name as deviceName,
        DATE_FORMAT( a.create_time, '%Y-%m-%d' ) AS date,
        a.remove_tagging_file_type as fileType
        FROM
        main_remove_picture_tagging_file a
        inner JOIN main_inspection_picture_tagging b ON a.inspection_picture_tagging_id = b.id
        inner JOIN main_pv_area c ON b.device_id = c.id
        WHERE
        a.is_deleted = 0
        <if test="dto.deviceName != null and dto.deviceName != ''">
            AND c.device_name LIKE concat('%',#{dto.deviceName},'%')
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            AND a.dept_code LIKE concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND a.create_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND a.create_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            AND b.defect_level = #{dto.defectLevel}
        </if>
        <if test="dto.deviceType != null and dto.deviceType != ''">
            AND b.device_type = #{dto.deviceType}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            AND b.inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        order by c.device_name,a.create_time
    </select>
    <select id="allTaskInfo" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            main_inspection_picture_tagging a
                INNER JOIN main_remove_picture_tagging c ON a.id = c.inspection_picture_tagging_id
        WHERE
            a.is_deleted = 0
          AND c.is_deleted = 0
          AND c.remove_task_status IN ( 'doing', 'completed' )
          AND a.device_type = 'PV'
          AND a.defect_type = 'defect'
          AND c.remove_user = #{userId}
    </select>

    <select id="completedTaskInfo" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            main_inspection_picture_tagging a
                INNER JOIN main_remove_picture_tagging c ON a.id = c.inspection_picture_tagging_id
        WHERE
            a.is_deleted = 0
          AND c.is_deleted = 0
          AND c.remove_task_status = 'completed'
          AND a.device_type = 'PV'
          AND a.defect_type = 'defect'
          AND c.remove_user = #{userId}
    </select>
    <select id="selectCompletedTask" resultType="java.lang.String">
        select distinct id from main_remove_task where is_deleted = 0 and id in (
            select distinct remove_task_id from main_remove_picture_tagging where inspection_picture_tagging_id in
                <foreach collection="inspectionPictureTaggingIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
        and id not in (select distinct a.id from main_remove_task a inner join main_remove_picture_tagging b
        on a.id = b.remove_task_id and b.remove_task_status !='completed' where a.is_deleted = 0 and b.is_deleted = 0 )
    </select>
    <select id="history" resultType="com.allcore.main.code.solve.vo.DefectHistoryVO">
        select a.*,b.start_date,b.end_date
        from (
            SELECT
                a.id as inspectionPictureTaggingId,
                a.inspection_task_id,
                a.pv_component_id
            FROM
                main_inspection_picture_tagging a
            inner join main_remove_picture_tagging b on a.id = b.inspection_picture_tagging_id

            WHERE
                a.is_deleted = 0 and b.is_deleted = 0
              and a.pv_component_id = #{pvComponentId}
              and a.defect_type = 'defect'
              and a.eliminate_status = 'already_vanish_defect'
              and b.remove_user = #{removeUser}
        ) a,main_inspection_task b where a.inspection_task_id = b.id
         and b.is_deleted = 0
        order by b.create_time desc
    </select>
    <select id="getRemoveNoList" resultType="java.lang.String">
        select distinct mrpt.remove_task_no
        from main_remove_picture_tagging mrpt
        left join main_remove_task mrt on mrpt.remove_task_id = mrt.id
        where mrt.is_deleted = 0 and mrpt.is_deleted = 0
        and mrt.dept_code like concat(#{deptCode},'%')
        <if test="deviceType != null and deviceType != ''">
            and mrt.device_type = #{deviceType}
        </if>

    </select>
    <select id="getDeviceInfo" resultType="com.allcore.main.code.solve.vo.InspectionDeviceInfoVO">
        SELECT a.device_name, a.longitude, a.latitude
        FROM main_pv_area a
        WHERE a.is_deleted = 0
        <if test="deviceIds != null and deviceIds != ''">
            AND a.id IN
            <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <!--<select id="selectUnifiedTaskPage" resultType="com.allcore.main.code.solve.vo.RemoveTaskPageVO">
        SELECT * FROM (
        &#45;&#45; TMS_LINE设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.tower_name AS device_name,
        d.tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        c.device_id,
        a.inspection_task_no,
        null as lightFileGuid
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_tower d ON c.device_id = d.id
        LEFT_JOIN main_line e ON d.line_id=e.id
        WHERE a.is_deleted = 0 AND c.device_type = 'TMS_LINE'

        UNION ALL

        &#45;&#45; FAN设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        null as tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no,
        null as lightFileGuid
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_fan d ON c.device_id = d.id
        WHERE a.is_deleted = 0 AND c.device_type = 'FAN'

        UNION ALL

        &#45;&#45; PV设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        null as tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no,

        e.big_file_guid AS lightFileGuid
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_pv_area d ON c.device_id = d.id
        LEFT JOIN main_inspection_picture_tagging e ON c.pv_component_id = e.pv_component_id
        AND e.defect_type = 'light' AND e.is_deleted = 0
        WHERE a.is_deleted = 0 AND c.device_type = 'PV'
        ) unified_result
        WHERE 1=1
        <if test="dto.deviceType != null and dto.deviceType != ''">
            AND device_type = #{dto.deviceType}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            AND dept_code LIKE concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.removeTaskName != null and dto.removeTaskName != ''">
            AND remove_task_name = #{dto.removeTaskName}
        </if>
        <if test="dto.removeTaskStatus != null and dto.removeTaskStatus != ''">
            AND remove_task_status = #{dto.removeTaskStatus}
        </if>
        <if test="dto.removeUser != null and dto.removeUser != ''">
            AND remove_user = #{dto.removeUser}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            AND inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        <if test="dto.removeTaskNo != null and dto.removeTaskNo != ''">
            AND remove_task_no = #{dto.removeTaskNo}
        </if>
        <if test="dto.treeType !=null and dto.treeType !='' ">
            <if test="dto.treeType =='0'">
                and dept_code =#{dto.treeId}
            </if>

        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            AND device_name LIKE concat('%',#{dto.deviceName},'%')
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            AND defect_level = #{dto.defectLevel}
        </if>
        <if test="dto.createTime != null and dto.createTime != ''">
            AND DATE_FORMAT(create_time,'%Y-%m-%d') = #{dto.createTime}
        </if>
        <if test="dto.updateTime != null and dto.updateTime != ''">
            AND DATE_FORMAT(remove_time,'%Y-%m-%d') = #{dto.updateTime}
        </if>
        ORDER BY inspection_task_no desc, SUBSTR(remove_task_no,INSTR(remove_task_no,'-',-1,1) + 1) * 1
    </select>-->


    <select id="selectUnifiedTaskPage" resultType="com.allcore.main.code.solve.vo.RemoveTaskPageVO">
        SELECT * FROM (
        -- TMS_LINE设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.tower_name AS device_name,
        d.tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no,
        null as lightFileGuid,
        e.id as line_id
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_tower d ON c.device_id = d.id
        LEFT JOIN main_line e on d.line_id = e.id
        WHERE a.is_deleted = 0 AND c.device_type = 'TMS_LINE'

        UNION ALL

        -- FAN设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        null as tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no,
        null as lightFileGuid,
        null as line_id
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_fan d ON c.device_id = d.id
        WHERE a.is_deleted = 0 AND c.device_type = 'FAN'

        UNION ALL

        -- PV设备查询
        SELECT
        a.id,
        a.dept_code,
        a.remove_task_name,
        c.device_id,
        d.device_name,
        null as tower_no,
        c.defect_description,
        c.defect_level,
        c.big_file_guid as file_guid,
        b.create_time,
        b.remove_time,
        b.remove_user,
        b.remove_task_status,
        b.inspection_picture_tagging_id,
        b.remove_task_no,
        c.device_type,
        a.inspection_task_no,
        e.big_file_guid AS lightFileGuid,
        null as line_id
        FROM
        main_remove_task a
        LEFT JOIN main_remove_picture_tagging b ON a.id = b.remove_task_id
        LEFT JOIN main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
        LEFT JOIN main_pv_area d ON c.device_id = d.id
        LEFT JOIN (select distinct defect_key,big_file_guid from main_inspection_picture_tagging where device_type = 'PV' GROUP BY defect_key) e on c.light_defect_key = e.defect_key
        WHERE a.is_deleted = 0 AND c.device_type = 'PV'
        ) unified_result
        WHERE 1=1
        <if test="dto.deviceType != null and dto.deviceType != ''">
            AND device_type = #{dto.deviceType}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            AND dept_code LIKE concat(#{dto.deptCode},'%')
        </if>
        <if test="dto.removeTaskName != null and dto.removeTaskName != ''">
            AND remove_task_name = #{dto.removeTaskName}
        </if>
        <if test="dto.removeTaskStatus != null and dto.removeTaskStatus != ''">
            AND remove_task_status = #{dto.removeTaskStatus}
        </if>
        <if test="dto.removeUser != null and dto.removeUser != ''">
            AND remove_user = #{dto.removeUser}
        </if>
        <if test="dto.inspectionTaskNo != null and dto.inspectionTaskNo != ''">
            AND inspection_task_no = #{dto.inspectionTaskNo}
        </if>
        <if test="dto.removeTaskNo != null and dto.removeTaskNo != ''">
            AND remove_task_no = #{dto.removeTaskNo}
        </if>
        <if test="dto.deviceName != null and dto.deviceName != ''">
            AND device_name LIKE concat('%',#{dto.deviceName},'%')
        </if>
        <if test="dto.towerNo != null and dto.towerNo != ''">
            AND tower_no LIKE concat('%',#{dto.towerNo},'%')
        </if>
        <if test="dto.defectLevel != null and dto.defectLevel != ''">
            AND defect_level LIKE concat(#{dto.defectLevel},'%')
        </if>
        <if test="dto.defectDescription != null and dto.defectDescription != ''">
            AND defect_description LIKE concat(#{dto.defectDescription},'%')
        </if>
        <if test="dto.createTime != null and dto.createTime != ''">
            AND DATE_FORMAT(create_time,'%Y-%m-%d') = #{dto.createTime}
        </if>
        <if test="dto.updateTime != null and dto.updateTime != ''">
            AND DATE_FORMAT(remove_time,'%Y-%m-%d') = #{dto.updateTime}
        </if>
        <!-- treeType和treeId的处理逻辑 -->
        <if test="dto.treeType != null and dto.treeType == '0'.toString()">
            <!-- 当treeType='0'时，用deptCode匹配treeId -->
            <if test="dto.treeId != null and dto.treeId != '' and dto.treeId != '0'.toString()">
                AND dept_code LIKE concat(#{dto.treeId},'%')
            </if>
        </if>
        <if test="dto.treeType != null and dto.treeType != '0'.toString()">
            <if test="dto.treeType == 'TMS_LINE'">
                AND line_id = #{dto.treeId}
            </if>
            <if test="dto.treeType == 'TMS_TOWER'">
                AND device_type = 'TMS_LINE' AND device_id = #{dto.treeId}
            </if>
            <if test="dto.treeType == 'FAN'">
                AND device_type = 'FAN'
                <if test="dto.treeId != null and dto.treeId != '' and dto.treeId != '0'.toString()">
                    AND device_id = #{dto.treeId}
                </if>
            </if>
            <if test="dto.treeType == 'PV'">
                AND device_type = 'PV'
                <if test="dto.treeId != null and dto.treeId != '' and dto.treeId != '0'.toString()">
                    AND device_id = #{dto.treeId}
                </if>
            </if>
        </if>
        ORDER BY inspection_task_no desc, SUBSTR(remove_task_no,INSTR(remove_task_no,'-',-1,1) + 1) * 1
    </select>



</mapper>
