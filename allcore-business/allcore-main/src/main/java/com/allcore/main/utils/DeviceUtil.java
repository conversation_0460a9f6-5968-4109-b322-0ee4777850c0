package com.allcore.main.utils;

import java.util.regex.Pattern;

import com.allcore.common.constant.BasicConstant;

import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.utils.StringUtil;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/10/24 10:48
 **/
@Data
public class DeviceUtil {

	private static final String  LONGITUDE_REGEX = "^[\\-\\+]?(0(\\.\\d+)?|([1-9](\\d)?)(\\.\\d+)?|1[0-7]\\d{1}(\\.\\d+)?|180\\.0+)$";
	private static final String  LATITUDE_REGEX= "^[\\-\\+]?((0|([1-8]\\d?))(\\.\\d+)?|90(\\.0+)?)$";

	/**
	 * 基础信息校验
	 *
	 * @param code
	 * @param value
	 */
	public static void basicValueCheck(String code, String value) {
		if (StringUtil.isBlank(value)) {
			return;
		}
		switch (code) {
			case BasicConstant.LONGITUDE:
				if (!Pattern.matches(LONGITUDE_REGEX, value)) {
					throw new ServiceException(BasicConstant.FAIL_LONGITUDE_LATITUDE);
				}
				break;
			case BasicConstant.LATITUDE:
				if (!Pattern.matches(LATITUDE_REGEX, value)) {
					throw new ServiceException(BasicConstant.FAIL_LONGITUDE_LATITUDE);
				}
				break;
			default:
				break;
		}
	}

}
