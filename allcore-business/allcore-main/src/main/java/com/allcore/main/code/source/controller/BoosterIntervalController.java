package com.allcore.main.code.source.controller;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.source.dto.BoosterUpNameDTO;
import com.allcore.main.code.source.entity.Interval;
import com.allcore.main.code.source.service.IBoosterIntervalService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;


/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/boosterInterval")
@Api(value = "升压站、储能站间隔接口", tags = "升压站、储能站间隔接口")
public class BoosterIntervalController extends ZxhcController {

    private final IBoosterIntervalService boosterIntervalService;

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "新增", notes = "传入interval")
    public R save(@Valid @RequestBody Interval interval) {
        return R.status(boosterIntervalService.saveBoosterInterval(interval));
    }

    /**
     * 修改名称
     */
    @PostMapping("/updateName")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "修改名称", notes = "传入BoosterUpNameDTO")
    public R save(@Valid @RequestBody BoosterUpNameDTO dto) {
        return R.status(boosterIntervalService.updateName(dto));
    }

}
