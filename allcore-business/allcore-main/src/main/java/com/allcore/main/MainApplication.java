package com.allcore.main;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.cloud.client.AllcoreCloudApplication;
import com.allcore.core.launch.AllcoreApplication;
import com.allcore.main.code.defect.props.DefectProperties;
import com.allcore.main.code.inspection.props.MainProperties;
import com.allcore.main.code.source.props.SourceProperties;
import com.allcore.main.code.system.props.SystemProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 *  门户服务启动器
 *
 * <AUTHOR>
 * @since 2023-03-30
 **/
@EnableAsync
@AllcoreCloudApplication
@EnableConfigurationProperties({SourceProperties.class, SystemProperties.class, DefectProperties.class, MainProperties.class})
public class MainApplication {

	public static void main(String[] args) {
		AllcoreApplication.run(LauncherConstant.MAIN_SERVER_NAME, MainApplication.class, args);
	}

}
