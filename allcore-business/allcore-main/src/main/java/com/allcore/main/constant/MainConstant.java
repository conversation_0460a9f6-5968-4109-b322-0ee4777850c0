package com.allcore.main.constant;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
public interface MainConstant {
    /**
     * 逆变器健康分析
     */
    String INVERTER_CURVE_HEALTH = "inverter_curve_health";
    String GOOD = "good";
    String FINE = "fine";
    String POOR = "poor";
    String BAD = "bad";



    /**
     * 数值0
     */
    Integer INTEGER_NUM_ZERO = 0;
    Integer INTEGER_NUM_ONE = 1;
    Integer ZERO_ONE = -1;
    Integer ONE_SIX = 16;
    Integer NINE = 9;
    Integer TWO_ZERO_ZERO = 200;

    String DJI = "DJI";
    String LIGHT_T = "_T";
    String LIGHT_W = "_W";
    String LIGHT_Z = "_Z";
    String POINT_XLS = ".xls";
    String POINT_XLSX = ".xlsx";
    String POINT_JSON = ".json";
    String IS_OWN = "isOwn";
    String STATUS = "status";
    String DATA = "data";
    String MESSAGE = "message";


    String PRO = "PRO";
    String XJ = "XJ";
    String XQ = "XQ";
    String ONE = "1";
    String ZERO = "0";
    String TWO = "2";
    String THREE = "3";
    String FOUR = "4";

    /**
     * 文件名称解析异常
     */
    String FILE_NAME_ERROR = "文件名称解析异常！";
    String FILE_DATA_ERROR = "文件数据解析异常！";
    String DEVICE_NAMES = "设备名:";
    String NO_DEVICE = "未匹配到具体设备";

    /**
     * 空文件提示
     */
    String EMPTY_FILE = "空文件，请选择文件重新上传！";

    String DEVICE_TYPE_FAN = "fan";

    String DEVICE_TYPE_AREA = "area";

    String DEVICE_TYPE_STRING = "string";

    String DEVICE_TYPE_COMPONENT = "component";

    String DEVICE_TYPE_LINE = "line";

    String DEVICE_TYPE_TOWER = "tower";


    String INSPECTION_TYPE_FAN = "FAN";

    String INSPECTION_TYPE_AREA = "PV";
    String DEVICE_TYPE_BOOSTER = "BOOSTER";

    String DEVICE_TYPE_ENERGYSTORAGE = "ENERGYSTORAGE";

    String INSPECTION_TYPE_STRING = "STRING";

    String INSPECTION_TYPE_COMPONENT = "COMPONENT";

    String INSPECTION_TYPE_LINE = "TMS_LINE";

    String INSPECTION_TYPE_TOWER = "TMS_TOWER";

    String HAVE_ERROR_DEVICE = "已上传，属性正确的设备";

    String NO_RIGHT_DEVICE = "校验后，无合适的设备！";

    /**
     * 巡检计划，待执行
     */
    String PLAN_STATUS_WAIT = "6";
    /**
     * 巡检计划，执行中
     */
    String PLAN_STATUS_EXECUTION = "7";
    /**
     * 巡检计划，完成
     */
    String PLAN_STATUS_COMPLETE = "8";
    /**
     * 巡检计划，暂停
     */
    String PLAN_STATUS_PAUSE = "9";

    /**
     *  内蒙古区域公司站点id
     */
    String NEIMENGGU_DEPT_ID="1";

    interface QrCode{

        /*
         * 二维码批量下载压缩包前缀
         */
        String BATCH_DOWNLOAD_ZIP_PREFIX="qrcodes_batch_";

        /**
         * ZIP文件扩展名
         */
        String ZIP_FILE_EXTENSION=".zip";

        /**
         * 二维码文件扩展名
         */
        String QR_CODE_FILE_EXTENSION=".png";

        /**
         *  二维码图片宽度
         */
        Integer QR_CODE_WIDTH=300;

        /**
         *  二维码图片高度
         */
        Integer QR_CODE_HEIGHT=300;




        String HOST="http://***********:8000/api/allcore-app";

        String DEVICE_INFO_API_PATH="/inventory/getDeviceInfo";
    }

}
