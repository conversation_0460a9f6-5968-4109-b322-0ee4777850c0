package com.allcore.main.code.solve.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.main.code.solve.entity.RemovePictureTagging;
import com.allcore.main.code.solve.mapper.RemovePictureTaggingMapper;
import com.allcore.main.code.solve.service.IRemovePictureTaggingService;
import com.allcore.main.code.solve.vo.RemovePictureTaggingVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Service
public class RemovePictureTaggingServiceImpl extends ZxhcServiceImpl<RemovePictureTaggingMapper, RemovePictureTagging> implements IRemovePictureTaggingService {

	@Override
	public IPage<RemovePictureTaggingVO> selectRemovePictureTaggingPage(IPage<RemovePictureTaggingVO> page, RemovePictureTaggingVO removePictureTagging) {
		return page.setRecords(baseMapper.selectRemovePictureTaggingPage(page, removePictureTagging));
	}

}
