package com.allcore.main.code.inspection.controller;

import java.util.List;

import javax.validation.Valid;

import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.inspection.dto.PlanDTO;
import com.allcore.main.code.inspection.dto.PlanSaveDTO;
import com.allcore.main.code.inspection.service.IPlanService;
import com.allcore.main.code.inspection.vo.PlanVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/plan")
@Api(value = "巡检计划", tags = "巡检接口")
public class PlanController extends ZxhcController {

    private final IPlanService planService;
    private final CommonService commonService;

    @PostMapping(value = "/getInspectionDeviceList")
    @ApiOperation(value = "巡检计划巡检设备列表", notes = "巡检设备列表")
    public R<List<BasicCommonVO>> getCommonDeviceList(@RequestBody @Validated DeviceCommonDTO dto) {
        return commonService.getCommonDeviceList(dto);
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "巡检计划详情", notes = "传入id")
    public R<PlanVO> detail(@RequestParam("id") String id, @RequestParam("deviceType") String deviceType) {
        PlanVO detail = planService.getOnePlan(id, deviceType);
        return R.data(detail);
    }

    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "巡检计划分页", notes = "传入plan")
    public R<IPage<PlanVO>> page(PlanDTO plan, Query query) {
        IPage<PlanVO> pages = planService.selectPlanPage(Condition.getPage(query), plan);
        IPage<PlanVO> pageResult =  pages.convert(item -> {
            if(StringUtil.isNotBlank(item.getPlanStatus())){
                item.setPlanStatusZh(DictBizCache.getValue(MainBizEnum.PLAN_STATUS.getCode(), item.getPlanStatus()));
            }
            return item;
        });
          return R.data(pageResult);
    }

    /**
     * 巡检计划列表
     */
    @PostMapping("/list")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "巡检计划列表", notes = "传入plan")
    public R<List<PlanVO>> page(@Valid @RequestBody PlanDTO plan) {
        List<PlanVO> pages = planService.selectPlanList(plan);
        return R.data(pages);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "巡检计划新增", notes = "传入plan")
    public R save(@Valid @RequestBody PlanSaveDTO plan) {
        return planService.savePlan(plan);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "巡检计划修改", notes = "传入plan")
    public R update(@Valid @RequestBody PlanSaveDTO plan) {
        return planService.updatePlanById(plan);
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "巡检计划删除", notes = "传入ids")
    public R remove(@Valid @RequestBody IdsDTO dto) {
        return planService.deletePlanLogic(Func.toStrList(dto.getIds()));
    }

    /**
     * 计划暂定/继续执行
     */
    @PostMapping("/updatePauseOrExec")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "计划暂定或者继续执行", notes = "传入plan")
    public R updatePauseOrExec(@Valid @RequestBody PlanDTO plan) {
        return planService.updatePauseOrExec(plan);
    }

}
