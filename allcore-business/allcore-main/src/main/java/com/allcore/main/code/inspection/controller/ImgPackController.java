package com.allcore.main.code.inspection.controller;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.ImgPackDTO;
import com.allcore.main.code.inspection.dto.ImgPackQueryDTO;
import com.allcore.main.code.inspection.dto.ImgPackSaveDTO;
import com.allcore.main.code.inspection.service.IImgPackService;
import com.allcore.main.code.inspection.vo.ImgPackVO;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/imgpack")
@Api(value = "巡检影像", tags = "巡检接口")
public class ImgPackController extends ZxhcController {

    private final IImgPackService imgPackService;

    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "巡检影像分页", notes = "传入imgPack")
    public R<IPage<ImgPackVO>> page(ImgPackQueryDTO imgPack, Query query) {
        IPage<ImgPackVO> pages = imgPackService.selectImgPackPage(Condition.getPage(query), imgPack);
        return R.data(pages);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "巡检影像新增", notes = "传入imgPack")
    public R save(@Valid @RequestBody ImgPackDTO imgPack) {
        return R.data(imgPackService.modify(imgPack));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "巡检影像修改", notes = "传入imgPack")
    public R update(@Valid @RequestBody ImgPackDTO imgPack) {
        return R.data(imgPackService.modify(imgPack));
    }

    /**
     * 打包影像
     */
    @PostMapping("/images/pack")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "巡检影像打包影像", notes = "传入VideoDownloadRecordForSaveDTO")
    public R saveRecord(@RequestBody ImgPackSaveDTO dto) throws IOException {
        return R.status(imgPackService.savePack(dto));
    }

    /**
     * 巡检影像库获取图片详情
     * 
     * @param pictureGuid
     * @return
     */
    @GetMapping("/getPictureDetailByGuid")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "巡检影像库获取图片详情", notes = "传入pictureGuid")
    R<InspectionPictureVO> getPictureTaggingByGuid(@RequestParam("pictureGuid") String pictureGuid,
        @RequestParam("deviceType") String deviceType) {
        return imgPackService.getPictureTaggingByGuid(pictureGuid, deviceType);
    }

}
