package com.allcore.main.code.inspection.controller;

import java.util.List;

import javax.validation.Valid;

import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.vo.InspectionDeviceDetailVO;
import com.allcore.main.code.solve.dto.InspectionBatchSendDTO;
import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.inspection.service.IInspectionTaskService;
import com.allcore.main.code.inspection.vo.InspectionTaskForNestVO;
import com.allcore.main.code.inspection.vo.InspectionTaskVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/inspectiontask")
@Api(value = "巡检任务", tags = "巡检接口")
public class InspectionTaskController extends ZxhcController {

    private final IInspectionTaskService inspectionTaskService;

    /**
     * 生成 自增的任务编号
     */
    @GetMapping("/getTaskNo")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "生成 自增的工单编号", notes = "生成 自增的工单编号")
    public R<String> getWorkOrderCode() {
        return R.data(inspectionTaskService.getWorkOrderCode());
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "巡检任务详情", notes = "传入inspectionTask")
    public R<InspectionTaskVO> detail(@RequestParam("taskId") String taskId,
        @RequestParam("deviceType") String deviceType) {
        return inspectionTaskService.getInspectionTaskOne(taskId, deviceType);
    }

    /**
     * 巡检对象列表
     */
    @PostMapping("/deviceDetail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "巡检对象列表", notes = "传入inspectionTask")
    public R<List<InspectionDeviceDetailVO>> deviceDetail(@RequestBody List<ReTaskDTO> reTaskDTOList) {
        return inspectionTaskService.getTaskDeviceOne(reTaskDTOList);
    }


    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "巡检任务分页", notes = "传入inspectionTask")
    public R<IPage<InspectionTaskVO>> page(InspectionTaskDTO inspectionTask, Query query) {
        IPage<InspectionTaskVO> pages =
            inspectionTaskService.selectInspectionTaskPage(Condition.getPage(query), inspectionTask);
        return R.data(pages);
    }


    /**
     * 查询进行中的巡检任务
     * @param inspectionTask
     * @return
     */
    @GetMapping("/page-in-doing")
    @ApiOperationSupport(order=2)
    @ApiOperation(value="查询进行中的巡检任务",notes = "查询进行中的巡检任务")
    public R<IPage<InspectionTaskVO>> pageInDoing(InspectionTaskDTO inspectionTask, Query query) {
        IPage<InspectionTaskVO> pages =
            inspectionTaskService.selectInspectionTaskPageIndoing(Condition.getPage(query), inspectionTask);
        return R.data(pages);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "巡检任务新增", notes = "传入inspectionTask")
    public R save(@Valid @RequestBody InspectionTaskSaveDTO inspectionTask) {
        inspectionTask.setRemoteFlag(false);
        return inspectionTaskService.saveInspectionTask(inspectionTask);
    }

    /**
     * 批量新增
     */
    @PostMapping("/batchSave")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "巡检任务批量新增-无人机复检", notes = "传入inspectionTask")
    public R batchSave(@Valid @RequestBody List<InspectionTaskSaveDTO> inspectionTasks) {
        return inspectionTaskService.saveBatchInspectionTask(inspectionTasks);
    }

    @PostMapping("/saveByRemoteTask")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "巡检任务新增", notes = "传入inspectionTask")
    public R<String> saveByRemoteTask(@Valid @RequestBody RemoteTaskSaveDTO dto) {
        return inspectionTaskService.saveInspectionTaskByRemoteTask(dto);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "巡检任务修改", notes = "传入inspectionTask")
    public R update(@Valid @RequestBody InspectionTaskSaveDTO inspectionTask) {
        return inspectionTaskService.updatInspectionTaskById(inspectionTask);
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "巡检任务删除", notes = "传入ids")
    public R remove(@Valid @RequestBody IdsDTO dto) {
        return inspectionTaskService.deleteInspectionTaskLogic(Func.toStrList(dto.getIds()));
    }

    /**
     * 工单编号下拉框接口
     */
    @GetMapping("/getWorkOrderNoList")
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "工单编号下拉框接口", notes = "工单编号下拉框接口")
    public R<List<String>> getWorkOrderNoList(@RequestParam(value = "deptCode", required = false) String deptCode,
        @RequestParam(value = "deviceType", required = true) String deviceType) {
        List<String> data = inspectionTaskService.getWorkOrderNoList(deptCode, deviceType);
        return R.data(data);
    }

    /**
     * 办结
     */
    @GetMapping("/updateStatus")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "巡检任务下发或办结", notes = "根据任务id和状态标识下发或办结巡检任务")
    public R completion(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        return inspectionTaskService.completionTask(taskId, status);
    }

    @PostMapping("/batchSend")
    @ApiOperationSupport(order=20)
    @ApiOperation(value = "巡检任务批量下发", notes = "")
    public R batchSend(@RequestBody List<InspectionBatchSendDTO>dto ) {
        return inspectionTaskService.batchSend(dto);
    }

    /**
     * 巡检结果分页
     */
    @GetMapping("/resultPage")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "巡检结果分页", notes = "传入inspectionTask")
    public R<IPage<InspectionTaskVO>> resultPage(InspectionTaskDTO inspectionTask, Query query) {
        IPage<InspectionTaskVO> pages =
            inspectionTaskService.selectInspectionTaskResultPage(Condition.getPage(query), inspectionTask);
        return R.data(pages);
    }

    /**
     * 查看全量巡检图
     */
    @GetMapping("/getAllInspectionPic")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "巡检图", notes = "传入inspectionTask")
    public R getAllInspectionPic(@RequestParam("taskId") String taskId, @RequestParam("deviceType") String deviceType,
        String deviceId) {
        return inspectionTaskService.getAllInspectionPic(taskId, deviceType, deviceId);
    }

    /**
     * 巡检结果详情
     */
    @GetMapping("/resultdDetail")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "巡检结果详情", notes = "传入inspectionTask")
    public R<InspectionTaskVO> resultdDetail(@RequestParam("taskId") String taskId,
        @RequestParam("deviceType") String deviceType) {
        return inspectionTaskService.getInspectionResultTaskOne(taskId, deviceType);
    }

    /**
     * 巡检任务编号列表
     */
    @GetMapping("/getTaskNoList")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "巡检任务编号", notes = "传入deptCode")
    public R<List<InspectionTaskVO>> getTaskNo(@RequestParam("deptCode") String deptCode) {
        return inspectionTaskService.getTaskNo(deptCode);
    }

    // @GetMapping("/pvInfoForNest")
    // @ApiOperationSupport(order = 9)
    // @ApiOperation(value = "远程巡检-当前机场巡检区域", notes = "")
    // public R<PvArea> pvInfoForNest(@RequestParam String airportNestId) {
    // //根据机场id获取当前执行的机巢任务、根据机巢任务获取当前的巡检任务
    // return R.data(inspectionTaskService.pvInfoForNest(airportNestId));
    // }

    /**
     * （远程巡检-任务信息）
     * 
     * <AUTHOR>
     * @date 2023/11/22 21:49
     * @param airportNestId
     * @return com.allcore.core.tool.api.R<com.allcore.main.code.inspection.vo.InspectionTaskForNestVO>
     */
    @GetMapping("/taskForNest")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "远程巡检-任务信息", notes = "")
    public R<InspectionTaskForNestVO> taskForNest(@RequestParam String airportNestId, @RequestParam String deviceType) {
        // 根据机场id获取当前执行的机巢任务、根据机巢任务获取当前的巡检任务
        return R.data(inspectionTaskService.taskForNest(airportNestId, deviceType));
    }
    /**
     * 新增报告图片
     */
    @PostMapping("/addReportPic")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "新增报告图片", notes = "传入dto")
    public R addReportPic(@Valid @RequestBody InspectionTaskReportPicDTO dto) {
        return inspectionTaskService.addReportPic(dto);
    }
    /**
     * 关联图片的设备列表
     */
    @PostMapping("/picDeviceList")
    @ApiOperationSupport(order = 16)
    @ApiOperation(value = "关联图片的设备列表", notes = "传入dto")
    public R picDeviceList(@Valid @RequestBody InspectionTaskReportPicDTO dto) {
        return inspectionTaskService.picDeviceList(dto);
    }


    /**
     *  取消下发
     */
    @GetMapping("/cancelSend")
    @ApiOperationSupport(order = 18)
    @ApiOperation(value = "取消下发", notes = "传入任务ID")
    public R cancelSend(@RequestParam("taskId") String taskId) {
        return inspectionTaskService.cancelSend(taskId);
    }


    /**
     *  统计进行中的巡检任务数量
     */
    @GetMapping("/countInspectionTask")
    @ApiOperationSupport(order = 19)
    @ApiOperation(value = "统计进行中的巡检任务数量", notes = "统计进行中的巡检任务数量")
    public R countInspectionTask() {
        return inspectionTaskService.countInspectionTask();
    }

    /**
     *  根据巡检类型
     */
}
