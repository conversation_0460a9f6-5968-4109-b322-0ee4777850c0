package com.allcore.main.utils;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.main.code.source.excel.BoosterExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 升压站模版导入校验
 * </p>
 *
 * @author: sunkun Date: 2023/8/21
 */
@Component
@Slf4j
public class BoosterImportVerifyHandler implements IExcelVerifyHandler<BoosterExcel> {



    @Override
    public ExcelVerifyHandlerResult verifyHandler(BoosterExcel  boosterExcel) {
        boosterExcel.setDeptCode(AuthUtil.getDeptCode());
        return new ExcelVerifyHandlerResult(true);
    }

}
