package com.allcore.main.utils;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.main.code.source.entity.Fan;
import com.allcore.main.code.source.entity.FanBox;
import com.allcore.main.code.source.excel.FanBoxExcel;
import com.allcore.main.code.source.mapper.FanBoxMapper;
import com.allcore.main.code.source.service.IFanBoxService;
import com.allcore.main.code.source.service.IFanService;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import dm.jdbc.dbaccess.Auth;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.StringJoiner;

import static com.allcore.main.constant.MainConstant.NINE;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-06-12 09:22
 **/

@Component
@Slf4j
@AllArgsConstructor
public class FanBoxImportHandler implements IExcelVerifyHandler<FanBoxExcel> {



    private final FanBoxMapper fanBoxMapper;

    private final IFanService fanService;


    @Override
    public ExcelVerifyHandlerResult verifyHandler(FanBoxExcel fanBoxExcel) {

        StringJoiner joiner = new StringJoiner(",");

        // 场站级权限验证：只有场站级单位（部门代码长度为9位）才能导入箱变
        if (AuthUtil.getDeptCode().length() != NINE) {
            joiner.add("非场站级单位，没有权限导入箱变!");
        }

        String stationName = fanBoxExcel.getStationName();
        String deptCode = AuthUtil.getDeptCode();
        String tenantId = AuthUtil.getTenantId();

        // 验证站点名称是否存在
        String deptIds = SysCache.getDeptIds(tenantId, stationName);
        if (StringUtil.isBlank(deptIds)) {
            joiner.add("站点名称：" + stationName + "不存在");
        } else {
            // 安全地获取第一个部门ID，避免数组越界
            String[] deptIdArray = deptIds.split(",");
            if (deptIdArray.length == 0 || StringUtil.isBlank(deptIdArray[0])) {
                joiner.add("站点名称：" + stationName + "对应的部门信息异常");
            } else {
                String deptId = deptIdArray[0];
                Dept dept = SysCache.getDept(deptId);
                if (Func.isEmpty(dept)) {
                    joiner.add("站点名称：" + stationName + "不存在");
                } else {
                    // 验证部门权限：站点必须是当前单位或其下级单位
                    String stationDeptCode = dept.getDeptCode();
                    if (!stationDeptCode.startsWith(deptCode)) {
                        joiner.add("站点名称：" + stationName + "不是当前单位或者其下级单位");
                    } else {
                        // 设置部门代码（只有在权限验证通过后才设置）
                        fanBoxExcel.setDeptCode(stationDeptCode);

                        // 验证关联的风机是否存在
                        Fan fan = fanService.getOne(Wrappers.<Fan>lambdaQuery()
                                .eq(Fan::getDeviceName, fanBoxExcel.getFanName())
                                .likeRight(Fan::getDeptCode, fanBoxExcel.getDeptCode()));
                        if (Func.notNull(fan)) {
                            fanBoxExcel.setFanId(fan.getId());
                        } else {
                            joiner.add("风机名称：" + fanBoxExcel.getFanName() + "不存在");
                        }

                        // 检查重复性（只有在前面验证都通过后才检查）
                        cheatRepeat(fanBoxExcel, joiner);
                    }
                }
            }
        }

        // 统一返回验证结果
        if (joiner.length() != 0) {
            return new ExcelVerifyHandlerResult(false, joiner.toString());
        }

        return new ExcelVerifyHandlerResult(true);
    }

    private void cheatRepeat(FanBoxExcel fanBoxExcel, StringJoiner joiner) {
        Optional.ofNullable(fanBoxMapper.selectOne(new LambdaQueryWrapper<FanBox>().eq(FanBox::getFanBoxNumber, fanBoxExcel.getFanBoxNumber())
                .likeRight(FanBox::getDeptCode, fanBoxExcel.getDeptCode()).eq(FanBox::getIsDeleted, 0))).ifPresent(f -> {
            joiner.add("箱变编号重复:" + fanBoxExcel.getFanBoxNumber());
        });

        Optional.ofNullable(fanBoxMapper.selectOne(new LambdaQueryWrapper<FanBox>().eq(FanBox::getFanId,fanBoxExcel.getFanId())
                .likeRight(FanBox::getDeptCode, fanBoxExcel.getDeptCode()).eq(FanBox::getIsDeleted, 0))).ifPresent(f->{
            joiner.add(fanBoxExcel.getFanName()+"已关联其他箱变");
        });

    }
}
