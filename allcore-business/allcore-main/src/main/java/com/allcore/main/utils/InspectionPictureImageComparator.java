package com.allcore.main.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;

import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.filesystem.vo.AllcoreFileVO;

import static com.allcore.main.constant.MainConstant.*;

/**
 * 排序比较
 *
 * <AUTHOR>
 * @date 2023/12/15 16:03
 **/
public class InspectionPictureImageComparator implements Comparator<AllcoreFileVO> {
    @Override
    public int compare(AllcoreFileVO vo1, AllcoreFileVO vo2) {

        String name1 = returnShortName(vo1.getOriginalName());
        String name2 = returnShortName(vo2.getOriginalName());
        return name1.compareTo(name2);
    }

    public static String returnShortName(String str) {
        if(str.contains(LIGHT_T)){
            str = str.replace(LIGHT_T,"");
        }
        if(str.contains(LIGHT_W)){
            str = str.replace(LIGHT_W,"");
        }
        if(str.contains(LIGHT_Z)){
            str = str.replace(LIGHT_Z,"");
        }

        str = filterDjTime(str);

        return str;
    }
    public static String filterDjTime(String str) {
        //去除时间  时分秒 部分
        if(str.contains(DJI)){
            String newStr = "";
            String[] strArr = str.split(StringPool.UNDERSCORE);
            for(int i=0;i<strArr.length;i++){
                if(isDateVail(strArr[i])){
                    newStr = newStr + strArr[i].substring(0,8)+StringPool.UNDERSCORE;
                }else if(i==strArr.length-1){
                    newStr = newStr + strArr[i];
                }else {
                    newStr = newStr + strArr[i]+StringPool.UNDERSCORE;
                }
            }
            return newStr;
        }else{
            return str;
        }
    }
    public static Boolean isDateVail(String time){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME_MINI);
        boolean flag = true;
        try{
            LocalDateTime.parse(time,dtf);
        }catch (Exception e){
            flag = false;
        }
        return flag;
    }
}
