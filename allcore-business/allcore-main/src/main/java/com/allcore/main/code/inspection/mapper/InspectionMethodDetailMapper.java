package com.allcore.main.code.inspection.mapper;

import com.allcore.main.code.inspection.entity.InspectionMethodDetail;
import com.allcore.main.code.inspection.vo.InspectionMethodDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
public interface InspectionMethodDetailMapper extends BaseMapper<InspectionMethodDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectionMethodDetail
	 * @return
	 */
	List<InspectionMethodDetailVO> selectInspectionMethodDetailPage(IPage page, InspectionMethodDetailVO inspectionMethodDetail);

}
