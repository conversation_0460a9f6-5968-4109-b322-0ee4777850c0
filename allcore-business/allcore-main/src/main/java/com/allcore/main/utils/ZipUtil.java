package com.allcore.main.utils;

import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Collections;

/**
 * zip文件工具类
 *
 * <AUTHOR>
 * @date 2022/04/13 14:28
 **/
@Slf4j
public class ZipUtil {
    private static Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    /** 文件分隔符 **/
    public static final String FILE_SEPARATOR = File.separator;

    /**
     * 使用给定密码压缩指定文件或文件夹到指定位置 dest可传最终压缩文件存放的绝对路径，也可以传存放目录，也可以传null或者""
     * 如果传null或者""则将压缩文件存放在当前目录，即跟源文件同目录,压缩文件名取源文件名，以.zip为后缀；
     * 如果以路径分隔符(File.separator)结尾，则视为目录，压缩文件名取源文件名，以.zip为后缀，否则视为文件名。
     *
     * @param src
     *            要压缩的文件或文件夹路径
     * @param dest
     *            压缩文件存放路径
     * @param zipFileName
     *            压缩文件名
     * @param createDir
     *            是否在压缩文件里创建目录，仅在压缩文件为目录时有效. 如果为false，将直接压缩目录下文件到压缩文件.
     * @param excludeFileTypes
     *            排除的文件类型
     * @return 最终的压缩文件存放的绝对路径，如果为null则说明压缩失败.
     */
    public static String zip(String src, String dest, String zipFileName, boolean createDir, String... excludeFileTypes)
        throws ZipException {
        if (StringUtils.isBlank(src)) {
            throw new ZipException("待压缩文件路径不能为空");
        }
        if (StringUtils.isBlank(zipFileName)) {
            throw new ZipException("压缩包名不能为空");
        }

        // 待压缩的源文件路径
        File srcFile = new File(src);
        // 构建压缩文件存放路径
        dest = buildDestZipFilePath(srcFile, dest, zipFileName);

        // 压缩参数
        ZipParameters parameters = new ZipParameters();
        // 压缩方式
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        // 压缩级别
        parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);

        try {
            ZipFile zipFile = new ZipFile(dest);
            if (srcFile.isDirectory()) {
                // 如果不创建目录的话，将直接把给定目录下的文件压缩到压缩文件，即没有目录结构
                if (!createDir) {
                    File[] subFiles = srcFile.listFiles();
                    if (subFiles != null && subFiles.length > 0) {
                        ArrayList<File> temp = new ArrayList<File>();

                        if (excludeFileTypes != null && excludeFileTypes.length > 0) {
                            for (File file : subFiles) {
                                for (String fileType : excludeFileTypes) {
                                    if (!file.getPath().toLowerCase().endsWith(fileType.toLowerCase())) {
                                        temp.add(file);
                                    }
                                }
                            }
                        } else {
                            Collections.addAll(temp, subFiles);
                        }
                        zipFile.addFiles(temp, parameters);
                    }

                    return dest;
                }

                zipFile.addFolder(srcFile, parameters);
            } else {
                if (excludeFileTypes != null && excludeFileTypes.length > 0) {
                    for (String fileType : excludeFileTypes) {
                        if (!srcFile.getPath().toLowerCase().endsWith(fileType.toLowerCase())) {
                            zipFile.addFile(srcFile, parameters);
                        }
                    }
                } else {
                    zipFile.addFile(srcFile, parameters);
                }
            }

            return dest;
        } catch (ZipException e) {
            logger.error(e.getMessage(), e);
            throw new ZipException("文件压缩发生异常", e);
        }
    }

    /**
     * 构建压缩文件存放路径，如果不存在将会创建 传入的可能是文件名或者目录，也可能不传，此方法用以转换最终压缩文件的存放路径
     *
     * @param srcFile
     *            源文件
     * @param dest
     *            压缩目标路径
     * @param zipFileName
     *            压缩文件名
     * @return 正确的压缩文件存放路径
     */
    private static String buildDestZipFilePath(File srcFile, String dest, String zipFileName) {
        if (StringUtils.isBlank(dest)) {
            dest = srcFile.getParent();
        } else {
            // 创建压缩文件存放目录
            createDestDirIfNecessary(dest);
        }
        dest = String.format("%s%s%s", dest, FILE_SEPARATOR, zipFileName);

        return dest;
    }

    /**
     * 创建压缩文件存放目录
     *
     * @param dest
     *            指定的存放路径
     */
    private static void createDestDirIfNecessary(String dest) {
        File destDir = new File(dest);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }
    }

    /**
     * 下载文件夹
     *
     * @param response
     * @param filePath
     */
    public static void zipDownloadRelativePath(HttpServletResponse response, String filePath, String fileName)
        throws UnsupportedEncodingException {
        fileName = fileName + ".zip";
        // 如果有中文需要转码
        response.setHeader("Content-disposition", "attachment;filename=\"" + new String(fileName.getBytes(), "iso-8859-1") + "\"");
        // 定义相关流
        // 用于浏览器下载响应
        OutputStream out = null;
        // 用于读压缩好的文件
        // 用缓存性能会好一些
        BufferedInputStream bis = null;
        String zipPath = "";
        try {
            out = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            log.info("filePath:{}", filePath);
            zipPath = zip(filePath, null, fileName, true);
            byte[] bt = new byte[10 * 1024];
            int size = 0;
            bis = new BufferedInputStream(new FileInputStream(zipPath), 10 * 1024);
            int len1=10;
			int len2=1024;
            while ((size = bis.read(bt, 0, len1 * len2)) != -1) {
                out.write(bt, 0, size);
            }
			out.flush();
        } catch (ZipException e) {
            e.printStackTrace();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {// 关闭相关流
            try {
                File file = new File(zipPath);
                if (file.exists()) {
                    file.delete();
                }
                if (bis != null) {
                    bis.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
                log.error("流关闭出错！");
            }
        }

    }

	/**
	 * 迭代删除文件夹
	 *
	 * @param dirPath 文件夹路径
	 */
	public static void deleteDir(String dirPath) {
		File file = new File(dirPath);
		if (file.isFile()) {
			file.delete();
		} else {
			File[] files = file.listFiles();
			if (files == null) {
				file.delete();
			} else {
				for (int i = 0; i < files.length; i++) {
					deleteDir(files[i].getAbsolutePath());
				}
				file.delete();
			}
		}
	}

}
