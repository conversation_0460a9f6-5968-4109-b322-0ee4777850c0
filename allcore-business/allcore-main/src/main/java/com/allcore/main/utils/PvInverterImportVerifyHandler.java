package com.allcore.main.utils;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.source.entity.PvInverter;
import com.allcore.main.code.source.excel.PvInverterExcel;
import com.allcore.main.code.source.mapper.PvInverterMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.StringJoiner;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-07-10 17:28
 **/
@Component
@Slf4j
@AllArgsConstructor
public class PvInverterImportVerifyHandler implements IExcelVerifyHandler<PvInverterExcel> {

    private final PvInverterMapper pvInverterMapper;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(PvInverterExcel pvInverterExcel) {


        StringJoiner joiner = new StringJoiner(",");

        String deptCode = AuthUtil.getDeptCode();
        if(Func.isBlank(deptCode)){
            joiner.add("没有获取到单位信息");
        }
        pvInverterExcel.setDeptCode(deptCode);
        checkRepeat(pvInverterExcel,joiner);
        if(joiner.length() != 0) {
            return new ExcelVerifyHandlerResult(false, joiner.toString());
        }
        return new ExcelVerifyHandlerResult(true);
    }

    private void checkRepeat(PvInverterExcel pvInverterExcel,StringJoiner joiner) {
        Optional.ofNullable(pvInverterMapper.selectOne(new LambdaQueryWrapper<PvInverter>().eq(PvInverter::getDeviceName, pvInverterExcel.getDeviceName())
                .eq(PvInverter::getDeptCode, pvInverterExcel.getDeptCode()).eq(PvInverter::getIsDeleted, 0))).ifPresent(f -> {
            joiner.add("设备名称重复");
        });
    }
}
