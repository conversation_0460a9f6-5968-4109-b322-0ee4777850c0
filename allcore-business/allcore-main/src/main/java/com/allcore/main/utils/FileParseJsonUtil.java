package com.allcore.main.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.springframework.web.multipart.MultipartFile;

import com.allcore.main.code.source.parser.PVJsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;

/**
 * 文件转化成json格式util
 *
 * <AUTHOR>
 * @Date 2022/08/03 10:36
 **/
public class FileParseJsonUtil {
    private static final ObjectMapper OBJECT_MAPPER;
    static {
        OBJECT_MAPPER = new ObjectMapper();
        //过时的 PropertyNamingStrategy.LOWER_CAMEL_CASE
        OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 将json字符串转为实体类
     *
     * @param json
     * @return
     */
    public static PVJsonParser loadJson(String json) {
        PVJsonParser pvJsonParser = null;
        try {
            // jackson解析
            pvJsonParser = OBJECT_MAPPER.readValue(json, new TypeReference<PVJsonParser>() {});
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return pvJsonParser;
    }

    /**
     * 读取文件
     *
     * @param file
     * @return
     */
    public static String loadFile(MultipartFile file) {
        // 获取文件流
        try (InputStream is = file.getInputStream()) {
            InputStreamReader isReader = new InputStreamReader(is, StandardCharsets.UTF_8);
            BufferedReader br = new BufferedReader(isReader);
            // 内容拼接
            return br.lines().collect(Collectors.joining());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;

    }
}
