package com.allcore.main.utils;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.source.entity.Box;
import com.allcore.main.code.source.excel.BoxExcel;
import com.allcore.main.code.source.mapper.BoxMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.StringJoiner;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-07-11 09:53
 **/

@Component
@Slf4j
@AllArgsConstructor
public class BoxImportVerifyHandler implements IExcelVerifyHandler<BoxExcel> {

    private final BoxMapper boxMapper;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(BoxExcel boxExcel) {

        StringJoiner joiner = new StringJoiner(",");

        String deptCode= AuthUtil.getDeptCode();
        if(Func.isBlank(deptCode)){
            joiner.add("没有获取到单位信息");
        }
        boxExcel.setDeptCode(deptCode);
        checkRepeat(boxExcel,joiner);
        if(joiner.length() != 0) {
            return new ExcelVerifyHandlerResult(false, joiner.toString());
        }
        return new ExcelVerifyHandlerResult(true);
    }

    private void checkRepeat(BoxExcel boxExcel, StringJoiner joiner) {
        Optional.ofNullable(boxMapper.selectOne(new LambdaQueryWrapper<Box>().eq(Box::getDeviceName, boxExcel.getDeviceName())
                .eq(Box::getDeptCode, boxExcel.getDeptCode()).eq(Box::getIsDeleted, 0))).ifPresent(f -> {
            joiner.add("设备名称重复");
        });
    }
}
