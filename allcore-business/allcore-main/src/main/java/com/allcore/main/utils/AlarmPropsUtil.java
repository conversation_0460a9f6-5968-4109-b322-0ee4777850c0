package com.allcore.main.utils;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-08-03 10:37
 **/

public class AlarmPropsUtil {


    /**
     * 读取指定文件里的所有key(告警字段)
     *
     */
    public static List<String>fields(String fileBaseName){
        return new ArrayList<>(loadProps(fileBaseName).stringPropertyNames());
    }


    /**
     * 根据key取中文含义
     * @param fileBaseName
     * @param key
     * @return
     */
    public static String translate(String fileBaseName, String key){
        return loadProps(fileBaseName).getProperty(key,key);
    }


    /**
     *  缓存，避免重复加载
     */
    public static final ConcurrentHashMap<String, Properties>CACHE=
            new ConcurrentHashMap<>();

    private static Properties loadProps(String fileBaseName){
        return CACHE.computeIfAbsent(fileBaseName,k->{
            String path="/templates/source/alarm/"+k+".properties";
            try(InputStream in=AlarmPropsUtil.class.getResourceAsStream(path)){
                Properties p=new Properties();
                p.load(new InputStreamReader(in, StandardCharsets.UTF_8));
                return p;

            }catch (Exception e){
                throw new IllegalStateException("找不到字典文件:"+path,e);
            }
        });
    }
}
