package com.allcore.main.code.inspection.wrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.allcore.system.cache.SysCache;
import com.allcore.user.cache.UserCache;

/**
 * 翻译
 *
 * <AUTHOR>
 * @date 2023/10/07 09:36
 **/
public class InspectionPictureWrapper extends BaseEntityWrapper<InspectionPictureVO, InspectionPictureVO> {

    public static InspectionPictureWrapper build() {
        return new InspectionPictureWrapper();
    }

    @Override
    public InspectionPictureVO entityVO(InspectionPictureVO entity) {

        InspectionPictureVO vo = Objects.requireNonNull(BeanUtil.copy(entity, InspectionPictureVO.class));
        translate(vo);
        return vo;
    }

    private void translate(InspectionPictureVO vo) {
        // 单位名称Zh
        String zh = SysCache.getDeptNameByDeptCode(vo.getDeptCode());
        vo.setDeptCodeZh(StringUtil.isBlank(zh) ? "" : zh);
        if (ObjectUtil.isNotEmpty(vo.getCreateUser())) {
            String userName = UserCache.getUser(vo.getCreateUser()).getName();
            vo.setCreateUserZh(StringUtil.isBlank(userName) ? "" : userName);
        }
    }

    public List<InspectionPictureVO> listVoByVo(List<InspectionPictureVO> list) {

        return (List<InspectionPictureVO>)list.stream().map(vo -> {
            translate(vo);
            return vo;
        }).collect(Collectors.toList());
    }

}
