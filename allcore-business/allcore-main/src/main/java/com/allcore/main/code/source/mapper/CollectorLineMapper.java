package com.allcore.main.code.source.mapper;

import com.allcore.main.code.source.dto.CollectorLineDTO;
import com.allcore.main.code.source.entity.CollectorLine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface CollectorLineMapper extends BaseMapper<CollectorLine> {

    /**
     * 自定义分页
     *
     * @param page
     * @param deviceQuery
     * @return
     */
    List<CollectorLine> selectLinePage(IPage page, CollectorLineDTO deviceQuery);

}
