package com.allcore.main.code.defect.service.impl;

import com.allcore.core.redis.cache.AllcoreRedis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.allcore.core.algorithm.service.IAlgorithmToolComponentService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AlgorithmToolComponentServiceImpl implements IAlgorithmToolComponentService {

	private final AllcoreRedis redis;
	/**
	 * 缺陷识别任务在redis中的key前缀
	 */
	private static final String DEFECT_TASK_CODE = "defect:task:code:";

	@Override
	public void onInterrupt(String recognitionTaskId, String message) {
		log.info("onInterrupt,recognitionTaskId={}, message={}", recognitionTaskId, message);
	}

	@Override
	public String get(String taskId) {
		Object object = redis.get(DEFECT_TASK_CODE.concat(taskId));
		if(Objects.nonNull(object)){
			return String.valueOf(object);
		}
		return null;
	}

	@Override
	public void put(String taskId, String recognitionTaskId) {
		redis.setEx(DEFECT_TASK_CODE.concat(taskId), recognitionTaskId, Duration.ofDays(2));
	}

	@Override
	public void remove(String taskId) {
		redis.del(DEFECT_TASK_CODE.concat(taskId));
	}

}
