package com.allcore.main.code.source.service.impl;

import static com.allcore.common.constant.BasicConstant.DEVICE_NAME_NOT_ONLY;

import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.source.dto.UavModelDTO;
import com.allcore.main.code.source.dto.UavModelSaveDTO;
import com.allcore.main.code.source.entity.UavModel;
import com.allcore.main.code.source.entity.UavModelFile;
import com.allcore.main.code.source.mapper.UavModelMapper;
import com.allcore.main.code.source.service.IUavModelFileService;
import com.allcore.main.code.source.service.IUavModelService;
import com.allcore.main.code.source.vo.UavModelVO;
import com.allcore.main.code.source.wrapper.UavModelWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
@AllArgsConstructor
public class UavModelServiceImpl extends ZxhcServiceImpl<UavModelMapper, UavModel> implements IUavModelService {

    private final IUavModelFileService uavModelFileService;
    private final IOssClient ossClient;

    @Override
    public IPage<UavModelVO> selectUavModelPage(IPage<UavModelVO> page, UavModelDTO uavModel) {
        if (Func.isBlank(uavModel.getDeptCode())) {
            uavModel.setDeptCode(AuthUtil.getDeptCode());
        }
        // 自定义列表查询
        List<UavModel> list = baseMapper.selectUavModelPage(page, uavModel);
        // 构建返回列表
        List<UavModelVO> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            voList = BeanUtil.copy(list, UavModelVO.class);
        }
        return page.setRecords(UavModelWrapper.build().listVoByVo(voList));
    }

    /**
     * 同级下同名设备
     *
     * @param uavType
     * @param uavBrand
     * @param modelName
     * @param id
     */
    public void checkRepeat(String uavType, String uavBrand, String modelName, String id) {
        Optional.ofNullable(super.getOne(
            new LambdaQueryWrapper<UavModel>().eq(UavModel::getUavBrand, uavType).eq(UavModel::getUavBrand, uavBrand)
                .eq(UavModel::getModelName, modelName).ne(Objects.nonNull(id), UavModel::getId, id)))
            .ifPresent(f -> {
                throw new ServiceException(DEVICE_NAME_NOT_ONLY + f.getModelName());
            });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveUavModel(UavModelSaveDTO dto) {
        UavModel entity = BeanUtil.copy(dto, UavModel.class);
        // 同级不能有同名设备
        this.checkRepeat(entity.getUavType(), entity.getUavBrand(), entity.getModelName(), null);

        if (!CollectionUtils.isEmpty(entity.getFileGuids())) {
            List<UavModelFile> list = Lists.newArrayList();
            entity.getFileGuids().forEach(d -> {
                UavModelFile modelFileEntity = new UavModelFile();
                modelFileEntity.setFileGuid(d);
                modelFileEntity.setModelId(entity.getId());
                list.add(modelFileEntity);
            });
            uavModelFileService.saveBatch(list);
        }
        return R.status(this.save(entity));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateUavModelById(UavModelSaveDTO dto) {
        UavModel entity = BeanUtil.copy(dto, UavModel.class);
        // 同级不能有同名设备
        this.checkRepeat(entity.getUavType(), entity.getUavBrand(), entity.getModelName(), entity.getId());
        uavModelFileService.remove(new LambdaQueryWrapper<UavModelFile>().eq(UavModelFile::getModelId, entity.getId()));
        if (!CollectionUtils.isEmpty(entity.getFileGuids())) {
            List<UavModelFile> list = Lists.newArrayList();
            entity.getFileGuids().forEach(d -> {
                UavModelFile modelFileEntity = new UavModelFile();
                modelFileEntity.setFileGuid(d);
                modelFileEntity.setModelId(entity.getId());
                list.add(modelFileEntity);

            });
            uavModelFileService.saveBatch(list);
        }
        return R.status(this.updateById(entity));
    }

    @Override
    public R deleteUavModelLogic(List<String> strList) {
        uavModelFileService.remove(new LambdaQueryWrapper<UavModelFile>().in(UavModelFile::getModelId, strList));
        return R.status(this.remove(new LambdaQueryWrapper<UavModel>().in(UavModel::getId, strList)));
    }

    @Override
    public R<UavModelVO> getUavModelOne(String id) {
        UavModel one = this.getOne(Wrappers.<UavModel>lambdaQuery().eq(UavModel::getId, id));
        List<UavModelFile> fileGuid =
            uavModelFileService.list(new LambdaQueryWrapper<UavModelFile>().eq(UavModelFile::getModelId, one.getId()));
        UavModelVO vo = UavModelWrapper.build().entityVO(one);
        if (fileGuid.size() > 0) {
            List<Map<String, String>> fileUrls = Lists.newArrayList();

            fileGuid.forEach((f -> {
                R<AllcoreFileVO> fileDetailTwo = ossClient.getFileDetail(f.getFileGuid());
                if (fileDetailTwo.isSuccess()) {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("url", fileDetailTwo.getData().getDynamicPath());
                    map.put("guid", f.getFileGuid());
                    fileUrls.add(map);
                }
            }));
            vo.setFileUrls(fileUrls);
        }
        return R.data(vo);
    }

    @Override
    public R findListByUavTypeAndUavBrand(String uavType, String uavBrand, String deptCode) {
        if (StringUtil.isBlank(deptCode)) {
            deptCode = AuthUtil.getDeptCode();
        }
        List<Map<String, String>> listmap = Lists.newArrayList();
        List<UavModel> list = this.list(new LambdaQueryWrapper<UavModel>().eq(UavModel::getUavType, uavType)
            .eq(StringUtil.isNotBlank(uavBrand), UavModel::getUavBrand, uavBrand));
        if (list.size() > 0) {
            list.forEach(d -> {
                Map<String, String> map = Maps.newHashMap();
                map.put("modelId", d.getId());
                map.put("modelName", d.getModelName());
                listmap.add(map);

            });
        }
        return R.data(listmap);

    }

}
