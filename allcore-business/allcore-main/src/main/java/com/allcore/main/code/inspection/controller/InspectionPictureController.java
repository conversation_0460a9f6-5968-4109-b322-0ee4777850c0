package com.allcore.main.code.inspection.controller;

import java.util.List;

import javax.validation.Valid;

import com.alibaba.fastjson.JSONObject;
import com.allcore.external.dto.AirportUploadTaskPicDTO;
import com.allcore.external.dto.BackStatusDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.main.code.inspection.vo.ImagesVO;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.FileGuidsDTO;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.service.IInspectionPictureService;
import com.allcore.main.code.inspection.vo.PatrolDataImageVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/inspectionpicture")
@Api(value = "巡检图片", tags = "巡检接口")
public class InspectionPictureController extends ZxhcController {

    private final IInspectionPictureService inspectionPictureService;

    /**
     * 根据巡检任务查图片
     */
    @PostMapping("/picQuery")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "巡检图片根据巡检任务查图片", notes = "传入dto")
    public R picQuery(@Valid @RequestBody InspectionTaskPicQueryDTO dto) {
        return inspectionPictureService.getPicInfo(dto);
    }
    @GetMapping("/getAppPicInfo")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "巡检图片根据巡检任务查图片", notes = "传入dto")
    public R picQuery(@RequestParam String inspectionTaskId,@RequestParam String deviceId) {
        return R.data(inspectionPictureService.getAppPicInfo(inspectionTaskId,deviceId));
    }
    /**
     * 新增
     */
    @PostMapping("/saveInspectionPicture")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "巡检图片批量新增", notes = "传入inspectionPictureList")
    public R save(@Valid @RequestBody List<InspectionPictureSaveDTO> inspectionPictureList) {
        return inspectionPictureService.saveInspectionPicture(inspectionPictureList);
    }

    /**
     * 文件夹上传
     */
    @PostMapping("/folderUpload")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "巡检图片文件夹上传", notes = "文件夹上传")
    public R folderUpload(@RequestBody FolderUpLoadDTO dto) {
        return inspectionPictureService.folderUpload(dto);
    }

    /**
     * 查询未关联数据
     */
    @GetMapping("/queryUnassociatedData")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "巡检图片查询未关联数据", notes = "传入tsakId")
    public R queryUnassociatedData(String taskId) {
        return inspectionPictureService.queryUnassociatedData(taskId);
    }

    /**
     * 查询关联数据
     */
    @GetMapping("/queryAssociatedData")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "巡检图片查询关联数据", notes = "传入taskId")
    public R queryAssociatedData(@Valid PatrolDataImageQueryDTO dto) {
        return inspectionPictureService.queryAssociatedData(dto);
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "巡检图片删除", notes = "传入ids")
    public R remove(@Valid @RequestBody IdsDTO dto) {
        return inspectionPictureService.deleteInspectionPicLogic(Func.toStrList(dto.getIds()));
    }

    @PostMapping("/deletedPic")
    @ApiOperation(value = "删除图片")
    public R deletedPic(@Valid @RequestBody FileGuidsDTO dto) {
        return inspectionPictureService.deletedPic(Func.toStrList(dto.getFileGuids()));
    }

    /**
     * 绑定图片到设备
     */
    @PostMapping("/bindPic")
    @ApiOperationSupport(order = 6)
    @ApiOperation("巡检图片绑定图片到设备")
    public R bindPic(@RequestBody BindPicDTO bindPicDTO) {
        return inspectionPictureService.bindPic(bindPicDTO);
    }

    /**
     * 解绑图片
     */
    @PostMapping("/unbindPic")
    @ApiOperationSupport(order = 8)
    @ApiOperation("巡检图片解绑图片到设备")
    public R unbindPic(@RequestBody BindPicDTO bindPicDTO) {
        return inspectionPictureService.unbindPic(bindPicDTO);
    }

    /**
     * 获取巡检影像
     */
    @GetMapping("/images")
    @ApiOperationSupport(order = 7)
    @ApiOperation("巡检图片获取巡检影像")
    public R<List<PatrolDataImageVO>> getPatrolImages(@Valid PatrolDataImageQueryDTO patrolDataImageQueryDTO) {
        return R.data(inspectionPictureService.getPatrolDataImages(patrolDataImageQueryDTO));
    }

    @PostMapping("/newUploadReceivePic")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "远程巡检-无人机拍摄图片批量上传", notes = "")
    public R newUploadReceivePic(@RequestParam("files") MultipartFile[] files,
                                 @RequestParam(value = "paramData") String paramData,
                                 @RequestHeader MultiValueMap<String, String> headers) {
        String hcJcAcsId = headers.get("HC-JC-ACCESSKEYID")==null?headers.get("hc-jc-accesskeyid").get(0):headers.get("HC-JC-ACCESSKEYID").get(0);
        String hcJcAcsSecret = headers.get("HC-JC-ACCESSKEYSECRET")==null?headers.get("hc-jc-accesskeysecret").get(0):headers.get("HC-JC-ACCESSKEYSECRET").get(0);
        ReceivePicNewDTO dto = JSONObject.parseObject(paramData,ReceivePicNewDTO.class);
        return inspectionPictureService.newUploadReceivePic(files,dto,hcJcAcsId,hcJcAcsSecret);
    }

    @PostMapping("/backStatus")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "任务执行状态回传", notes = "任务执行状态回传")
    public R backStatus(@RequestBody BackStatusDTO dto,
                        @RequestHeader MultiValueMap<String, String> headers) {
        String hcJcAcsId = headers.get("HC-JC-ACCESSKEYID")==null?headers.get("hc-jc-accesskeyid").get(0):headers.get("HC-JC-ACCESSKEYID").get(0);
        String hcJcAcsSecret = headers.get("HC-JC-ACCESSKEYSECRET")==null?headers.get("hc-jc-accesskeysecret").get(0):headers.get("HC-JC-ACCESSKEYSECRET").get(0);
        return inspectionPictureService.backStatus(dto,hcJcAcsId,hcJcAcsSecret);
    }

    @GetMapping("/airPortPicProcess")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "回传图片进度", notes = "")
    public R<String> airPortPicProcess(@RequestParam("inspectionTaskNo") String inspectionTaskNo) {
        return R.data(inspectionPictureService.airPortPicProcess(inspectionTaskNo));
    }


    @PostMapping("/uploadTaskPics")
    public R uploadTaskPics(@RequestBody AirportUploadTaskPicDTO dto){
        return R.status(inspectionPictureService.uploadTaskPics(dto));
    }

    @GetMapping("/getImages")
    @ApiOperationSupport(order = 20)
    @ApiOperation(value = "获取该设备航拍影像", notes = "获取该设备航拍影像")
    public R<List<ImagesVO>> getImages(@Valid InspectionImageDTO dto) {
        return inspectionPictureService.selectNewestImages(dto);
    }

}
