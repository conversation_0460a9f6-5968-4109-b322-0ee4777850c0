package com.allcore.main.code.flywatch.service.impl;

import com.allcore.main.code.flywatch.entity.AirspaceDeviceType;
import com.allcore.main.code.flywatch.mapper.AirspaceDeviceTypeMapper;
import com.allcore.main.code.flywatch.service.IAirspaceDeviceTypeService;
import com.allcore.main.code.flywatch.vo.AirspaceDeviceTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class AirspaceDeviceTypeServiceImpl extends ServiceImpl<AirspaceDeviceTypeMapper, AirspaceDeviceType> implements IAirspaceDeviceTypeService {

	@Override
	public IPage<AirspaceDeviceTypeVO> selectAirspaceDeviceTypePage(IPage<AirspaceDeviceTypeVO> page, AirspaceDeviceTypeVO airspaceDeviceType) {
		return page.setRecords(baseMapper.selectAirspaceDeviceTypePage(page, airspaceDeviceType));
	}

}
