package com.allcore.main.code.inspection.controller;

import javax.naming.ldap.Rdn;
import javax.validation.Valid;

import com.allcore.main.code.inspection.dto.InspectionTaggingQueryDTO;
import com.allcore.main.code.inspection.vo.DefectInfoOnlineReportingVO;
import com.allcore.main.code.inspection.vo.InspectionTaggingVO;
import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.InspectionTaskPicQueryDTO;
import com.allcore.main.code.inspection.service.IInspectionPictureTaggingService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/inspectionpicturetagging")
@Api(value = "在线报告", tags = "巡检接口")
public class InspectionPictureTaggingController extends ZxhcController {

    private final IInspectionPictureTaggingService inspectionPictureTaggingService;

    /**
     * 任务级别缺陷信息
     */
    @PostMapping("/taskReport")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "在线报告-任务信息", notes = "传入dto")
    public R taskReport(@Valid @RequestBody InspectionTaskPicQueryDTO dto) {
        return inspectionPictureTaggingService.getTaskReport(dto);
    }

    /**
     * 缺陷详情信息
     */
    @PostMapping("/defectDetailInfo")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "在线报告-缺陷详情信息", notes = "传入dto")
    public R defectDetailInfo(@Valid @RequestBody InspectionTaskPicQueryDTO dto) {
        return inspectionPictureTaggingService.getDefectDetailInfo(dto);
    }

    @PostMapping("/defectDetailInfoByDeviceId")
    @ApiOperation(value = "根据设备id获取缺陷详情信息", notes = "传入dto")
    public R defectDetailInfoByDeviceId(@Valid @RequestBody InspectionTaggingQueryDTO dto) {
        List<InspectionTaggingVO> list = inspectionPictureTaggingService.defectDetailInfoByDeviceId(dto);
        return R.data(list);
    }

    @PostMapping("/pvRemoveDefectDetailInfo")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "在线报告-缺陷详情信息", notes = "传入dto")
    public R pvRemoveDefectDetailInfo(@Valid @RequestBody InspectionTaskPicQueryDTO dto) {
        return inspectionPictureTaggingService.pvRemoveDefectDetailInfo(dto);
    }

    @GetMapping("/station/fanInfo")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "模型地址", notes = "模型地址")
    public R<String> stationFanInfo(@RequestParam String deviceType) {
        return R.data(inspectionPictureTaggingService.stationFanInfo(deviceType));
    }

    /**
     * 风机缺陷前置信息
     */
    @PostMapping("/fanDefectBeforeInfo")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "在线报告-风机前置信息", notes = "传入dto")
    public R fanDefectBeforeInfo(@Valid @RequestBody InspectionTaskPicQueryDTO dto) {
        return inspectionPictureTaggingService.fanDefectBeforeInfo(dto);
    }

}
