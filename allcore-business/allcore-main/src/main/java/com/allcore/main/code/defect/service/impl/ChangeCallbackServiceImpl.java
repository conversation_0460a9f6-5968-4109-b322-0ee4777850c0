package com.allcore.main.code.defect.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.allcore.core.algorithm.invoke.abnormalChange.param.AbnormalChangeResult;
import org.allcore.core.algorithm.invoke.abnormalChange.param.AbnormalProcessResult;
import org.allcore.core.algorithm.service.abnormalChange.IChangeCallbackService;
import org.springframework.stereotype.Service;

/**
 * 实现Call
 *
 * <AUTHOR>
 * @date 2025/03/11 14:41
 **/
@Service
@AllArgsConstructor
@Slf4j
public class ChangeCallbackServiceImpl implements IChangeCallbackService {
    @Override
    public void changeCallback(AbnormalChangeResult abnormalChangeResult) {

    }

    @Override
    public void progressCallback(AbnormalProcessResult abnormalProcessResult) {

    }
}
