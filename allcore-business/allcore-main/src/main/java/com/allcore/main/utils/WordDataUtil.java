package com.allcore.main.utils;


import com.allcore.core.tool.utils.CharPool;
import com.allcore.core.tool.utils.StringPool;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * word数据临时文件
 *
 * <AUTHOR>
 **/
public class WordDataUtil {

	private static final int NUM10 = 10;
	private static final int NUM20 = 20;

	public static String getCurrentYear() {
		Date currentTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		String min;
		min = dateString.substring(0, 4);
		return min;
	}

	public static String dateToZhStr(Date dateDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
		String dateString = formatter.format(dateDate);
		return dateString;
	}

	public static String dateToUpper(Date date) {
		String res = null;
		if (date != null) {
			Calendar ca = Calendar.getInstance();
			ca.setTime(date);
			int year = ca.get(Calendar.YEAR);
			int month = ca.get(Calendar.MONTH) + 1;
			int day = ca.get(Calendar.DAY_OF_MONTH);
			res = numToUpper(year) + "年" + monthToUppder(month) + "月" + dayToUppder(day) + "日";
		}
		return res;
	}

	/**
	 * （将数字转化为大写）
	 *
	 * @param num
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2021/12/16 19:34
	 */
	private static String numToUpper(int num) {
		String[] u = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
		char[] str = String.valueOf(num).toCharArray();
		StringBuilder rstr = new StringBuilder();
		for (int i = 0; i < str.length; i++) {
			rstr.append(u[Integer.parseInt(str[i] + "")]);
		}
		return rstr.toString();
	}

	/**
	 * （月转化为大写）
	 *
	 * @param month
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2021/12/16 19:34
	 */
	private static String monthToUppder(int month) {
		if (month < NUM10) {
			return numToUpper(month);
		} else if (month == NUM10) {
			return "十";
		} else {
			return "十" + numToUpper(month - NUM10);
		}
	}

	/**
	 * （日转化为大写）
	 *
	 * @param day
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2021/12/16 19:34
	 */
	private static String dayToUppder(int day) {
		if (day < NUM20) {
			return monthToUppder(day);
		} else {
			char[] str = String.valueOf(day).toCharArray();
			if (str[1] == CharPool.ZERO) {
				return numToUpper(Integer.parseInt(str[0] + "")) + "十";
			} else {
				return numToUpper(Integer.parseInt(str[0] + "")) + "十"
					+ numToUpper(Integer.parseInt(str[1] + ""));
			}
		}
	}

//	/**
//	 * 获取多个空域记录中最高的真高
//	 *
//	 * @param flyAirinfoList
//	 * @return
//	 */
//	public static Double getTrueHeight(List<AirspaceApply> flyAirinfoList) {
//		Double maxFlyHeight = 0D;
//		for (AirspaceApply tbFlyAirinfo : flyAirinfoList) {
//			Double flyHeight = tbFlyAirinfo.getFlyRealHigh().doubleValue();
//			if (maxFlyHeight < flyHeight) {
//				maxFlyHeight = flyHeight;
//			}
//		}
//		return maxFlyHeight;
//	}

	/**
	 * （根据某一个属性去重）
	 *
	 * @param keyExtractor
	 * @return java.util.function.Predicate<T>
	 * <AUTHOR>
	 * @date 2021/12/16 19:34
	 */
	public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
		Map<Object, Boolean> seen = new ConcurrentHashMap<>(16);
		return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
	}

	public static String ddToDms(Double d) {

		String[] array = d.toString().split("[.]");
		String degrees = array[0];
		//得到度
		if (degrees.length() == 1) {
			degrees = StringPool.ZERO + degrees;
		}

		Double m = Double.parseDouble("0." + array[1]) * 60;
		String[] array1 = m.toString().split("[.]");
		String minutes = array1[0];
		//得到分
		if (minutes.length() == 1) {
			minutes = StringPool.ZERO + minutes;
		}

		Double s = Double.parseDouble(StringPool.ZERO + StringPool.DOT + array1[1]) * 60;
		String[] array2 = s.toString().split("[.]");
		String seconds = array2[0];
		//得到秒
		if (seconds.length() == 1) {
			seconds = StringPool.ZERO + seconds;
		}
		return degrees + "°" + minutes + "′" + seconds + "\" ";
	}
}
