package com.allcore.main.utils;

import com.allcore.main.code.inspection.vo.InspectionPictureVO;

import java.util.Comparator;

/**
 * 排序比较
 *
 * <AUTHOR>
 * @date 2023/12/15 16:03
 **/
public class InspectionPictureComparator implements Comparator<InspectionPictureVO> {
    @Override
    public int compare(InspectionPictureVO vo1, InspectionPictureVO vo2) {
        String name1 = vo1.getShortOriginalName();
        String name2 = vo2.getShortOriginalName();
        return name1.compareTo(name2);
    }
}
