package com.allcore.main.code.inspection.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.*;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.external.dto.AirportTaskDTO;
import com.allcore.external.dto.NestTaskDTO;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.entity.NestTaskDetail;
import com.allcore.external.feign.IAirportTaskClient;
import com.allcore.external.feign.IAlarmClient;
import com.allcore.external.feign.INestClient;
import com.allcore.external.vo.AirportTaskVO;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.defect.entity.RecognitionTask;
import com.allcore.main.code.defect.mapper.RecognitionTaskMapper;
import com.allcore.main.code.defect.service.IRecognitionTaskService;
import com.allcore.main.code.flywatch.dto.RouteDTO;
import com.allcore.main.code.flywatch.mapper.RouteMapper;
import com.allcore.main.code.flywatch.vo.RouteVO;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.entity.*;
import com.allcore.main.code.inspection.mapper.InspectionPictureMapper;
import com.allcore.main.code.inspection.mapper.InspectionPictureTaggingMapper;
import com.allcore.main.code.inspection.mapper.InspectionTaskMapper;
import com.allcore.main.code.inspection.service.*;
import com.allcore.main.code.inspection.vo.*;
import com.allcore.main.code.inspection.wrapper.InspectionTaskWrapper;
import com.allcore.main.code.solve.dto.InspectionBatchSendDTO;
import com.allcore.main.code.solve.dto.RemoveTaskDTO;
import com.allcore.main.code.solve.service.IRemoveTaskService;
import com.allcore.main.code.source.entity.*;
import com.allcore.main.code.source.mapper.*;
import com.allcore.main.code.source.service.IAirportService;
import com.allcore.main.code.station.entity.StationManagement;
import com.allcore.main.code.station.mapper.StationManagementMapper;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.weaver.ast.Var;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.allcore.common.constant.BasicConstant.*;
import static com.allcore.core.tool.utils.DateUtil.PATTERN_DATETIME;
import static com.allcore.main.constant.MainConstant.*;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@AllArgsConstructor
@Slf4j
public class InspectionTaskServiceImpl extends ZxhcServiceImpl<InspectionTaskMapper, InspectionTask>
        implements IInspectionTaskService {
    private final InspectionPictureMapper inspectionPictureMapper;
    private final IInspectionPictureService inspectionPictureService;
    private final IInspectionDeviceTypeService inspectionDeviceTypeService;
    private final IInspectionDeviceDetailService deviceDetailService;
    private final IInspectionDeviceTypeService deviceTypeService;
    private final IInspectionMethodDetailService methodDetailService;
    private final IInspectionUserService userService;
    private final PvComponentsMapper pvComponentsMapper;
    private final PvStringMapper pvStringMapper;
    private final PvAreaMapper pvAreaMapper;
    private final TowerMapper towerMapper;
    private final FanMapper fanMapper;
    private final IOssClient ossClient;
    private final INestClient nestClient;
    private final CommonService commonService;
    private final UavModelMapper uavModelMapper;
    private final IPlanDeviceDetailService planDeviceDetailService;

    private final RouteMapper routeMapper;
    private final IAirportService airportService;

    private final RecognitionTaskMapper recognitionTaskMapper;
    private final IRemoveTaskService removeTaskService;

    private final InspectionPictureTaggingMapper inspectionPictureTaggingMapper;
    private final StationManagementMapper stationManagementMapper;

    private final IPlanService planService;
    private final IAlarmClient alarmClient;
    private final IAirportTaskClient airportTaskClient;
    private final IRecognitionTaskService recognitionTaskService;

    @Override
    public R addReportPic(InspectionTaskReportPicDTO dto) {

        return R.status(deviceDetailService.update(new LambdaUpdateWrapper<InspectionDeviceDetail>()
                .set(InspectionDeviceDetail::getInspectionReportPic, dto.getPicGuid())
                .eq(InspectionDeviceDetail::getDeviceId, dto.getDeviceId())
                .eq(InspectionDeviceDetail::getInspectionTaskId, dto.getInspectionTaskId())));
    }

    @Override
    public R picDeviceList(InspectionTaskReportPicDTO dto) {
        List<InspectionDeviceDetail> entitys = deviceDetailService.list(new LambdaQueryWrapper<InspectionDeviceDetail>()
                .eq(InspectionDeviceDetail::getInspectionTaskId, dto.getInspectionTaskId()));
        if (CollectionUtil.isEmpty(entitys)) {
            return R.data(new InspectionDeviceDetailVO());
        }

        List<InspectionDeviceDetailVO> voList = BeanUtil.copy(entitys, InspectionDeviceDetailVO.class);
        List<String> deviceIds = voList.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());
        String deviceType = dto.getDeviceType();
        if (StringUtil.equals(TMS_LINE, deviceType)) {
            deviceType = TMS_TOWER;
        }
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, deviceType);
        voList.stream().forEach(e -> {
            e.setDeviceName(Func.isEmpty(deviceMap) || Func.isEmpty(deviceMap.get(e.getDeviceId())) ? ""
                    : deviceMap.get(e.getDeviceId()).getDeviceName());
        });
        List<InspectionDeviceDetailVO> sortedList = voList.stream()
                .sorted(Comparator.comparing(InspectionDeviceDetailVO::getDeviceName)).collect(Collectors.toList());
        return R.data(sortedList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R batchSend(List<InspectionBatchSendDTO> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return R.fail("参数为空！");
        }

        // 记录处理结果
        List<String> successTaskIds = new ArrayList<>();
        List<Map<String, Object>> failedTasks = new ArrayList<>();
        //记录气象预警记录
        List<Map<String, Object>> weatherWarningList = new ArrayList<>();


        // 记录成功任务的详细信息（包括与故障工单关联的任务）
        List<Map<String, Object>> successTaskInfos = new ArrayList<>();

        for (InspectionBatchSendDTO batchSendDTO : dto) {

            try {
                InspectionTask entity = new InspectionTask();
                entity.setId(batchSendDTO.getInspectionTaskId());
                entity.setInspectionTaskStatus(batchSendDTO.getInspectionTaskStatus());

                // 校验巡检时间是否合法
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                //解析输入日期
                LocalDate startDate = LocalDate.parse(batchSendDTO.getStartDate(), formatter);
                LocalDate currentDate = LocalDate.now();// 获取当前日期
                // 只有当前巡检结束日期不早于当前日期，才可以进行后续操作
                if (startDate.isBefore(currentDate)) {
                    Map<String, Object> failInfo = new HashMap<>();
                    failInfo.put("inspectionTaskId", batchSendDTO.getInspectionTaskId());
                    failInfo.put("failReason", "巡检结束日期早于当前日期");
                    failedTasks.add(failInfo);
                    continue;
                }
                // 非继续下发状态，且设备类型为风机时，检查机场实时天气预警
                boolean hasWeatherWarning = false;
                if (!FOUR.equals(batchSendDTO.getInspectionTaskStatus()) && BizDictEnum.INSPECTION_TYPE_FAN.getCode().equals(batchSendDTO.getDeviceType())) {



                    if(Func.isEmpty(batchSendDTO.getInspectionDeviceId())){
                        throw new ServiceException("机场id为空");
                    }
                    Airport airport = airportService.getById(batchSendDTO.getInspectionDeviceId());
                    if(Func.isEmpty(airport)){
                        throw new ServiceException("机场不存在");
                    }
                    String siteId=airport.getSiteId();
                    R weatherWarningResult = nestClient.checkNestWeatherWarning(siteId);


                    // 处理气象预警情况
                    if (weatherWarningResult.isSuccess() && weatherWarningResult.getData() != null) {
                        // 获取气象预警数据
                        JSONObject weatherWarningData = JSONObject.parseObject(JSON.toJSONString(weatherWarningResult.getData()));


                        // 获取预警列表和数量
                        JSONArray warnings = weatherWarningData.getJSONArray("warnings");
                        Integer warningCount = weatherWarningData.getInteger("warningCount");

                        // 构建预警提示信息
                        String recommendationMessage = buildWarningMessage(warnings, warningCount);

                        // 记录预警信息，但继续处理其他任务
                        Map<String, Object> warningInfo = new HashMap<>();
                        warningInfo.put("weatherWarning", weatherWarningData);
                        warningInfo.put("taskId", batchSendDTO.getInspectionTaskId());
                        warningInfo.put("recommendationMessage", recommendationMessage);
                        weatherWarningList.add(warningInfo);

                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("weatherWarning", weatherWarningData);
                        jsonObject.put("warnMessage", recommendationMessage);
                        jsonObject.put("batchStatus", ONE);

                        entity.setRemark(jsonObject.toJSONString());
                        entity.setProcessContent(recommendationMessage);
                        entity.setInspectionTaskStatus(ZERO);
                        updateById(entity);

                        // 标记有预警，但不立即返回
                        hasWeatherWarning = true;
                    }
                }

                // 如果有气象预警，跳过当前任务的处理，继续处理下一个任务
                if (hasWeatherWarning) {
                    continue;
                }

                // 记录故障工单关联信息
                if (StringUtil.isNotBlank(batchSendDTO.getRemoveTaskNo())) {
                    // 更新任务的智能处理字段状态为是
                    entity.setSmartProcess(Integer.parseInt(ONE));
                    // 记录与故障工单关联的成功任务
                    Map<String, Object> successInfo = new HashMap<>();
                    successInfo.put("InspectionTaskId", batchSendDTO.getInspectionTaskId());
                    successInfo.put("removeTaskNo", batchSendDTO.getRemoveTaskNo());
                    String content = batchSendDTO.getRemoveTaskNo();
                    successInfo.put("message", content);
                    successTaskInfos.add(successInfo);

                    // 设置智能处理内容
                    entity.setProcessContent(content);
                }

                //将巡检任务状态设置为待数据上传
                entity.setInspectionTaskStatus(ONE);
                updateById(entity);

                //修改巡检计划
                PlanDTO planDTO = new PlanDTO();
                planDTO.setId(entity.getPlanId());
                planDTO.setPlanStatus(PLAN_STATUS_EXECUTION);
                planService.updatePlanStatus(planDTO);
                airportTaskClient.posted(entity.getId(),batchSendDTO.getStartDate()); // 执行飞控平台巡检任务

                // 记录成功下发的任务ID
                successTaskIds.add(batchSendDTO.getInspectionTaskId());
            } catch (Exception e) {
                log.error("巡检任务下发失败，ID: " + batchSendDTO.getInspectionTaskId(), e);
                Map<String, Object> failInfo = new HashMap<>();
                failInfo.put("inspectionTaskId", batchSendDTO.getInspectionTaskId());
                failInfo.put("failedReason", "下发出错: " + e.getMessage());
                failedTasks.add(failInfo);
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successTaskIds.size());
        result.put("failCount", failedTasks.size());
        result.put("failedTasks", failedTasks);
        result.put("successTaskInfos", successTaskInfos);

        // 如果有气象预警信息，优先返回预警
        if (!weatherWarningList.isEmpty()) {
            result.put("weatherWarnings", weatherWarningList);
            return R.data(result, "检测到气象预警，请前往巡检任务界面确认相关任务是否继续下发任务");
        }

        if (failedTasks.isEmpty()) {
            return R.data(result, "所有巡检任务下发成功");
        } else if (successTaskIds.isEmpty()) {
            return R.data(result, "所有巡检任务下发失败");
        } else {
            return R.data(result, "部分巡检任务下发成功，部分失败");
        }


    }


    @Override
    public R cancelSend(String taskId) {

        InspectionTask task = this.getById(taskId);
        if (task == null) {
            return R.fail("任务不存在");
        }
        // 将任务状态设置为待下发
        task.setInspectionTaskStatus(ZERO);
        //获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将当前日期格式化为字符串
        String dateToday = currentDate.format(formatter);
        if (Func.isNotBlank(task.getRemark())) {
            JSONObject processData = JSONObject.parseObject(task.getRemark());
            JSONObject weatherWarning = processData.getJSONObject("weatherWarning");
            if (Func.isNotEmpty(weatherWarning)) {

                JSONArray warnings = weatherWarning.getJSONArray("warnings");
                Integer warningCount = weatherWarning.getInteger("warningCount");

                if (warnings == null || warnings.isEmpty()) {
                    return R.data("任务不存在气象预警信息！");
                }
                StringBuilder sb =new StringBuilder();
                sb.append("天气");
                if(warningCount==1){
                    // 单一预警
                    JSONObject warning = warnings.getJSONObject(0);
                    String warningType = warning.getString("warningType");
                    String warningLevel = warning.getString("warningLevel");
                    sb.append(warningType).append(warningLevel).append("预警");
                }else{
                    // 多重预警
                    for (int i = 0; i < warnings.size(); i++) {
                        JSONObject warning = warnings.getJSONObject(i);
                        String warningType = warning.getString("warningType");
                        String warningLevel = warning.getString("warningLevel");

                        if (i > 0) {
                            sb.append("、");
                        }
                        sb.append(warningType).append(warningLevel).append("预警");
                    }
                }

                // 设置智能处理内容
                task.setProcessContent(sb.toString());
                //更新任务的智能处理字段状态
                task.setSmartProcess(Integer.parseInt(ONE));
                task.setCancelTime(dateToday);
                // 保存更新
                this.updateById(task);
                // 返回成功信息
                return R.success("任务取消下发成功");
            }
        }


        return R.fail("任务不存在气象预警信息！");
    }


    @Override
    public IPage<InspectionTaskVO> selectInspectionTaskPage(IPage<InspectionTaskVO> page,
                                                            InspectionTaskDTO inspectionTask) {
        if (Func.isBlank(inspectionTask.getDeptCode())) {
            inspectionTask.setDeptCode(AuthUtil.getDeptCode());
        }
        List<InspectionTaskVO> voList = baseMapper.selectInspectionTaskPage(page, inspectionTask);
        if (CollectionUtils.isNotEmpty(voList)) {
            // 完善voList
            this.completionVoList(voList);
            // 设置页面展示需要的参数
            this.setProperty(voList, inspectionTask.getDeviceType());
            List<String> taskIds = voList.stream().filter(vo -> vo.getInspectionTaskStatus().equals("1") || vo.getInspectionTaskStatus().equals("2"))
                    .filter(vo -> LocalDateTime.parse(vo.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isBefore(LocalDateTime.now()))
                    .map(InspectionTask::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(taskIds)){
                R<List<AirportTaskVO>> listR = airportTaskClient.unfinishedTaskByTaskIds(taskIds);
                if(listR.isSuccess()){
                    listR.getData().forEach(airportTaskVO -> {
                        voList.stream().filter(vo -> vo.getId().equals(airportTaskVO.getInspectionTaskId()))
                                .findFirst().ifPresent(vo -> vo.setAirportTask(airportTaskVO));
                    });
                }

            }
        }
        return page.setRecords(InspectionTaskWrapper.build().listVoByVo(voList));
    }

    private void completionVoList(List<InspectionTaskVO> voList) {
        // 任务ids
        List<String> taskIds = voList.stream().map(m -> m.getId()).collect(Collectors.toList());

        // 设备详情map
        Map<String, List<InspectionDeviceDetailVO>> deviceDetailListMap = this.getDeviceDetailListMap(taskIds);

        // 负责人详情map
        Map<String, List<InspectionUserVO>> userListMap = getUserListMap(taskIds);

        for (InspectionTaskVO vo : voList) {
            if (deviceDetailListMap.containsKey(vo.getId())) {
                List<InspectionDeviceDetailVO> deviceVo = deviceDetailListMap.get(vo.getId());
                vo.setDeviceDetailList(deviceVo);
            }
            if (userListMap.containsKey(vo.getId())) {
                vo.setInspectionUserList(userListMap.get(vo.getId()));
            }

        }
    }

    private Map<String, List<InspectionUserVO>> getUserListMap(List<String> taskIds) {
        // 负责人列表
        List<InspectionUser> userList =
                userService.list(new LambdaQueryWrapper<InspectionUser>().in(InspectionUser::getInspectionTaskId, taskIds));
        if (Func.isEmpty(userList)) {
            return Maps.newHashMap();
        }
        List<InspectionUserVO> vo = BeanUtil.copy(userList, InspectionUserVO.class);
        vo.stream().forEach(e -> {
            e.setUserName(ObjectUtil.isEmpty(UserCache.getUser(e.getUserId()))?
                    "":UserCache.getUser(e.getUserId()).getRealName());
        });
        Map<String, List<InspectionUserVO>> map =
                vo.stream().collect(Collectors.groupingBy(InspectionUserVO::getInspectionTaskId));
        return Func.isEmpty(map) ? Maps.newHashMap() : map;
    }

    private Map<String, List<InspectionDeviceDetailVO>> getDeviceDetailListMap(List<String> taskIds) {
        // 设备列表
        List<InspectionDeviceDetail> deviceDetailList = deviceDetailService.list(
                new LambdaQueryWrapper<InspectionDeviceDetail>().in(InspectionDeviceDetail::getInspectionTaskId, taskIds));
        if (Func.isEmpty(deviceDetailList)) {
            return Maps.newHashMap();
        }
        List<InspectionDeviceDetailVO> vo = BeanUtil.copy(deviceDetailList, InspectionDeviceDetailVO.class);
        Map<String, List<InspectionDeviceDetailVO>> map =
                vo.stream().collect(Collectors.groupingBy(InspectionDeviceDetailVO::getInspectionTaskId));
        return Func.isEmpty(map) ? Maps.newHashMap() : map;
    }

    @Override
    public IPage<InspectionTaskVO> selectInspectionTaskResultPage(IPage<InspectionTaskVO> page,
                                                                  InspectionTaskDTO inspectionTask) {
        if (Func.isBlank(inspectionTask.getDeptCode())) {
            inspectionTask.setDeptCode(AuthUtil.getDeptCode());
        }
        // 已办结状态--3
        inspectionTask.setInspectionTaskStatus("3");
        List<InspectionTaskVO> voList = baseMapper.selectInspectionResultTaskPage(page, inspectionTask);
        if (CollectionUtils.isNotEmpty(voList)) {
            this.completionTaskResultList(voList);
            this.setPicNum(voList, inspectionTask.getDeviceType());
        }
        return page.setRecords(InspectionTaskWrapper.build().listVoByVo(voList));
    }

    private void completionTaskResultList(List<InspectionTaskVO> voList) {
        List<String> taskIds = voList.stream().map(m -> m.getId()).collect(Collectors.toList());
        // 负责人详情map
        Map<String, List<InspectionUserVO>> userListMap = getUserListMap(taskIds);
        // 巡检图片详情map
        Map<String, List<InspectionPictureVO>> picMap = getPicListMap(taskIds);
        // 巡检设备详情map
        Map<String, List<InspectionDeviceDetailVO>> deviceMap = getDeviceListMap(taskIds);
        for (InspectionTaskVO vo : voList) {
            if (userListMap.containsKey(vo.getId())) {
                vo.setInspectionUserList(userListMap.get(vo.getId()));
            }
            if (picMap.containsKey(vo.getId())) {
                vo.setInspectionPicList(picMap.get(vo.getId()));
            }
            if (deviceMap.containsKey(vo.getId())) {
                vo.setDeviceDetailList(deviceMap.get(vo.getId()));
            }
        }
    }

    private Map<String, List<InspectionDeviceDetailVO>> getDeviceListMap(List<String> taskIds) {
        List<InspectionDeviceDetail> deviceList = deviceDetailService.list(
                new LambdaQueryWrapper<InspectionDeviceDetail>().in(InspectionDeviceDetail::getInspectionTaskId, taskIds));
        if (Func.isEmpty(deviceList)) {
            return Maps.newHashMap();
        }
        List<InspectionDeviceDetailVO> vo = BeanUtil.copy(deviceList, InspectionDeviceDetailVO.class);
        Map<String, List<InspectionDeviceDetailVO>> map =
                vo.stream().collect(Collectors.groupingBy(InspectionDeviceDetailVO::getInspectionTaskId));
        return Func.isEmpty(map) ? Maps.newHashMap() : map;
    }

    private Map<String, List<InspectionPictureVO>> getPicListMap(List<String> taskIds) {
        List<InspectionPicture> picList = inspectionPictureMapper.selectList(
                new LambdaQueryWrapper<InspectionPicture>().in(InspectionPicture::getInspectionTaskId, taskIds));
        if (Func.isEmpty(picList)) {
            return Maps.newHashMap();
        }
        List<InspectionPictureVO> vo = BeanUtil.copy(picList, InspectionPictureVO.class);
        Map<String, List<InspectionPictureVO>> map =
                vo.stream().collect(Collectors.groupingBy(InspectionPictureVO::getInspectionTaskId));
        return Func.isEmpty(map) ? Maps.newHashMap() : map;
    }

    @Override
    public R<InspectionTaskVO> getInspectionResultTaskOne(String taskId, String deviceType) {
        InspectionTask entity = this.getById(taskId);
        if (ObjectUtil.isEmpty(entity)) {
            return R.data(null);
        }
        List<InspectionDeviceDetail> deviceList = deviceDetailService.list(
                new LambdaQueryWrapper<InspectionDeviceDetail>().eq(InspectionDeviceDetail::getInspectionTaskId, taskId));
        List<InspectionUser> userList =
                userService.list(new LambdaQueryWrapper<InspectionUser>().eq(InspectionUser::getInspectionTaskId, taskId));
        InspectionTaskVO vo = BeanUtil.copy(entity, InspectionTaskVO.class);
        Dept dept = SysCache.getDeptByDeptCode(vo.getDeptCode());
        StationManagement stationEntity = stationManagementMapper
                .selectOne(new LambdaQueryWrapper<StationManagement>().eq(StationManagement::getDeptId, dept.getId()));
        vo.setTerrainExaggeration(Func.isEmpty(stationEntity) ? "" : stationEntity.getTerrainExaggeration());
        if (Func.isNotEmpty(userList)) {
            vo.setInspectionUserList(BeanUtil.copy(userList, InspectionUserVO.class));
        }
        if (Func.isNotEmpty(deviceList)) {
            vo.setDeviceDetailList(BeanUtil.copy(deviceList, InspectionDeviceDetailVO.class));
        }
        // 设置相关参数
        this.setFlyFlightAndPicNum(vo);
        // 设置设备信息
        this.setDeviceInfo(vo, deviceType);
        return R.data(InspectionTaskWrapper.build().entityVO(vo));
    }

    private void setDeviceInfo(InspectionTaskVO vo, String deviceType) {
        List<InspectionDeviceDetailVO> deviceList = vo.getDeviceDetailList();
        List<String> deviceIds = deviceList.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());
        // 线路类型只有杆塔才有坐标
        String type = deviceType;
        if (StringUtil.equals(TMS_LINE, type)) {
            type = TMS_TOWER;
        }
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, type);
        if (ObjectUtil.isNotEmpty(deviceMap)) {
            for (InspectionDeviceDetailVO e : deviceList) {
                BasicCommonVO basicCommonVO = deviceMap.get(e.getDeviceId());
                e.setLatitude(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getLatitude() : "");
                e.setDeviceName(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getDeviceName() : "");
                e.setLongitude(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getLongitude() : "");
                e.setCoordinates(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getCoordinates() : "");
                e.setParentDeviceId(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getParentDeviceId() : "");
                e.setHeight(ObjectUtil.isNotEmpty(basicCommonVO) ? basicCommonVO.getHeight() : "");
            }
        }

    }

    @Override
    public String getWorkOrderCode() {
        // 取最新的一条数据
        List<InspectionTask> workOrderInfos = this.list(new LambdaQueryWrapper<InspectionTask>()
                .orderByDesc(InspectionTask::getCreateTime).last(CommonConstant.SQL_LIMIT));
        // 生成 -yyyymmdd-001 格式的按日自增的工单号
        return XJ + getOddNumbers(workOrderInfos);
    }

    @Override
    public R<List<InspectionTaskVO>> getTaskNo(String deptCode) {
        List<InspectionTask> taskInfos = this.list(new LambdaQueryWrapper<InspectionTask>()
                .likeRight(InspectionTask::getDeptCode, deptCode).orderByDesc(InspectionTask::getCreateTime));

        return R.data(BeanUtil.copy(taskInfos, InspectionTaskVO.class));
    }

    private String getOddNumbers(List<InspectionTask> workOrderInfos) {
        // 定义变量(最新的单号)
        String newOddNumber = "";
        // 获取当前日期时分秒 并将其进行格式化
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String formatDate = simpleDateFormat.format(new Date());
        // 生成编号用时分秒，但是校验是否需要自增还是用 日期来判断
        SimpleDateFormat simpleDateFormatDate = new SimpleDateFormat("yyyyMMdd");
        String formatDateCheck = simpleDateFormatDate.format(new Date());
        // 判断数据中的最大单号是否存在，是否包含当前日期
        if (CollectionUtil.isNotEmpty(workOrderInfos) && workOrderInfos.get(0).getInspectionTaskNo() != null
                && workOrderInfos.get(0).getInspectionTaskNo().contains(formatDateCheck)) {
            String maxOddNumbers = workOrderInfos.get(0).getInspectionTaskNo();
            // 截取后3位数
            String endNum = maxOddNumbers.substring(maxOddNumbers.length() - 3);
            // 把截取的最后四位数解析为int
            int endIntNum = Integer.parseInt(endNum);
            // 在将其加1(自增1)
            int newEndIntNum = 1000 + endIntNum + 1;
            // 把1000的1去掉，获取到最新的后四位
            String newEndNum = String.valueOf(newEndIntNum).substring(1);
            // 生成单号
            newOddNumber = StringPool.DASH + formatDate + StringPool.DASH + newEndNum;
            // 将单号返回
            return newOddNumber;
        } else {
            // 如果为空(第一次生成)或者当前最大订单号的日期与当前日期不一致说明需要重新计数生成单号
            newOddNumber = StringPool.DASH + formatDate + StringPool.DASH + "001";
            // 将单号返回
            return newOddNumber;
        }
    }

    @Override
    public R getAllInspectionPic(String taskId, String deviceType, String deviceId) {
        List<InspectionPicture> picList = inspectionPictureMapper
                .selectList(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getInspectionTaskId, taskId)
                        .eq(Objects.nonNull(deviceId), InspectionPicture::getDeviceId, deviceId));
        if (CollectionUtil.isEmpty(picList)) {
            return R.data(new ArrayList<InspectionPictureVO>());
        }
        InspectionTask task = this.getById(taskId);
        List<InspectionPictureVO> vo = BeanUtil.copy(picList, InspectionPictureVO.class);
        List<String> fileGuidAll = vo.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());
        Map<String, AllcoreFileVO> mapAll = getFileGuidObjectMap(fileGuidAll);
        List<String> deviceIdAll = vo.stream().map(InspectionPictureVO::getDeviceId).collect(Collectors.toList());
        Map<String, BasicCommonVO> deviceMap;
        if (StringUtil.equals(TMS_LINE, deviceType)) {
            deviceMap = commonService.getDeviceByDeviceIds(deviceIdAll, TMS_TOWER);
        } else {
            deviceMap = commonService.getDeviceByDeviceIds(deviceIdAll, deviceType);
        }
        vo.forEach(e -> {
            if (StringUtil.equals(deviceType, FAN)) {
                String bigPositionZh =
                        DictBizCache.getValue(BizDictEnum.FAN_BIG_POSITION.getCode(), e.getFanBigPosition().toLowerCase());
                e.setBigPositionZh(StringUtil.isBlank(bigPositionZh) ? "" : bigPositionZh.replace("-机舱", ""));

                if (StringUtil.isNotBlank(e.getFanBladeOrder())) {
                    String bladeOrderZh = DictBizCache.getValue(BizDictEnum.FAN_BLADE_ORDER.getCode(),
                            e.getFanBladeOrder().toUpperCase());
                    e.setBladeOrderZh(StringUtil.isBlank(bladeOrderZh) ? "" : bladeOrderZh);
                } else {
                    e.setBladeOrderZh("");
                }
                if (StringUtil.isNotBlank(e.getFanDetailPosition())) {
                    String detailPositionZh = DictBizCache.getValue(BizDictEnum.FAN_DETAIL_POSITION.getCode(),
                            e.getFanDetailPosition().toUpperCase());
                    e.setDetailPositionZh(StringUtil.isBlank(detailPositionZh) ? "" : detailPositionZh);
                } else {
                    e.setDetailPositionZh("");
                }

            }
            e.setInspectionTaskNo(task.getInspectionTaskNo());
            AllcoreFileVO fileVo = mapAll.get(e.getFileGuid());
            if (Func.isNotEmpty(fileVo)) {
                e.setFilePath(fileVo.getDynamicPath());
                e.setStaticThumbPath(fileVo.getDynamicThumbPath());
                e.setLongitude(StringUtil.equals(fileVo.getLongitude(), "0.0") ? "-" : fileVo.getLongitude());
                e.setLatitude(StringUtil.equals(fileVo.getLatitude(), "0.0") ? "-" : fileVo.getLatitude());
                e.setFileName(fileVo.getOriginalName());
                if (ObjectUtil.isNotEmpty(fileVo.getTime())) {
                    e.setShootTime(DateUtil.format(fileVo.getTime(), PATTERN_DATETIME));
                } else {
                    e.setShootTime("-");
                }
            }
            String deptName = SysCache.getDeptNameByDeptCode(e.getDeptCode());
            if (StringUtil.isNotBlank(deptName)) {
                e.setDeptCodeZh(deptName);
            }
            User use = UserCache.getUser(e.getCreateUser());
            if (ObjectUtil.isNotEmpty(use)) {
                e.setCreateUserZh(use.getRealName());
            }
            if (ObjectUtil.isNotEmpty(deviceMap.get(e.getDeviceId()))) {
                e.setDeviceName(deviceMap.get(e.getDeviceId()).getDeviceName());
            }

        });
        //sortListByDeviceName(vo);
        return R.data(vo);
    }

    public static void sortListByDeviceName(List<InspectionPictureVO> list) {
        Collections.sort(list, new Comparator<InspectionPictureVO>() {
            @Override
            public int compare(InspectionPictureVO o1, InspectionPictureVO o2) {
                return o1.getDeviceName().compareTo(o2.getDeviceName());
            }
        });
    }

    public static void sortVoListByDeviceName(List<InspectionDeviceDetailVO> list) {
        Collections.sort(list, new Comparator<InspectionDeviceDetailVO>() {
            @Override
            public int compare(InspectionDeviceDetailVO o1, InspectionDeviceDetailVO o2) {
                // 如果o1的deviceName为null，认为它应该排在后面
                if (o1.getDeviceName() == null && o2.getDeviceName() == null) {
                    return 0;
                }
                if (o1.getDeviceName() == null) {
                    return 1; // o1排在o2后面
                }
                if (o2.getDeviceName() == null) {
                    return -1; // o1排在o2前面
                }
                return o1.getDeviceName().compareTo(o2.getDeviceName());
            }
        });
    }

    private void setPicNum(List<InspectionTaskVO> voList, String deviceType) {
        // 全量fileGuid
        List<String> fileGuidAll = Lists.newArrayList();
        // 全量deviceId
        List<String> deviceIdAll = Lists.newArrayList();
        voList.stream().forEach(entity -> {

//            entity.setStartToEndTime(CommonUtil.formatDate(entity.getStartDate()) + "--" + CommonUtil.formatDate(entity.getEndDate()));
            entity.setStartToEndTime(entity.getStartDate());
            if (Func.isNotEmpty(entity.getInspectionPicList())) {
                entity.setPicNum(entity.getInspectionPicList().size() + "");
                /*fileGuidAll.addAll(entity.getInspectionPicList().stream().map(InspectionPictureVO::getFileGuid)
                    .collect(Collectors.toList()));*/
            }
            if (Func.isNotEmpty(entity.getDeviceDetailList())) {
                deviceIdAll.addAll(entity.getDeviceDetailList().stream().map(InspectionDeviceDetailVO::getDeviceId)
                        .collect(Collectors.toList()));
            }

        });
        // Map<String, AllcoreFileVO> mapAll = getFileGuidObjectMap(fileGuidAll);
        String queryType = deviceType;
        if (StringUtil.equals(TMS_LINE, deviceType)) {
            queryType = TMS_TOWER;
        }
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIdAll, queryType);
        for (InspectionTaskVO vo : voList) {
            vo.setDeviceNum(String.valueOf(vo.getDeviceDetailList().size()));
            if (Func.isNotEmpty(vo.getDeviceDetailList())) {
                vo.getDeviceDetailList().forEach(e -> {
                    BasicCommonVO basicVo = deviceMap.get(e.getDeviceId());
                    if (ObjectUtil.isNotEmpty(basicVo)) {
                        e.setDeviceName(basicVo.getDeviceName());
                        e.setLongitude(basicVo.getLongitude());
                        e.setLatitude(basicVo.getLatitude());
                        e.setCoordinates(basicVo.getCoordinates());
                    }
                });
                sortVoListByDeviceName(vo.getDeviceDetailList());
            }
            /*if (Func.isNotEmpty(vo.getInspectionPicList())) {
                vo.getInspectionPicList().forEach(e -> {
                    AllcoreFileVO fileVo = mapAll.get(e.getFileGuid());
                    if (Func.isNotEmpty(fileVo)) {
                        e.setFilePath(fileVo.getDynamicPath());
                        e.setStaticThumbPath(fileVo.getDynamicThumbPath());
                        e.setLongitude(StringUtil.equals(fileVo.getLongitude(), "0.0") ? "-" : fileVo.getLongitude());
                        e.setLatitude(StringUtil.equals(fileVo.getLatitude(), "0.0") ? "-" : fileVo.getLatitude());
                        e.setFileName(fileVo.getOriginalName());
                        if (ObjectUtil.isNotEmpty(fileVo.getTime())) {
                            e.setShootTime(DateUtil.format(fileVo.getTime(), PATTERN_DATETIME));
                        } else {
                            e.setShootTime("-");
                        }
                    }
                    String deptName = SysCache.getDeptNameByDeptCode(vo.getDeptCode());
                    if (StringUtil.isNotBlank(deptName)) {
                        e.setDeptCodeZh(deptName);
                    }
                    User use = UserCache.getUser(e.getCreateUser());
                    if (ObjectUtil.isNotEmpty(use)) {
                        e.setCreateUserZh(use.getName());
                    }
                    if (ObjectUtil.isNotEmpty(deviceMap.get(e.getDeviceId()))) {
                        e.setDeviceName(deviceMap.get(e.getDeviceId()).getDeviceName());
                    }
            
                });
            }*/

        }

    }

    private Map<String, AllcoreFileVO> getFileGuidObjectMap(List<String> fileGuidAll) {
        R<List<AllcoreFileVO>> listR = ossClient.getFilesDetail(fileGuidAll);
        if (listR.isSuccess() || CollectionUtil.isNotEmpty(listR.getData())) {
            return listR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
        }
        return Maps.newHashMap();
    }

    private void setFlyFlightAndPicNum(InspectionTaskVO vo) {
        Long picNum = inspectionPictureMapper.selectCount(
                new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getInspectionTaskId, vo.getId()));
        vo.setPicNum(picNum.toString());
        if (StringUtil.equals(vo.getInspectionMethod(), UAV)) {
            // 操作员信息
            List<String> userNameList = Lists.newArrayList();
            if (Func.isNotEmpty(vo.getInspectionUserList())) {
                vo.getInspectionUserList().stream().forEach(e -> {
                    userNameList.add(e.getUserName());
                });
            }
            vo.setUavUsers(String.join(",", userNameList));
        }
//        vo.setStartToEndTime(CommonUtil.formatDate(vo.getStartDate()) + "--" + CommonUtil.formatDate(vo.getEndDate()));
        vo.setStartToEndTime(CommonUtil.formatDate(vo.getStartDate()));

    }

    private void setProperty(List<InspectionTaskVO> voList, String deviceType) {
        List<String> codes = this.getSetCodes(voList);
        Map<String, BasicCommonVO> guidNameMap;
        if (StringUtil.equals(deviceType, TMS_LINE)) {
            guidNameMap = commonService.getDeviceByDeviceIds(codes, TMS_TOWER);
        } else {
            guidNameMap = commonService.getDeviceByDeviceIds(codes, deviceType);
        }

        voList.stream().filter(entity -> CollectionUtils.isNotEmpty(entity.getDeviceDetailList())).forEach(entity -> {
            List<String> deviceNameList = Lists.newArrayList();

            entity.getDeviceDetailList().stream().forEach(e -> {
                BasicCommonVO deviceMap = guidNameMap.get(e.getDeviceId());
                if (Func.isNotEmpty(deviceMap)) {
                    String deviceName = deviceMap.getDeviceName();
                    e.setDeviceName(deviceName);
                    e.setCoordinates(deviceMap.getCoordinates());
                    e.setLongitude(deviceMap.getLongitude());
                    e.setLatitude(deviceMap.getLatitude());
                    if (Func.isNotBlank(deviceName)) {
                        deviceNameList.add(deviceName);
                    }
                }
            });
            sortVoListByDeviceName(entity.getDeviceDetailList());

            if (StringUtil.equals(entity.getInspectionMethod(), UAV)) {
                // 操作员信息
                List<String> userNameList = Lists.newArrayList();
                if (Func.isNotEmpty(entity.getInspectionUserList())) {
                    entity.getInspectionUserList().stream().forEach(e -> {
                        userNameList.add(e.getUserName());
                    });
                }
                entity.setUavUsers(String.join(",", userNameList));
            }

            entity.setDeviceNames(String.join(",", deviceNameList));
            entity.setStartToEndTime(entity.getStartDate());
        });
    }

    private void setProperty4TaskDevice(List<InspectionTaskVO> voList) {

        Map<String, BasicCommonVO> guidNameMap = new HashMap<>();
        voList.stream().filter(entity -> CollectionUtils.isNotEmpty(entity.getDeviceDetailList())).forEach(entity -> {
            List<String> codes = this.getSetCodes4TaskDevice(entity);
            if (StringUtil.equals(entity.getDeviceType(), TMS_LINE)) {
                guidNameMap.putAll(commonService.getDeviceByDeviceIds(codes, TMS_TOWER));
            } else {
                guidNameMap.putAll(commonService.getDeviceByDeviceIds(codes, entity.getDeviceType()));
            }
        });
        if (guidNameMap != null) {
            voList.stream().filter(entity -> CollectionUtils.isNotEmpty(entity.getDeviceDetailList())).forEach(entity -> {
                List<String> deviceNameList = Lists.newArrayList();

                entity.getDeviceDetailList().stream().forEach(e -> {

                    BasicCommonVO deviceMap = guidNameMap.get(e.getDeviceId());
                    if (Func.isNotEmpty(deviceMap)) {
                        String deviceName = deviceMap.getDeviceName();
                        e.setDeviceName(deviceName);
                        e.setCoordinates(deviceMap.getCoordinates());
                        e.setLongitude(deviceMap.getLongitude());
                        e.setLatitude(deviceMap.getLatitude());
                        if (Func.isNotBlank(deviceName)) {
                            deviceNameList.add(deviceName);
                        }
                    }
                });
                sortVoListByDeviceName(entity.getDeviceDetailList());

                entity.setDeviceNames(String.join(",", deviceNameList));
                entity.setStartToEndTime(entity.getStartDate() + "--" + entity.getEndDate());
            });
        }
    }

    private Map<String, String> getGuidNameMap(String deviceType, Set<String> deviceCodes) {
        Map<String, String> deviceMap = Maps.newHashMap();

        if (PV_COMPONENTS.equals(deviceType)) {
            // 光伏组件
            List<PvComponents> pvComponentsList = pvComponentsMapper
                    .selectList(new LambdaQueryWrapper<PvComponents>().in(PvComponents::getId, deviceCodes));
            if (CollectionUtils.isNotEmpty(pvComponentsList)) {
                deviceMap = pvComponentsList.stream()
                        .collect(Collectors.toMap(PvComponents::getId, PvComponents::getDeviceName));
            }
        } else if (PV_STRING.equals(deviceType)) {
            // 光伏组串
            List<PvString> pvStringList =
                    pvStringMapper.selectList(new LambdaQueryWrapper<PvString>().in(PvString::getId, deviceCodes));
            if (CollectionUtils.isNotEmpty(pvStringList)) {
                deviceMap = pvStringList.stream().collect(Collectors.toMap(PvString::getId, PvString::getDeviceName));
            }
        } else if (PV_AREA.equals(deviceType) || PV.equals(deviceType)) {
            // 光伏
            List<PvArea> pvAreaList =
                    pvAreaMapper.selectList(new LambdaQueryWrapper<PvArea>().in(PvArea::getId, deviceCodes));
            if (CollectionUtils.isNotEmpty(pvAreaList)) {
                deviceMap = pvAreaList.stream().collect(Collectors.toMap(PvArea::getId, PvArea::getDeviceName));
            }
        } else if (TMS_TOWER.equals(deviceType) || TMS_LINE.equals(deviceType)) {
            // 杆塔
            List<Tower> towerList =
                    towerMapper.selectList(new LambdaQueryWrapper<Tower>().in(Tower::getId, deviceCodes));
            if (CollectionUtils.isNotEmpty(towerList)) {
                deviceMap = towerList.stream().collect(Collectors.toMap(Tower::getId, Tower::getTowerName));
            }
        } else if (FAN.equals(deviceType)) {
            // 风机
            List<Fan> fanList = fanMapper.selectList(new LambdaQueryWrapper<Fan>().in(Fan::getId, deviceCodes));
            if (CollectionUtils.isNotEmpty(fanList)) {
                deviceMap = fanList.stream().collect(Collectors.toMap(Fan::getId, Fan::getDeviceName));
            }
        }
        return deviceMap;

    }

    private List<String> getSetCodes(List<InspectionTaskVO> entityList) {
        List<String> codes = new ArrayList<>();
        entityList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getDeviceDetailList())).forEach(e -> {
            e.getDeviceDetailList().forEach(i -> {
                codes.add(i.getDeviceId());
            });
        });
        return codes;
    }

    private List<String> getSetCodes4TaskDevice(InspectionTaskVO entity) {
        List<String> codes = new ArrayList<>();
        entity.getDeviceDetailList().forEach(i -> {
            codes.add(i.getDeviceId());
        });
        return codes;
    }

    /**
     * @param entity
     * @return
     */
    private Set<String> getPlanSetCodes(PlanVO entity) {
        Set<String> codes = new HashSet<>();
        if (CollectionUtil.isNotEmpty(entity.getDeviceDetailList())) {
            entity.getDeviceDetailList().forEach(i -> {
                codes.add(i.getDeviceId());
            });
        }
        return codes;
    }

    /**
     * 同级下同名设备
     *
     * @param name
     * @param deptCode
     * @param id
     */
    public void checkRepeat(String name, String deptCode, String id) {
        Optional
                .ofNullable(
                        super.getOne(new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getInspectionTaskName, name)
                                .eq(InspectionTask::getDeptCode, deptCode).ne(Objects.nonNull(id), InspectionTask::getId, id)))
                .ifPresent(f -> {
                    throw new ServiceException(TASK_NAME_NOT_ONLY + REPEAT_PLAN_NAME_NOT_ONLY + f.getInspectionTaskName());
                });
    }

    private void checkInspectionTime(InspectionTask dto) {
        Optional.ofNullable(getOne(new LambdaQueryWrapper<InspectionTask>()
                .eq(InspectionTask::getStartDate, dto.getStartDate())
                .ne(Objects.nonNull(dto.getId()), InspectionTask::getId, dto.getId()))
        ).ifPresent(f -> {
            throw new ServiceException("巡检时间不能重复! " + f.getStartDate());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveInspectionTask(InspectionTaskSaveDTO dto) {
        if (ObjectUtil.isEmpty(dto.getDeviceDetailList())) {
            return R.fail("请添加设备");
        }
        // 页面去掉了巡检类型的选择
        dto.setInspectionType(dto.getDeviceType());

        List<InspectionDeviceDetail> deviceDetailList = processSingleTask(dto);

        return R.status(deviceDetailService.saveBatch(deviceDetailList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveBatchInspectionTask(List<InspectionTaskSaveDTO> inspectionTask) {
        if (CollectionUtil.isEmpty(inspectionTask)) {
            return R.fail("请添加任务");
        }
        List<AlarmInfo> alarmInfoList = new ArrayList<>();
        List<InspectionDeviceDetail> allDeviceDetails = new ArrayList<>();

        for (InspectionTaskSaveDTO dto : inspectionTask) {
            if (ObjectUtil.isEmpty(dto.getDeviceDetailList())) {
                return R.fail("请添加设备");
            }
            //查看是否已经复检,传过来的告警id用逗号隔开
            if (StringUtil.isNotBlank(dto.getAlarmId())) {
                String[] split = dto.getAlarmId().split(",");
                if (split.length > 0) {
                    for (String itemId : split) {
                        R<AlarmInfo> detail = alarmClient.getDetail(itemId);
                        if (R.isSuccess(detail)) {
                            AlarmInfo alarmInfo = detail.getData();
                            if (alarmInfo != null) {
                                if (alarmInfo.getRecheckNum() == 1) {
                                    return R.fail("请勿重复复检");
                                }
                                alarmInfo.setRecheckNum(1);
                                alarmInfoList.add(alarmInfo);
                            }
                        }
                    }
                }
            }
            // 设定不是远程巡检
            dto.setRemoteFlag(false);
            // 页面去掉了巡检类型的选择
            dto.setInspectionType(dto.getDeviceType());
            List<InspectionDeviceDetail> deviceDetails = processSingleTask(dto);
            allDeviceDetails.addAll(deviceDetails);
        }
        alarmClient.updateBatch(alarmInfoList);
        return R.status(deviceDetailService.saveBatch(allDeviceDetails));
    }

    /**
     * 处理单个任务的通用逻辑
     */
    private List<InspectionDeviceDetail> processSingleTask(InspectionTaskSaveDTO dto) {
        InspectionTask taskEntity = BeanUtil.copy(dto, InspectionTask.class);
        // 重复校验
        checkRepeat(taskEntity.getInspectionTaskName(), taskEntity.getDeptCode(), null);
        checkInspectionTime(taskEntity);
         // 处理因结束时间缺失导致的后续流程异常
        if(StringUtil.isBlank(taskEntity.getEndDate())){
            taskEntity.setEndDate(taskEntity.getStartDate());
        }
        // 任务入表
            this.save(taskEntity);

        // 设备类型入表
        InspectionDeviceType typeEntity = getDeviceType(taskEntity, dto);
        deviceTypeService.save(typeEntity);

        // 巡检设备入表 无人机和机场
        List<InspectionMethodDetail> methodEntityList = getMethodEntityList(taskEntity, dto);
        methodDetailService.saveBatch(methodEntityList);

        // 无人机操作员入表（机场不存在操作员）
        if (StringUtil.equals(taskEntity.getInspectionMethod(), UAV)) {
            List<InspectionUser> userList = getUserList(taskEntity, dto);
            userService.saveBatch(userList);
        }

        // 巡检设备入表 光伏、塔线、风机
        List<InspectionDeviceDetail> deviceDetailList = getInspectionDeviceDetailList(taskEntity, typeEntity, dto);

        // 创建机场任务 先暂定创建的时候任务就给到机巢
        // 光伏或者线路的机场巡检
        if (StringUtil.equals(dto.getInspectionMethod(), AIRPORT) && !StringUtil.equals(dto.getDeviceType(), FAN)) {
            //todo 机巢任务待处理
//            asyncNestTask(dto.getDeviceType(), methodEntityList, deviceDetailList, dto.getRemoteFlag(),
//                    dto.getRouteIds());
            createAirportTask(dto.getDeviceType(), methodEntityList, deviceDetailList, dto.getRemoteFlag(),
                    dto.getRouteIds(), taskEntity.getInspectionTaskName());
        }

        return deviceDetailList;
    }

    private void createAirportTask(String deviceType, List<InspectionMethodDetail> methodEntityList,
                                   List<InspectionDeviceDetail> deviceDetailList, Boolean remoteFlag,
                                   List<String> routeIds, String taskName) {
        // 只有一个机场
        InspectionMethodDetail inspectionMethodDetail = methodEntityList.get(0);
        String siteId = Optional.ofNullable(airportService.getOne(new LambdaQueryWrapper<Airport>()
                .eq(Airport::getId, inspectionMethodDetail.getInspectionDeviceId())
                .select(Airport::getSiteId)
        )).orElseThrow(() -> new ServiceException("机场不存在")).getSiteId();
        List<AirportTaskDTO> taskDTOList = deviceDetailList.stream().map(item -> {
            RouteDTO route = new RouteDTO();
            route.setDeviceId(item.getDeviceId());
            route.setDeptCode(AuthUtil.getDeptCode());
            List<RouteVO> routeList = routeMapper.selectRouteList(route);
            if (CollectionUtils.isEmpty(routeList)) {
                throw new ServiceException("航线不存在！");
            }
            RouteVO routeVO = routeList.get(0);
            R<AllcoreFileVO> fileDetail = ossClient.getFileDetail(routeVO.getFileGuid());
            if (!fileDetail.isSuccess()) {
                throw new ServiceException("航线不存在:" + fileDetail.getMsg());
            }
            AirportTaskDTO airportTaskDTO = new AirportTaskDTO();
            airportTaskDTO.setInspectionTaskId(item.getInspectionTaskId());
            airportTaskDTO.setTaskName(taskName);
            airportTaskDTO.setDeviceId(item.getDeviceId());
            airportTaskDTO.setFileGuid(routeVO.getFileGuid());
            airportTaskDTO.setFilePath(fileDetail.getData().getStaticPath());
            airportTaskDTO.setDeviceType(deviceType);
            airportTaskDTO.setSiteId(siteId);
            return airportTaskDTO;
        }).collect(Collectors.toList());
        R r = airportTaskClient.saveBatch(taskDTOList);
        if (!r.isSuccess()) {
            throw new ServiceException("创建机场任务失败！" + r.getMsg());
        }
    }

    @Override
    public R<String> saveInspectionTaskByRemoteTask(RemoteTaskSaveDTO dto) {

        // 查询是否有其他机场 在执行此区域的航线任务
        Integer cnt = baseMapper.checkTrack(dto.getAirportNestId(), dto.getDeviceId());

        if (cnt > 0) {
            throw new ServiceException("此区域有其他机场正在执行航迹");
        }

        String inspectionTaskNo = getWorkOrderCode();
        Date now = new Date();
        // 封装巡检任务新增数据
        InspectionTaskSaveDTO saveDTO = new InspectionTaskSaveDTO();
        saveDTO.setStartDate(DateUtil.formatDateTime(now));
        saveDTO.setEndDate(DateUtil.formatDateTime(DateUtil.plusHours(now, 3)));
        saveDTO.setInspectionTaskStatus(StringPool.ONE);
        saveDTO.setInspectionTaskNo(inspectionTaskNo);
        saveDTO.setDeptCode(AuthUtil.getDeptCode());
        saveDTO.setInspectionTaskName(
                DateUtil.format(now, DateUtil.PATTERN_DATETIME_MINI) + dto.getAirportNestName() + "巡检任务");
        saveDTO.setInspectionMethod(AIRPORT);

        List<InspectionDeviceDetail> deviceDetailList = new ArrayList<>();
        InspectionDeviceDetail detail = new InspectionDeviceDetail();
        detail.setDeviceId(dto.getDeviceId());
        deviceDetailList.add(detail);
        saveDTO.setDeviceDetailList(deviceDetailList);

        List<InspectionMethodDetail> inspectionMethodDetailList = new ArrayList<>();
        InspectionMethodDetail methodDetail = new InspectionMethodDetail();
        methodDetail.setInspectionDeviceId(dto.getAirportNestId());
        inspectionMethodDetailList.add(methodDetail);
        saveDTO.setInspectionMethodDetailList(inspectionMethodDetailList);
        saveDTO.setDeviceType(PV);
        saveDTO.setRemoteFlag(true);
        saveDTO.setRouteIds(dto.getRouteIds());

        R flag = saveInspectionTask(saveDTO);
        if (TWO_ZERO_ZERO == flag.getCode()) {
            return R.data(inspectionTaskNo);
        } else {
            return R.fail("失败");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updatInspectionTaskById(InspectionTaskSaveDTO dto) {
        if (ObjectUtil.isEmpty(dto.getDeviceDetailList())) {
            return R.fail("请添加设备");
        }
        InspectionTask taskEntity = BeanUtil.copy(dto, InspectionTask.class);
        // 修改任务状态为0-待下发
        taskEntity.setInspectionTaskStatus(ZERO);
        // 重复校验
        this.checkRepeat(taskEntity.getInspectionTaskName(), taskEntity.getDeptCode(), taskEntity.getId());
        checkInspectionTime(taskEntity);
        // 任务入表
        this.updateById(taskEntity);
        // 设备类型入表
        InspectionDeviceType typeEntity = this.getDeviceType(taskEntity, dto);
        deviceTypeService.remove(new LambdaQueryWrapper<InspectionDeviceType>()
                .eq(InspectionDeviceType::getInspectionTaskId, taskEntity.getId()));
        deviceTypeService.save(typeEntity);
        // 巡检设备入表
        List<InspectionMethodDetail> methodEntityList = getMethodEntityList(taskEntity, dto);
        methodDetailService.remove(new LambdaQueryWrapper<InspectionMethodDetail>()
                .eq(InspectionMethodDetail::getInspectionTaskId, taskEntity.getId()));
        methodDetailService.saveBatch(methodEntityList);
        // 无人机操作员入表（机场不存在操作员）
        if (StringUtil.equals(taskEntity.getInspectionMethod(), UAV)) {
            List<InspectionUser> userList = getUserList(taskEntity, dto);
            userService.remove(
                    new LambdaQueryWrapper<InspectionUser>().eq(InspectionUser::getInspectionTaskId, taskEntity.getId()));
            userService.saveBatch(userList);
        }
        // 巡检设备入表
        List<InspectionDeviceDetail> deviceDetailList = getInspectionDeviceDetailList(taskEntity, typeEntity, dto);

        // 校验航迹文件和创建飞控任务
        // todo 任务名重复问题待解决
        if(StringUtil.equals(dto.getInspectionMethod(), AIRPORT) && !StringUtil.equals(dto.getDeviceType(), FAN)){
            createAirportTask(dto.getDeviceType(), methodEntityList, deviceDetailList, dto.getRemoteFlag(),
                    dto.getRouteIds(), taskEntity.getInspectionTaskName());
        }
        deviceDetailService.remove(new LambdaQueryWrapper<InspectionDeviceDetail>()
                .eq(InspectionDeviceDetail::getInspectionTaskId, taskEntity.getId()));
        return R.status(deviceDetailService.saveBatch(deviceDetailList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteInspectionTaskLogic(List<String> strList) {
        deviceTypeService.remove(
                new LambdaQueryWrapper<InspectionDeviceType>().in(InspectionDeviceType::getInspectionTaskId, strList));

        methodDetailService.remove(
                new LambdaQueryWrapper<InspectionMethodDetail>().in(InspectionMethodDetail::getInspectionTaskId, strList));

        userService.remove(new LambdaQueryWrapper<InspectionUser>().in(InspectionUser::getInspectionTaskId, strList));

        deviceDetailService.remove(
                new LambdaQueryWrapper<InspectionDeviceDetail>().in(InspectionDeviceDetail::getInspectionTaskId, strList));
        return R.status(this.removeByIds(strList));
    }

    @Override
    public R<InspectionTaskVO> getInspectionTaskOne(String id, String deviceType) {
        List<InspectionTaskVO> voList = baseMapper.selectInspectionTaskOne(id);
        if (CollectionUtils.isEmpty(voList)) {
            return R.data(new InspectionTaskVO());
        }
        this.completionVoList(voList);
        this.setProperty(voList, deviceType);
        InspectionTaskVO vo = voList.get(0);
        // 添加详情方式
        this.addMethod(vo);
        // 巡检区块图地址
        this.addInspectionAreaGuid(vo);

        return R.data(InspectionTaskWrapper.build().entityVO(vo));
    }

    @Override
    public R<List<InspectionDeviceDetailVO>> getTaskDeviceOne(List<ReTaskDTO> reTaskDTOList) {
        List<InspectionTaskVO> voList = new ArrayList<>();
        List<InspectionDeviceDetailVO> deviceDetailList = new ArrayList<>();
        reTaskDTOList.forEach(reTaskDTO -> {
            InspectionTaskVO vo = new InspectionTaskVO();
            vo.setId(reTaskDTO.getTaskId());
            vo.setDeviceType(reTaskDTO.getDeviceType());
            voList.add(vo);
        });

        this.completionVoList(voList);
        this.setProperty4TaskDevice(voList);
        voList.forEach(vo -> {
            deviceDetailList.addAll(vo.getDeviceDetailList());
        });
        return R.data(deviceDetailList);
    }

    private void addInspectionAreaGuid(InspectionTaskVO vo) {
        String fileGuid = vo.getInspectionAreaGuid();
        R<AllcoreFileVO> fileR = ossClient.getFileDetail(fileGuid);
        if (fileR.isSuccess()) {
            vo.setInspectionAreaGuidUrl(fileR.getData().getDynamicPath());
        }
    }

    private void addMethod(InspectionTaskVO vo) {
        List<InspectionMethodDetailVO> list = vo.getInspectionMethodDetailList();
        try {
            list.stream().forEach(e -> {
                if (vo.getInspectionMethod().equals(AIRPORT)) {
                    Airport airport = airportService.getOne(new LambdaQueryWrapper<Airport>().eq(Airport::getId,
                            vo.getInspectionMethodDetailList().get(0).getInspectionDeviceId()));
                    if (airport != null) {
                        String airportTypeZh =
                                DictBizCache.getValue(MainBizEnum.AIRPORT_CATEGORY.getCode(), airport.getAirportCategory());
                        e.setUavType(airport.getAirportCategory());
                        e.setUavTypeZh(StringUtil.isBlank(airportTypeZh) ? "" : airportTypeZh);
                    }
                } else {
                    UavModel modelEntity =
                            uavModelMapper.selectOne(new LambdaQueryWrapper<UavModel>().eq(UavModel::getId, e.getModelId()));
                    e.setModelIdZh(Func.isNotEmpty(modelEntity) ? modelEntity.getModelName() : "");
                    if (StringUtil.isNotBlank(e.getUavType())) {
                        String uavTypeZh = DictBizCache.getValue(MainBizEnum.UAV_TYPE.getCode(), e.getUavType());
                        e.setUavTypeZh(StringUtil.isBlank(uavTypeZh) ? "" : uavTypeZh);
                    } else {
                        e.setUavTypeZh("");
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("addMethod error:{}", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R completionTask(String id, String status) {
        InspectionTask entity = new InspectionTask();
        entity.setId(id);
        entity.setInspectionTaskStatus(status);
        //办结
        if (THREE.equals(status)) {
            // 按照deviceId去重后的缺陷图集
            List<InspectionPicture> picList = inspectionPictureMapper
                    .selectList((new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getInspectionTaskId, id))
                            .groupBy(InspectionPicture::getDeviceId));
            if (Func.isEmpty(picList)) {
                return R.fail(LEAST_ONE_PIC);
            }
            List<String> deviceIds = picList.stream().map(InspectionPicture::getDeviceId).collect(Collectors.toList());
            List<InspectionDeviceDetail> deviceList = deviceDetailService.list(
                    new LambdaQueryWrapper<InspectionDeviceDetail>().eq(InspectionDeviceDetail::getInspectionTaskId, id));
            for (InspectionDeviceDetail e : deviceList) {
                if (!deviceIds.contains(e.getDeviceId())) {
                    return R.fail(LEAST_ONE_PIC);
                }
            }
            // 查询未关联设备的图片
            List<String> pids = inspectionPictureService.list(new LambdaQueryWrapper<InspectionPicture>()
                    .eq(InspectionPicture::getInspectionTaskId, id)
                    .isNull(InspectionPicture::getDeviceId)
                    .select(InspectionPicture::getId)
            ).stream().map(InspectionPicture::getId).collect(Collectors.toList());
            // 删除未关联设备的图片
            if(CollectionUtils.isNotEmpty(pids)){
                inspectionPictureService.remove(new LambdaQueryWrapper<InspectionPicture>().in(InspectionPicture::getId, pids));
            }

            RecognitionTask recognitionTask = recognitionTaskMapper
                    .selectOne(new QueryWrapper<RecognitionTask>().lambda().eq(RecognitionTask::getInspectionTaskId, id));
            if (recognitionTask == null) {
                InspectionTask task = getOne(new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getId, id));
                InspectionDeviceType deviceType = inspectionDeviceTypeService.getOne(new LambdaQueryWrapper<InspectionDeviceType>()
                        .eq(InspectionDeviceType::getInspectionTaskId, id));
                inspectionPictureService.extracted(task.getInspectionTaskNo(), deviceType.getDeviceType(), task.getId());
                recognitionTask = recognitionTaskMapper
                        .selectOne(new QueryWrapper<RecognitionTask>().lambda().eq(RecognitionTask::getInspectionTaskId, id));
                inspectionPictureService.update(new LambdaUpdateWrapper<InspectionPicture>()
                        .set(InspectionPicture::getPicAuditStatus, BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode())
                        .set(InspectionPicture::getPicDefectFlg, BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode())
                        .set(InspectionPicture::getRecognitionTaskId, recognitionTask.getId())
                        .eq(InspectionPicture::getInspectionTaskId, id)
                );
                log.info("------------新增识别任务---------");
                R r = recognitionTaskService.algorithmIdentify(recognitionTask.getId(), id, null, null);
                if(r.isSuccess()){
                    log.info("调用成功，开始识别");
                }else {
                    log.info("算法识别调用失败");
                }
                log.info("------------办结任务执行---------");
            }
            if (recognitionTask.getRecognitionTaskStatus()
                    .equals(BizDictEnum.RECOGNITION_STATUS_IDENTIFIED.getCode())) {
                // 如果识别任务 状态是 识别完成 则办结的时候 改成 任务完成
                recognitionTask.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_COMPLETED.getCode());
                // 查询出图片的所有缺陷 不筛选是否审核
                log.info("=============查询出图片的所有缺陷");

                List<InspectionPictureTagging> taggingIds;
                // 过滤掉实际相同的可见光缺陷框
                // 遍历 如果defect_key 在 light_defect_key中存在 则舍弃
                if (StringUtil.equals(PV, recognitionTask.getDeviceType())) {
                    taggingIds =
                            inspectionPictureTaggingMapper.getPvDefectTaggingIds(recognitionTask.getId(), StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                    // 过滤可见光缺陷
                    this.filterLightDefect(taggingIds);
                    taggingIds = this.filterRepeatDefect(taggingIds);
                } else {
                    taggingIds =
                            inspectionPictureTaggingMapper.getDefectTaggingIds(recognitionTask.getId(), StringPool.EMPTY,
                                    BizDictEnum.PIC_DEFECT_FLG_ALREADY.getCode(), BizDictEnum.DEFECT_TYPE_DEFECT.getCode());
                }
                log.info("=============生成消缺任务");
                RemoveTaskDTO removeTask = new RemoveTaskDTO();
                removeTask.setRemoveTaskName(recognitionTask.getInspectionTaskNo());
                removeTask.setInspectionTaskNo(recognitionTask.getInspectionTaskNo());
                removeTask.setDeviceType(recognitionTask.getDeviceType());
                removeTask.setInspectionTaskId(recognitionTask.getInspectionTaskId());
                removeTask.setInspectionPictureTaggingIds(
                        taggingIds.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList()));
                //根据设备类型设置消缺任务初始状态：光伏设备为待下发，其他设备为执行中
                if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(recognitionTask.getDeviceType())) {
                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
                } else {
                    removeTask.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
                }
                removeTaskService.saveRemoveTask(removeTask);
                recognitionTaskMapper.updateById(recognitionTask);
            }
            //判断当前计划是否所有任务都办结，进度100%将任务改为已完成
            this.updateAllTaskCompleted(id);
            // 设置图片分组
            List<InspectionPicture> pictureList = inspectionPictureService.list(new LambdaQueryWrapper<InspectionPicture>()
                    .eq(InspectionPicture::getInspectionTaskId, id)
            );
            setGroupId(pictureList);
        }
        //下发
        if (ONE.equals(status) || FOUR.equals(status)) {
            //下发的时候，将计划改为执行中
            InspectionTask inspectionTask = baseMapper.selectOne(new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getId, id));
            if (inspectionTask == null) {
                throw new ServiceException("任务不存在");
            }

            String siteId="";

            InspectionMethodDetail one = methodDetailService.getOne(Wrappers.<InspectionMethodDetail>lambdaQuery().eq(InspectionMethodDetail::getInspectionTaskId, id));
            if (one != null) {
                Airport airport = airportService.getById(one.getInspectionDeviceId());
                if (airport != null) {
                    siteId = airport.getSiteId();
                }
            }


            // 下发前进行巡检时间校验
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDate startDate = LocalDate.parse(inspectionTask.getStartDate(), formatter);
            LocalDate currentDate = LocalDate.now();// 获取当前日期
            if (startDate.isBefore(currentDate)) {
                return R.fail("巡检日期早于当前日期，无法下发");
            }

            // 获取设备类型
            InspectionDeviceType deviceType = inspectionDeviceTypeService.getOne(new LambdaQueryWrapper<InspectionDeviceType>()
                    .eq(InspectionDeviceType::getInspectionTaskId, id));

            String type = deviceType.getDeviceType();

            //不是继续下发状态，且设备类型为风机时，检查天气预警
            if (ONE.equals(status) && BizDictEnum.INSPECTION_TYPE_FAN.getCode().equals(type)) {
                // 检查机场天气预警
                // todo 正式版去掉注释
                if(Func.isBlank(siteId)){
                    throw new ServiceException("获取飞控平台场站信息失败！");
                }
                R weatherWarningResult = nestClient.checkNestWeatherWarning(siteId);



               /* // TODO: 测试用天气预警数据，正式环境请删除
                JSONObject testWeatherWarningData = new JSONObject();
                JSONArray testWarnings = new JSONArray();
                JSONObject windWarning = new JSONObject();
                windWarning.put("warningType", "大风");
                windWarning.put("warningLevel", "黄色");
                JSONObject rainWarning = new JSONObject();
                rainWarning.put("warningType", "暴雨");
                rainWarning.put("warningLevel", "蓝色");
                testWarnings.add(windWarning);
                testWarnings.add(rainWarning);
                testWeatherWarningData.put("warnings", testWarnings);
                testWeatherWarningData.put("warningCount", 2);*/

                if (weatherWarningResult.isSuccess() && weatherWarningResult.getData() != null) {
                    //获取气象预警数据
                    JSONObject weatherWarningData = JSONObject.parseObject(JSONObject.toJSONString(weatherWarningResult.getData()));

                    // TODO: 测试时使用测试数据，正式环境请注释掉下面这行
//                    weatherWarningData = testWeatherWarningData;

                    // 获取预警列表和数量
                    JSONArray warnings = weatherWarningData.getJSONArray("warnings");
                    Integer warningCount = weatherWarningData.getInteger("warningCount");

                    // 构建预警提示信息
                    String warnMessage = buildWarningMessage(warnings, warningCount);

                    // 设置remark内容
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("weatherWarning", weatherWarningData);
                    jsonObject.put("warnMessage", warnMessage);
                    jsonObject.put("batchStatus", ZERO);


                    // 保存更新
                    entity.setRemark(jsonObject.toJSONString());
                    entity.setProcessContent(warnMessage);
                    entity.setInspectionTaskStatus(ZERO);
                    updateById(entity);

                    // 有气象预警，返回提示信息
                    return R.data(warnMessage);
                }
            }
            // 将巡检任务状态设置为待数据上传
            entity.setInspectionTaskStatus(ONE);
            entity.setSmartProcess(0);
            entity.setProcessContent(null);
            updateById(entity);


            PlanDTO planDTO = new PlanDTO();
            planDTO.setId(inspectionTask.getPlanId());
            planDTO.setPlanStatus(PLAN_STATUS_EXECUTION);
            planService.updatePlanStatus(planDTO);
            airportTaskClient.posted(entity.getId(), inspectionTask.getStartDate());
        }
        return R.status(this.updateById(entity));
    }

    private void setGroupId(List<InspectionPicture> pictureList) {
        List<String> fileGuidList = pictureList.stream().map(InspectionPicture::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> filesDetail = ossClient.getFilesDetail(fileGuidList);
        if (!filesDetail.isSuccess()) {
            throw new ServiceException("获取文件失败:" + filesDetail.getMsg());
        }
        pictureList.stream()
                .filter(pic -> StringUtil.isBlank(pic.getGroupId()))
                .peek(pic1 -> {
                    String name1 = filesDetail.getData().stream()
                            .filter(file -> pic1.getFileGuid().equals(file.getFileGuid()))
                            .map(AllcoreFileVO::getOriginalName)
                            .findFirst().orElse("");
                    for (InspectionPicture pic2 : pictureList) {
                        String name2 = filesDetail.getData().stream()
                                .filter(file -> pic2.getFileGuid().equals(file.getFileGuid()))
                                .map(AllcoreFileVO::getOriginalName)
                                .findFirst().orElse("");
                        // 不和自己比较
                        if (pic1.getId().equals(pic2.getId())) {
                            continue;
                        }
                        // 根据名称判断是否是同一组
                        if (CommonUtil.isGroup(name1, name2)) {
                            // 判断是否是有组id，没有则加上，有就沿用
                            if (StringUtil.isBlank(pic2.getGroupId())) {
                                pic2.setGroupId(CommonUtil.generateUuid());
                                inspectionPictureService.update(new LambdaUpdateWrapper<>(InspectionPicture.class)
                                        .set(InspectionPicture::getGroupId, pic2.getGroupId())
                                        .eq(InspectionPicture::getId, pic2.getId())
                                );
                            }
                            pic1.setGroupId(pic2.getGroupId());
                            inspectionPictureService.update(new LambdaUpdateWrapper<>(InspectionPicture.class)
                                    .set(InspectionPicture::getGroupId, pic1.getGroupId())
                                    .eq(InspectionPicture::getId, pic1.getId())
                            );
                        }
                    }
                })
                .filter(pic -> StringUtil.isBlank(pic.getGroupId()))
                .forEach(p -> {
                    inspectionPictureService.update(new LambdaUpdateWrapper<>(InspectionPicture.class)
                            .set(InspectionPicture::getGroupId, CommonUtil.generateUuid())
                            .eq(InspectionPicture::getId, p.getId())
                    );
                });
    }

    public void updateAllTaskCompleted(String id) {
        InspectionTask inspectionTask = baseMapper.selectOne(new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getId, id));
        if (inspectionTask == null) {
            throw new ServiceException("任务不存在");
        }
        Plan plan = planService.getById(inspectionTask.getPlanId());
        //如果plan是空，表示是自建任务，不关联计划，不处理
        if (plan != null) {
            PlanVO vo = BeanUtil.copy(plan, PlanVO.class);
            List<PlanDeviceDetail> planDeviceDetail = planDeviceDetailService
                    .list(new LambdaQueryWrapper<PlanDeviceDetail>().eq(PlanDeviceDetail::getPlanId, inspectionTask.getPlanId()));
            vo.setDeviceDetailList(BeanUtil.copy(planDeviceDetail, PlanDeviceDetailVO.class));
            if (this.setPlanVoProperty(vo)) {
                PlanDTO planDTO = new PlanDTO();
                planDTO.setId(inspectionTask.getPlanId());
                planDTO.setPlanStatus(PLAN_STATUS_COMPLETE);
                planService.updatePlanStatus(planDTO);
            }
        }

    }

    private boolean setPlanVoProperty(PlanVO entity) {
        if (entity.getDeviceDetailList() == null) {
            return false;
        }
        AtomicReference<Integer> complate = new AtomicReference<>(0);
        entity.getDeviceDetailList().stream().forEach(e -> {
            if (getPro(entity, e)) {
                complate.getAndSet(complate.get() + 1);
            }
        });
        int total = entity.getDeviceDetailList().size();
        int progress = total == 0 ? 0 : complate.get() * 100 / total;
        planService.updateProcess(entity.getId(), progress + "");
        return progress == 100;

    }

    private boolean getPro(PlanVO entity, PlanDeviceDetailVO e) {
        ProgressQueryDTO dto = ProgressQueryDTO.builder().deviceId(e.getDeviceId())
                .startDate(entity.getStartDate()).endDate(entity.getEndDate()).build();
        List<InspectionTaskVO> voList = baseMapper.selectForProgress(dto);
        return CollectionUtil.isNotEmpty(voList);
    }

    /**
     * （遍历 如果defect_key 在 light_defect_key中存在 则舍弃）
     *
     * @param taggingIds
     * @return void
     * <AUTHOR>
     * @date 2024/01/18 15:58
     */
    private void filterLightDefect(List<InspectionPictureTagging> taggingIds) {
        Iterator<InspectionPictureTagging> iterator = taggingIds.iterator();
        while (iterator.hasNext()) {
            InspectionPictureTagging currentObj = iterator.next();

            for (int i = 0; i < taggingIds.size(); i++) {
                if (!currentObj.equals(taggingIds.get(i))
                        && currentObj.containsFieldInAnotherField(taggingIds.get(i))) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    public List<InspectionPictureTagging> filterRepeatDefect(List<InspectionPictureTagging> entityList) {
        return entityList.stream()
                .collect(
                        Collectors
                                .toMap(
                                        entity -> Arrays.asList(entity.getDefectDescription(), entity.getPvComponentId(),
                                                entity.getPvComponentName()),
                                        entity -> entity, (existingEntity, newEntity) -> existingEntity))
                .values().stream().collect(Collectors.toList());
    }

    private List<InspectionDeviceDetail> getInspectionDeviceDetailList(InspectionTask taskEntity,
                                                                       InspectionDeviceType typeEntity, InspectionTaskSaveDTO dto) {
        Map<String, String> map = this.getParentGuidMap(dto);
        List<InspectionDeviceDetail> list = dto.getDeviceDetailList();
        list.stream().forEach(detail -> {
            detail.setInspectionTaskId(taskEntity.getId());
            detail.setInspectionDeviceTypeId(typeEntity.getId());
            detail.setParentDeviceId(map.get(detail.getDeviceId()));
        });

        return list;
    }

    private Map<String, String> getParentGuidMap(InspectionTaskSaveDTO dto) {
        List<String> idLists =
                dto.getDeviceDetailList().stream().map(InspectionDeviceDetail::getId).collect(Collectors.toList());
        Map<String, String> deviceMap = Maps.newHashMap();

        if (PV_COMPONENTS.equals(dto.getDeviceType())) {
            // 光伏组件
            List<PvComponents> pvComponentsList =
                    pvComponentsMapper.selectList(new LambdaQueryWrapper<PvComponents>().in(PvComponents::getId, idLists));
            if (CollectionUtils.isNotEmpty(pvComponentsList)) {
                deviceMap =
                        pvComponentsList.stream().collect(Collectors.toMap(PvComponents::getId, PvComponents::getId));
            }
        } else if (PV_STRING.equals(dto.getDeviceType())) {
            // 光伏组串
            List<PvString> pvStringList =
                    pvStringMapper.selectList(new LambdaQueryWrapper<PvString>().in(PvString::getId, idLists));
            if (CollectionUtils.isNotEmpty(pvStringList)) {
                deviceMap = pvStringList.stream().collect(Collectors.toMap(PvString::getId, PvString::getId));
            }
        } else if (TMS_TOWER.equals(dto.getDeviceType()) || DMS_TOWER.equals(dto.getDeviceType())) {
            // 杆塔
            List<Tower> towerList = towerMapper.selectList(new LambdaQueryWrapper<Tower>().in(Tower::getId, idLists));
            if (CollectionUtils.isNotEmpty(towerList)) {
                deviceMap = towerList.stream().collect(Collectors.toMap(Tower::getId, Tower::getId));
            }
        }
        return deviceMap;

    }

    private List<InspectionUser> getUserList(InspectionTask taskEntity, InspectionTaskSaveDTO dto) {
        List<InspectionUser> list = dto.getInspectionUserList();
        list.stream().forEach(detail -> detail.setInspectionTaskId(taskEntity.getId()));
        return list;
    }

    private List<InspectionMethodDetail> getMethodEntityList(InspectionTask taskEntity, InspectionTaskSaveDTO dto) {
        List<InspectionMethodDetail> list = dto.getInspectionMethodDetailList();
        list.stream().forEach(detail -> detail.setInspectionTaskId(taskEntity.getId()));
        return list;
    }

    private InspectionDeviceType getDeviceType(InspectionTask taskEntity, InspectionTaskSaveDTO dto) {
        InspectionDeviceType entity = new InspectionDeviceType();
        entity.setDeviceType(dto.getDeviceType());
        entity.setInspectionTaskId(taskEntity.getId());
        return entity;
    }

    private Set<String> getInspectionTaskIds(List<InspectionTaskVO> entityList) {
        return CollectionUtils.isNotEmpty(entityList)
                ? entityList.stream().map(InspectionTaskVO::getId).collect(Collectors.toSet()) : new HashSet<String>();
    }

    @Async
    @Override
    public void asyncNestTask(String deviceType, List<InspectionMethodDetail> methodEntityList,
                              List<InspectionDeviceDetail> deviceDetailList, Boolean remoteFlag, List<String> routeIds) {

        // 机场1 光伏区域1-光伏区域2
        // 光伏区域1 航迹1、航迹2 ====== 光伏区域2 航迹3 、航迹4

        // 总共生成4条机巢task
        // 机场1 航迹1 区域1
        // 机场1 航迹2 区域1
        // 机场1 航迹3 区域2
        // 机场1 航迹4 区域2

        // 一个机场 3个区域
        for (InspectionMethodDetail methodDetail : methodEntityList) {
            // 遍历区域
            for (InspectionDeviceDetail deviceDetail : deviceDetailList) {
                // 根据机场id 巡检的某个光伏 查询巡检的光伏下面所有的航线
                RouteDTO route = new RouteDTO();
                route.setDeviceId(deviceDetail.getDeviceId());
                route.setDeptCode(AuthUtil.getDeptCode());
                List<RouteVO> routeVOList = routeMapper.selectRouteList(route);

                // 如果是远程巡检的 过滤一下指定的航线
                if (remoteFlag) {
                    routeVOList =
                            routeVOList.stream().filter(e -> routeIds.contains(e.getId())).collect(Collectors.toList());
                }
                R<List<AllcoreFileVO>> rstListR = ossClient
                        .getFilesDetail(routeVOList.stream().map(RouteVO::getFileGuid).collect(Collectors.toList()));
                if (rstListR.isSuccess()) {
                    routeVOList.forEach(e -> {
                        rstListR.getData().forEach(r -> {
                            if (e.getFileGuid().equals(r.getFileGuid())) {
                                e.setFileLink(r.getStaticPath());
                            }
                        });
                    });
                }

                List<NestTaskDTO> nestTaskDTOList = Lists.newArrayList();
                List<NestTaskDetail> nestTaskDetailList = Lists.newArrayList();
                // 遍历某个区域的航迹 每个区域的航迹生成一个机巢任务
                for (RouteVO routeVO : routeVOList) {
                    String taskId = CommonUtil.generateUuid();
                    NestTaskDTO nestTaskDTO = new NestTaskDTO();
                    nestTaskDTO.setId(taskId);
                    nestTaskDTO.setInspectionTaskId(methodDetail.getInspectionTaskId());
                    nestTaskDTO.setAirportNestId(methodDetail.getInspectionDeviceId());
                    nestTaskDTO.setFileGuid(routeVO.getFileGuid());
                    // 航迹路径
                    nestTaskDTO.setFilePath(routeVO.getFileLink());
                    // 机场sn等属性
                    Airport airport = airportService.getById(methodDetail.getInspectionDeviceId());
                    nestTaskDTO.setSnCode(airport.getSnCode());
                    nestTaskDTO.setAccessKeyId(airport.getAccessKeyId());
                    nestTaskDTO.setAccessKeySecret(airport.getAccessKeySecret());
                    nestTaskDTO.setServiceAddress(airport.getServiceAddress());
                    nestTaskDTOList.add(nestTaskDTO);

                    NestTaskDetail taskDetail = new NestTaskDetail();
                    taskDetail.setNestTaskId(taskId);
                    taskDetail.setDeviceId(deviceDetail.getDeviceId());
                    taskDetail.setDeviceType(deviceType);
                    nestTaskDetailList.add(taskDetail);
                }
                R nestR = nestClient.saveBatch(nestTaskDTOList);
                if (!nestR.isSuccess()) {
                    throw new ServiceException(nestR.getMsg());
                } else {
                    R nestDetailR = nestClient.saveDetailBatch(nestTaskDetailList);
                    if (!nestDetailR.isSuccess()) {
                        throw new ServiceException(nestDetailR.getMsg());
                    }
                }
            }
        }
    }

    @Override
    public PvArea pvInfoForNest(String airportNestId) {
        PvArea rst = new PvArea();
        // 根据机场id获取当前执行的机巢任务、根据机巢任务获取当前的巡检任务
        R<NestTaskDetail> nestTaskR = nestClient.getNestTaskDetail(airportNestId);
        if (nestTaskR.isSuccess() && Func.isNotEmpty(nestTaskR.getData())) {
            rst = pvAreaMapper.selectById(nestTaskR.getData().getDeviceId());
        }
        return rst;
    }

    @Override
    public List<String> getWorkOrderNoList(String deptCode, String deviceType) {
        List<String> workOrderNos = new ArrayList<>();
        List<InspectionTask> list = this.list(new QueryWrapper<InspectionTask>().lambda()
                .eq(StrUtil.isNotBlank(deptCode), InspectionTask::getInspectionType, deviceType)
                .likeRight(InspectionTask::getDeptCode, StrUtil.isBlank(deptCode) ? AuthUtil.getDeptCode() : deptCode));
        if (CollectionUtil.isNotEmpty(list)) {
            workOrderNos = list.stream().map(x -> x.getInspectionTaskNo()).collect(Collectors.toList());
        }
        return workOrderNos;
    }

    @Override
    public InspectionTaskForNestVO taskForNest(String airportNestId, String deviceType) {
        InspectionTaskForNestVO rst = new InspectionTaskForNestVO();

        String inspectionTaskId = baseMapper.getLastTask(airportNestId, deviceType);
        if (StringUtil.isNotBlank(inspectionTaskId)) {
            List<InspectionTaskVO> voList = baseMapper.selectInspectionTaskOne(inspectionTaskId);

            InspectionTaskVO task = voList.get(0);
            rst.setInspectionTaskNo(task.getInspectionTaskNo());
            rst.setDeptName(SysCache.getDeptNameByDeptCode(task.getDeptCode()));
            rst.setInspectionTypeZh(
                    DictBizCache.getValue(BizDictEnum.INSPECTION_TYPE.getCode(), task.getInspectionType()));
            rst.setStartDate(task.getStartDate());
            rst.setEndDate(task.getEndDate());
            rst.setOtherWorkers(task.getOtherWorkers());

            if (CollectionUtils.isNotEmpty(voList)) {
                this.completionVoList(voList);
                this.setProperty(voList, task.getDeviceType());
            }
            rst.setInspectionUsers(voList.get(0).getUavUsers());
            rst.setDeviceName(voList.get(0).getDeviceNames());
        }
        return rst;
    }

    /**
     * 构建天气预警提示信息
     *
     * @param warnings 预警列表
     * @param warningCount 预警数量
     * @return 预警提示信息
     */
    private String buildWarningMessage(JSONArray warnings, Integer warningCount) {
        if (warnings == null || warnings.isEmpty()) {
            return "";
        }

        StringBuilder message = new StringBuilder();

        message.append("天气");

        if (warningCount == 1) {
            // 单一预警
            JSONObject warning = warnings.getJSONObject(0);
            String warningType = warning.getString("warningType");
            String warningLevel = warning.getString("warningLevel");
            message.append(warningType).append(warningLevel).append("预警");
        } else {
            // 多重预警
            for (int i = 0; i < warnings.size(); i++) {
                JSONObject warning = warnings.getJSONObject(i);
                String warningType = warning.getString("warningType");
                String warningLevel = warning.getString("warningLevel");

                if (i > 0) {
                    message.append("、");
                }
                message.append(warningType).append(warningLevel).append("预警");
            }
        }
        return message.toString();
    }


    @Override
    public R countInspectionTask() {

        Map<String,Long>countMap;

        // 获取当前登录单位
        String deptCode = AuthUtil.getDeptCode();
        if(Func.isBlank(deptCode)){
            deptCode = "001";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDate currentDate = LocalDate.now();// 获取当前日期

        List<InspectionTask> inspectionTasks = baseMapper.selectList(Wrappers.<InspectionTask>lambdaQuery().like(InspectionTask::getDeptCode, deptCode)
                .eq(InspectionTask::getInspectionTaskStatus, ONE)
                .eq(InspectionTask::getInspectionMethod, AIRPORT));
       countMap = inspectionTasks.stream().filter(inspectionTask -> {
            LocalDate startDate = LocalDate.parse(inspectionTask.getStartDate(), formatter);
           return currentDate.isAfter(startDate);
       }).collect(Collectors.groupingBy(InspectionTask::getInspectionType, Collectors.counting()));

        return R.data(countMap);
    }

    @Override
    public IPage<InspectionTaskVO> selectInspectionTaskPageIndoing(IPage<InspectionTaskVO> page, InspectionTaskDTO inspectionTask) {

        if (Func.isBlank(inspectionTask.getDeptCode())) {
            inspectionTask.setDeptCode(AuthUtil.getDeptCode());
        }
        List<InspectionTaskVO> inspectionTaskVOS = baseMapper.selectInspectionTaskPage(page, inspectionTask);
        if (CollectionUtils.isNotEmpty(inspectionTaskVOS)) {
            this.completionVoList(inspectionTaskVOS);
            this.setProperty(inspectionTaskVOS, inspectionTask.getDeviceType());
            // 查找机场正在执行中的任务
            inspectionTaskVOS= inspectionTaskVOS.stream().filter(vo -> vo.getInspectionTaskStatus().equals("1"))
                    .filter(vo -> LocalDateTime.parse(vo.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isBefore(LocalDateTime.now()))
                    .collect(Collectors.toList());
        }


           return page.setRecords(InspectionTaskWrapper.build().listVoByVo(inspectionTaskVOS));
    }


}
