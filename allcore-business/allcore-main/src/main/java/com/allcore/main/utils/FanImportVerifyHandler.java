package com.allcore.main.utils;

import static com.allcore.main.constant.MainConstant.NINE;

import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.main.code.source.entity.Fan;
import com.allcore.main.code.source.excel.FanExcel;
import com.allcore.main.code.source.mapper.FanMapper;
import com.allcore.system.entity.Region;
import com.allcore.system.feign.ISysClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 风机模版导入校验
 * </p>
 *
 * @author: sunkun Date: 2023/8/21
 */
@Component
@Slf4j
public class FanImportVerifyHandler implements IExcelVerifyHandler<FanExcel> {

    @Autowired
    private ISysClient sysClient;

    @Autowired
    private FanMapper fanMapper;
    private List<Region> regionList;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(FanExcel fanExcel) {
        StringJoiner joiner = new StringJoiner(",");
        fanExcel.setDeptCode(AuthUtil.getDeptCode());
        checkRepeat(fanExcel, joiner);
        if (joiner.length() != 0) {
            return new ExcelVerifyHandlerResult(false, joiner.toString());
        }
        return new ExcelVerifyHandlerResult(true);
    }

    private void checkRepeat(FanExcel fan, StringJoiner joiner) {
        Optional.ofNullable(fanMapper.selectOne(new LambdaQueryWrapper<Fan>().eq(Fan::getFanNum, fan.getFanNum())
            .eq(Fan::getDeptCode, fan.getDeptCode()).eq(Fan::getIsDeleted, 0))).ifPresent(f -> {
                joiner.add("风机编号重复:" + fan.getFanNum());
            });
    }
}
