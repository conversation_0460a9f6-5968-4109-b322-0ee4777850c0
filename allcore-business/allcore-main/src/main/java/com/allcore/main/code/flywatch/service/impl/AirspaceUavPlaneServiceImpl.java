package com.allcore.main.code.flywatch.service.impl;

import com.allcore.main.code.flywatch.entity.AirspaceUavPlane;
import com.allcore.main.code.flywatch.mapper.AirspaceUavPlaneMapper;
import com.allcore.main.code.flywatch.service.IAirspaceUavPlaneService;
import com.allcore.main.code.flywatch.vo.AirspaceUavPlaneVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class AirspaceUavPlaneServiceImpl extends ServiceImpl<AirspaceUavPlaneMapper, AirspaceUavPlane> implements IAirspaceUavPlaneService {

	@Override
	public IPage<AirspaceUavPlaneVO> selectAirspaceUavPlanePage(IPage<AirspaceUavPlaneVO> page, AirspaceUavPlaneVO airspaceUavPlane) {
		return page.setRecords(baseMapper.selectAirspaceUavPlanePage(page, airspaceUavPlane));
	}

}
