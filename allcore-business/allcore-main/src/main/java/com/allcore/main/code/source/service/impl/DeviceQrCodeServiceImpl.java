package com.allcore.main.code.source.service.impl;



import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.external.dto.RasDataByObjDTO;
import com.allcore.external.dto.RasDataFilterDTO;
import com.allcore.external.feign.IRasDataClient;
import com.allcore.external.vo.RasDataVO;
import com.allcore.filesystem.feign.IOssClient;

import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.mapper.InspectionTaskMapper;
import com.allcore.main.code.inspection.service.IInspectionDeviceDetailService;
import com.allcore.main.code.source.dto.QrCodeDeviceDTO;
import com.allcore.main.code.source.dto.QrCodeDownloadDTO;
import com.allcore.main.code.source.dto.QrCodeQueryDTO;
import com.allcore.main.code.source.entity.DeviceQrCode;
import com.allcore.main.code.source.entity.Tower;
import com.allcore.main.code.source.mapper.DeviceQrCodeMapper;
import com.allcore.main.code.source.mapper.TowerMapper;
import com.allcore.main.code.source.service.IDeviceQrCodeService;

import com.allcore.main.code.source.vo.QrCodeDeviceVO;
import com.allcore.main.code.source.vo.QrCodeGenerateResultVO;
import com.allcore.main.code.source.vo.QrCodeManagementVO;
import com.allcore.main.constant.MainConstant;
import com.allcore.main.constant.QrcodeProperties;
import com.allcore.main.utils.AlarmPropsUtil;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.servlet.http.HttpServletResponse;

import java.net.URLEncoder;
import java.util.*;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @program: bl
 * @description: 二维码设备管理服务实现类
 * @author: fanxiang
 * @create: 2025-06-19 10:54
 **/

@Service
@Slf4j
@AllArgsConstructor
public class DeviceQrCodeServiceImpl extends ZxhcServiceImpl<DeviceQrCodeMapper, DeviceQrCode> implements IDeviceQrCodeService {

    private final IOssClient ossClient;
    private final IInspectionDeviceDetailService deviceDetailService;
    private final InspectionTaskMapper inspectionTaskMapper;
    private final TowerMapper towerMapper;
    private final QrcodeProperties qrcodeProperties;
    private final IRasDataClient rasDataClient;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);


    @Override
    public IPage<QrCodeManagementVO>selectQrCodePage(IPage<QrCodeManagementVO> page, QrCodeQueryDTO dto) {

        if(Func.isBlank(dto.getDeptCode())){
            dto.setDeptCode(AuthUtil.getDeptCode());
        }

        Set<String>lineSets=new HashSet<>();
        lineSets.add("TMS_LINE");
        lineSets.add("CCL_LINE");
        List<QrCodeManagementVO> list = baseMapper.selectQrCodePage(page, dto);
        if(Func.isEmpty(list)){
            return page.setRecords(new ArrayList<>());
        }
        list.forEach(device -> {
            device.setDeviceTypeZh(DictBizCache.getValue(BizDictEnum.QRCODE_DEVICE_TYPE.getCode(), device.getDeviceType()));
            if(Func.isBlank(device.getQrCodeUrl())){
                //创建二维码链接
                device.setQrCodeUrl(
                        generateQrCodeURL(
                        device.getDeviceId(),
                        device.getDeviceType()
                ));
            }
            if(Func.isNotBlank(device.getQrCodeFileGuid())){
                device.setQrCodeFilePath(ossClient.getFileDetail(device.getQrCodeFileGuid()).getData().getDynamicPath());
                device.setQrCodeGenerated(MainConstant.INTEGER_NUM_ONE);
            }else{
                device.setQrCodeGenerated(MainConstant.INTEGER_NUM_ZERO);
            }
            if(lineSets.contains(device.getDeviceType())){
                device.setDeviceNo(device.getDeviceName());
            }
            //光伏设备型号映射
            if("PV".equals(device.getDeviceType())){
                device.setDeviceModel(DictBizCache.getValue(BizDictEnum.DEVICE_TYPE_PV_COMPONENT_MODEL.getCode(),device.getDeviceModel()));
            }
            device.setQrCodeFileName(buildStandardFileName(device));
        });

        return page.setRecords(list);
    }


    @Override
    public void downloadSingleQrCode(QrCodeDownloadDTO dto,HttpServletResponse response) {

        try{

            // 1.查询设备信息
            QrCodeManagementVO deviceInfo=baseMapper.selectByDeviceIdsWithType(dto.getDeviceType(), Func.toStrList(dto.getDeviceIds())).get(0);
            if(Func.isNull(deviceInfo)){
                throw new ServiceException("未找到设备信息");
            }

            // 3.检查文件guid是否存在
            if(Func.isBlank(deviceInfo.getQrCodeFileGuid())){
                throw new ServiceException("未找到二维码文件");
            }
            // 4.构建下载文件名
            String standardFileName=buildStandardFileName(deviceInfo)+".png";

            // 5.设置响应头
            response.reset();
            response.setContentType("image/png");
            response.setHeader("Content-Disposition","attachment;filename="+
                    URLEncoder.encode(standardFileName,"UTF-8"));

            R<byte[]> fileByte = ossClient.getFileByte(deviceInfo.getQrCodeFileGuid());
            if(!fileByte.isSuccess()||fileByte.getData()==null){
                throw new ServiceException("获取文件失败:"+fileByte.getMsg());
            }
            response.setContentLength(fileByte.getData().length);
            response.getOutputStream().write(fileByte.getData());
            response.getOutputStream().flush();
            log.info("单个二维码下载成功，设备ID:{}, 文件guid：{}", deviceInfo.getDeviceId(), deviceInfo.getQrCodeFileGuid());
        }catch (Exception e){
            log.error("单个二维码下载失败",e);
            throw new ServiceException("下载二维码失败：" + e.getMessage());
        }
    }



    @Override
    public void batchDownloadQrCode(QrCodeDownloadDTO dto, HttpServletResponse response) {

        try {

            // 1.参数验证
            if (Func.isEmpty(dto) || Func.isAnyBlank(dto.getDeviceIds(), dto.getDeviceType())) {
                throw new ServiceException("参数为空");
            }

            List<String> strList = Func.toStrList(dto.getDeviceIds());

            // 如果只有一个设备，直接调用单个下载方法
            if(strList.size()==1){
                downloadSingleQrCode(dto,response);
                return;
            }

            // 2.查询设备信息
            List<QrCodeManagementVO> deviceList = baseMapper.selectByDeviceIdsWithType(dto.getDeviceType(), Func.toStrList(dto.getDeviceIds()));

            if (Func.isEmpty(deviceList)) {
                throw new ServiceException("未找到所选择设备信息");
            }



            // 3.过滤已生成的二维码设备
            List<QrCodeManagementVO> generatedList = deviceList.stream()
                    .filter(device -> Func.isNotBlank(device.getQrCodeFileGuid()))
                    .collect(Collectors.toList());
            if (Func.isEmpty(generatedList)) {
                throw new ServiceException("未找到已生成二维码的设备");
            }

            // 4.获取文件guid列表
            List<String> fileGuids = new ArrayList<>();
            for (QrCodeManagementVO deviceInfo : generatedList) {
                if (Func.isNotBlank(deviceInfo.getQrCodeFileGuid())) {
                    fileGuids.add(deviceInfo.getQrCodeFileGuid());
                }
            }
            if (Func.isEmpty(fileGuids)) {
                throw new ServiceException("未找到二维码文件");
            }


            // 5.构建ZIP文件名
            String zipFileName = "二维码_" + DateUtil.format(new Date(), DateUtil.PATTERN_DATETIME_MINI) + ".zip";

            // 6.设置响应头
            response.reset(); // 清除之前可能设置的响应头
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            log.info("响应头设置完成");

            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                log.info("准备处理{}个文件", fileGuids.size());
                int successCount = 0;
                for (QrCodeManagementVO deviceInfo : generatedList) {
                    try {
                        R<byte[]> byteResult = ossClient.getFileByte(deviceInfo.getQrCodeFileGuid());
                        if (!byteResult.isSuccess() || byteResult.getData() == null) {
                            log.warn("获取文件失败，fileGuid：{}，错误：{}", deviceInfo.getQrCodeFileGuid(), byteResult.getMsg());
                            continue; // 跳过这个文件，继续处理其他文件
                        }
                        String fileName = buildStandardFileName(deviceInfo) + ".png";
                        ZipEntry zipEntry = new ZipEntry(fileName);
                        zipOut.putNextEntry(zipEntry);
                        zipOut.write(byteResult.getData());
                        zipOut.closeEntry();
                        log.info("成功添加文件到ZIP：{}，文件大小：{} bytes", fileName, byteResult.getData().length);
                    } catch (Exception e) {
                        log.error("处理文件失败，fileGuid：{}", deviceInfo.getQrCodeFileGuid(), e);
                        // 继续处理其他文件，不中断整个流程
                    }
                }
                log.info("完成文件添加，成功：{}个，开始finish ZIP", successCount);
                zipOut.finish();
                log.info("ZIP finish完成");
            }
        }catch (Exception e) {
            log.error("批量下载失败", e);
            throw new ServiceException("批量下载失败：" + e.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveQrCode(List<QrCodeDeviceDTO> dto) {
        List<DeviceQrCode>saveList=new ArrayList<>();
        for (QrCodeDeviceDTO qrCodeDeviceDTO : dto) {
            DeviceQrCode entity = BeanUtil.copy(qrCodeDeviceDTO, DeviceQrCode.class);
            saveList.add(entity);
        }
        return R.status(this.saveOrUpdateBatch(saveList));
    }



    private String buildStandardFileName(QrCodeManagementVO deviceInfo) {
        StringBuilder fileName=new StringBuilder();
        //设备类型
        if(Func.isNotBlank(deviceInfo.getDeviceType())){
            fileName.append(DictBizCache.getValue(BizDictEnum.QRCODE_DEVICE_TYPE.getCode(), deviceInfo.getDeviceType()));
        }else{
            fileName.append("未知类型");
        }

        fileName.append("-");

        //设备型号
        // 判断是否是输电和集电，如果是，则不添加设备型号
        if(Func.isNotBlank(deviceInfo.getDeviceModel()) && !deviceInfo.getDeviceModel().equals("-")){
            fileName.append(deviceInfo.getDeviceModel());
            fileName.append("-");
        }


        //设备名称
        if(Func.isNotBlank(deviceInfo.getDeviceName())){
            fileName.append(deviceInfo.getDeviceName());
        }else{
            fileName.append("未知名称");
        }

        fileName.append("-");

        // 设备ID(编号)
        fileName.append(deviceInfo.getDeviceNo());

        // 替换文件名中的非法字符
        return fileName.toString()
                .replaceAll("[\\\\/:*?\"<>|]", "_")  // 替换Windows文件名非法字符
                .replaceAll("\\s+", "_");

    }

    /**
     * 清理OSS中的旧文件
     */
    public void cleanupOldFile(List<String> fileGuids) {
        if (Func.isEmpty(fileGuids)) {
            return;
        }
        try {
            // 异步清理文件，避免影响主流程
            CompletableFuture.runAsync(() -> {
                try {
                    R<?> result = ossClient.removeLogicalFile(fileGuids);
                    if (result.isSuccess()) {
                        log.info("成功清理二维码文件，fileGuid：{}", fileGuids);
                    } else {
                        log.warn("清理二维码文件失败，fileGuid：{}，错误：{}", fileGuids, result.getMsg());
                    }
                } catch (Exception e) {
                    log.error("清理二维码文件异常，fileGuid：{}", fileGuids, e);
                }
            }, executorService);
        } catch (Exception e) {
            log.error("提交文件清理任务失败，fileGuid：{}", fileGuids, e);
        }
    }

    @Override
    public R<QrCodeGenerateResultVO> generateQrCode(QrCodeDownloadDTO dto) {
       /* try {
            // 1. 参数验证
            if (Func.isEmpty(dto) || Func.isAnyBlank(dto.getDeviceIds(), dto.getDeviceType())) {
                throw new ServiceException("参数为空");
            }

            // 2. 查询设备信息
            List<QrCodeManagementVO> deviceList = baseMapper.selectByDeviceIdsWithType(dto.getDeviceType(), Func.toStrList(dto.getDeviceIds()));
            if (Func.isEmpty(deviceList)) {
                throw new ServiceException("未找到所选择设备信息");
            }
            // 3. 过滤已生成的二维码设备
            deviceList = deviceList.stream()
                    .filter(device -> Func.isBlank(device.getQrCodeFileGuid()))
                    .collect(Collectors.toList());
            if (Func.isEmpty(deviceList)) {
                throw new ServiceException("所选设备均已生成二维码");
            }

            // 4. 生成二维码链接
            deviceList.forEach(device -> {
                device.setQrCodeUrl(QrCodeUtil.generateQrCodeURL(
                        device.getDeviceId(),
                        device.getDeviceType()
                ));
            });

            // 5. 生成二维码图片
        }*/
        return null;
    }

    @Override
    public R<QrCodeDeviceVO> getDeviceInfo(String deviceId, String deviceType) {

        try{
            log.info("智慧app获取设备信息，设备id:{},设备类型：{}",deviceId,deviceType);

            // 1. 参数验证
            if(Func.isAnyBlank(deviceId,deviceType)){
                return R.fail("设备ID和设备类型不能为空");
            }
            // 2.获取设备信息
          QrCodeDeviceVO.DeviceBasicInfoVO basicInfoVO = baseMapper.selectBasicInfoByDeviceId(deviceId, deviceType);
            if(Func.isNull(basicInfoVO)){
                return R.fail("未找到设备信息");
            }
            if(Func.isBlank(basicInfoVO.getQrCodeFileGuid())){
                basicInfoVO.setQrCodeFilePath("");
            }else{
                basicInfoVO.setQrCodeFilePath(qrcodeProperties.getHost()+"/"+ossClient.getFileDetail(basicInfoVO.getQrCodeFileGuid()).getData().getDynamicPath());
            }
            // 对设备名称的空值处理
            if(Func.isBlank(basicInfoVO.getDeviceName())){
                basicInfoVO.setDeviceName("");
            }

            basicInfoVO.setDeviceTypeZh(DictBizCache.getValue(BizDictEnum.QRCODE_DEVICE_TYPE.getCode(), basicInfoVO.getDeviceType()));

            // 对设备型号的空值处理
            if(Func.isBlank(basicInfoVO.getDeviceModel())){
                basicInfoVO.setDeviceModel("");
            }
            // 光伏设备型号映射
            if("PV".equals(basicInfoVO.getDeviceType())){
                basicInfoVO.setDeviceModel(DictBizCache.getValue(BizDictEnum.DEVICE_TYPE_PV_COMPONENT_MODEL.getCode(),basicInfoVO.getDeviceModel()));
            }
        QrCodeDeviceVO deviceInfo = new QrCodeDeviceVO();
        deviceInfo.setDeviceBasicInfo(basicInfoVO);

         //todo 从告警表中获取告警数据
            // mock几组告警数据
        List<QrCodeDeviceVO.DeviceAlarmVO> alarmList = new ArrayList<>();

        if("PV".equals(deviceType)){

            // 提取设备名称的数字
            String numStr=numByName(basicInfoVO.getDeviceName());
            // 构建name属性
            String name="东站-"+numStr+"#光伏区";

            // 构建请求参数
            RasDataByObjDTO dto = new RasDataByObjDTO();
            dto.setObjId("7033");
            List<String> pvFields = AlarmPropsUtil.fields("pvAlarmDict");
            dto.setAttrs(pvFields);
            List<RasDataFilterDTO> filters = new ArrayList<>();
            RasDataFilterDTO filter = new RasDataFilterDTO();
            filter.setAttr("name");
            filter.setComparator("=");
            filter.setValue(Arrays.asList(name));
            filters.add(filter);
            dto.setFilters(filters);
            RasDataVO rasDataVO = rasDataClient.dataByObj(dto);
            if (!rasDataVO.getSuccessful()) {
                throw new ServiceException("查询光伏告警数据失败: " + rasDataVO.getResultHint());
            }
            JSONArray data ;
            try {
                data = JSONUtil.parseArray(rasDataVO.getResultData());
            } catch (Exception e) {
                throw new ServiceException("解析光伏数据失败: " + e.getMessage());
            }
            if (Func.isNotEmpty(data)) {
                JSONObject jsonObject=data.getJSONObject(0);
                for (String pvField : pvFields) {
                    if(Func.isNotBlank(jsonObject.getStr(pvField))){
                        String alarmValue=jsonObject.getStr(pvField);
                        if(Func.isNotBlank(alarmValue) && !"1".equals(alarmValue)){
                            QrCodeDeviceVO.DeviceAlarmVO alarmVO = new QrCodeDeviceVO.DeviceAlarmVO();
                            alarmVO.setDeviceName(basicInfoVO.getDeviceName());
                            alarmVO.setDeviceType(basicInfoVO.getDeviceType());
                            alarmVO.setAlarmContent();
                        }
                    }
                }
            }


        }

        QrCodeDeviceVO.DeviceAlarmVO alarmVO = new QrCodeDeviceVO.DeviceAlarmVO();
        alarmVO.setAlarmId("1");
        alarmVO.setDeviceType(deviceType);
        alarmVO.setDeviceName(basicInfoVO.getDeviceName());
        alarmVO.setAlarmContent("设备告警");
        alarmVO.setAlarmLevel("一般告警");
        alarmVO.setAlarmStatus("未处理");
        alarmVO.setAlarmSource("南瑞");
        alarmVO.setAlarmType("设备告警");
        alarmVO.setAlarmTime(new Date());
        alarmList.add(alarmVO);
        deviceInfo.setAlarmList(alarmList);

        // 3. 获取巡检信息
            // 只有设备类型是风机机组、光伏子阵、输电线路时，有巡检信息
            Set<String> deviceTypeSet=new HashSet<>();
            deviceTypeSet.add("PV"); // 光伏
            deviceTypeSet.add("FAN"); // 风机
            deviceTypeSet.add("TMS_LINE"); // 输电线路
            if(deviceTypeSet.contains(deviceType) && !"TMS_LINE".equals(deviceType)){
                List<InspectionDeviceDetail> deviceDetailList = deviceDetailService.list(Wrappers.<InspectionDeviceDetail>lambdaQuery()
                        .eq(InspectionDeviceDetail::getDeviceId, deviceId));
                if(Func.isNotEmpty(deviceDetailList)){
                    List<String> inspectionTaskIds = deviceDetailList.stream().map(InspectionDeviceDetail::getInspectionTaskId).collect(Collectors.toList());

                    List<InspectionTask> inspectionTaskList = inspectionTaskMapper.selectList(Wrappers.<InspectionTask>lambdaQuery()
                            .in(InspectionTask::getId, inspectionTaskIds));
                    if(Func.isNotEmpty(inspectionTaskList)){
                        deviceInfo.setInspectionList(inspectionTaskList.stream().map(inspectionTask -> {
                            QrCodeDeviceVO.DeviceInspectionVO inspectionVO = new QrCodeDeviceVO.DeviceInspectionVO();
                            inspectionVO.setInspectionTaskNo(inspectionTask.getInspectionTaskNo());
                            inspectionVO.setDeviceName(basicInfoVO.getDeviceName());
                            inspectionVO.setStartDate(inspectionTask.getStartDate());
                            User user = UserCache.getUser(inspectionTask.getResponsibleUser());
                           inspectionVO.setResponsibleUser(user==null?"":user.getRealName());
                            return inspectionVO;
                        }).collect(Collectors.toList()));
                    }
            }
            }else if("TMS_LINE".equals(deviceType)){
                //找到该线路下的所有杆塔
                List<Tower> towerList = towerMapper.selectList(Wrappers.<Tower>lambdaQuery()
                        .eq(Tower::getLineId, deviceId));

                // 杆塔名称映射表
                Map<String, String> towerNameMap = towerList.stream()
                        .collect(Collectors.toMap(Tower::getLineId, Tower::getTowerName));

                if(Func.isNotEmpty(towerList)){
                    List<String> towerIds = towerList.stream().map(Tower::getId).collect(Collectors.toList());
                    //查询巡检任务
                    Map<String, List<InspectionDeviceDetail>> listMap = deviceDetailService.list(Wrappers.<InspectionDeviceDetail>lambdaQuery()
                                    .in(InspectionDeviceDetail::getDeviceId, towerIds))
                            .stream()
                            .collect(Collectors.groupingBy(InspectionDeviceDetail::getInspectionTaskId));

                    List<QrCodeDeviceVO.DeviceInspectionVO> inspectionList = new ArrayList<>();
                    listMap.forEach((inspectionTaskId, deviceDetailList) -> {
                        InspectionTask inspectionTask = inspectionTaskMapper.selectOne(Wrappers.<InspectionTask>lambdaQuery()
                                .eq(InspectionTask::getId, inspectionTaskId));
                        if (Func.isNull(inspectionTask)) {
                            return;
                        }
                        StringBuilder sb = new StringBuilder();
                        for (InspectionDeviceDetail inspectionDeviceDetail : deviceDetailList) {
                            towerNameMap.get(inspectionDeviceDetail.getDeviceId());
                            sb.append(towerNameMap.get(inspectionDeviceDetail.getDeviceId())).append(",");
                        }
                        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == ',') {
                            sb.deleteCharAt(sb.length() - 1);
                        }
                        QrCodeDeviceVO.DeviceInspectionVO inspectionVO = new QrCodeDeviceVO.DeviceInspectionVO();
                        inspectionVO.setInspectionTaskNo(inspectionTask.getInspectionTaskNo());
                        inspectionVO.setDeviceName(sb.toString());
                        inspectionVO.setStartDate(inspectionTask.getStartDate());
                        User user = UserCache.getUser(inspectionTask.getResponsibleUser());
                        inspectionVO.setResponsibleUser(user == null ? "" : user.getRealName());
                        inspectionList.add(inspectionVO);
                    });
                    deviceInfo.setInspectionList(inspectionList);
                }
            }
        return R.data(deviceInfo);
        }catch (Exception e){
            log.error("智慧app获取设备信息失败，设备ID：{}，设备类型：{}", deviceId, deviceType, e);
            return R.fail("获取设备信息失败：" + e.getMessage());
        }
    }

    private String numByName(String deviceName) {
        if(Func.isBlank(deviceName)){
            return "";
        }
        Pattern pattern=Pattern.compile("XBX-0*(\\\\d+)");
        Matcher matcher=pattern.matcher(deviceName);
        if(matcher.find()){
            return matcher.group(1);
        }
        return "";
    }


    private String generateQrCodeURL(String deviceId,String deviceType){
        return qrcodeProperties.getHost()+qrcodeProperties.getInfoPath()+
                "?deviceId="+deviceId+"&deviceType="+deviceType;
    }

}
