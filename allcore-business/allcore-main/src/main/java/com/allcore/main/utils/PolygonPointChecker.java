package com.allcore.main.utils;

import java.util.List;

public class PolygonPointChecker {
    private static final double EPSILON = 1e-6; // 浮点数计算精度阈值

    /**
     * 判断点是否在多边形集合内
     * @param polygons 多边形集合 (List<多边形>)
     *               每个多边形: List<点> (每个点是List<Float>[2] [x, y])
     * @param px 待测点x坐标
     * @param py 待测点y坐标
     * @return true-点在边界内(包括边界上), false-点在边界外
     */
    public static boolean isPointInPolygons(List<List<List<Float>>> polygons, float px, float py) {
        // 遍历所有多边形
        for (List<List<Float>> polygon : polygons) {
            if (isPointInPolygon(polygon, px, py)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 使用射线法判断点是否在单个多边形内
     * @param polygon 多边形点列表，每个点是List<Float>包含两个元素[x, y]
     */
    private static boolean isPointInPolygon(List<List<Float>> polygon, float px, float py) {
        int pointCount = polygon.size();
        if (pointCount < 3) return false; // 至少需要3个点构成多边形

        boolean inside = false;

        // 遍历多边形的每条边 (i-当前点, j-前一个点)
        for (int i = 0, j = pointCount - 1; i < pointCount; j = i, i++) {
            // 获取点i和点j
            List<Float> pointI = polygon.get(i);
            List<Float> pointJ = polygon.get(j);

            float xi = pointI.get(0);
            float yi = pointI.get(1);
            float xj = pointJ.get(0);
            float yj = pointJ.get(1);

            // 1. 检查点是否在顶点上
            if (isEqualPoint(xi, yi, px, py) || isEqualPoint(xj, yj, px, py)) {
                return true;
            }

            // 2. 检查点是否在边上（不包括顶点）
            if (isPointOnLineSegment(xi, yi, xj, yj, px, py)) {
                return true;
            }

            // 3. 射线法核心判断 (从点向右发水平射线)
            // 检查边是否跨越射线 (一个端点在射线上方，一个在下方)
            if ((yi > py) != (yj > py)) {
                // 计算射线与边的交点x坐标
                double intersectX = (xj - xi) * (py - yi) / (yj - yi) + xi;

                // 如果交点在点右侧 (考虑浮点精度)
                if (px < intersectX + EPSILON) {
                    inside = !inside;
                }
            }
        }
        return inside;
    }

    /**
     * 判断点是否在线段上（包括端点）
     */
    private static boolean isPointOnLineSegment(float x1, float y1, float x2, float y2, float px, float py) {
        // 1. 先检查点是否在端点
        if (isEqualPoint(x1, y1, px, py) || isEqualPoint(x2, y2, px, py)) {
            return true;
        }

        // 2. 检查点是否在线段延长线上
        double crossProduct = (py - y1) * (x2 - x1) - (px - x1) * (y2 - y1);
        if (Math.abs(crossProduct) > EPSILON) {
            return false; // 不共线
        }

        // 3. 检查点是否在线段范围内
        return (px >= Math.min(x1, x2) - EPSILON &&
                px <= Math.max(x1, x2) + EPSILON &&
                py >= Math.min(y1, y2) - EPSILON &&
                py <= Math.max(y1, y2) + EPSILON);
    }

    /**
     * 判断两点是否相同（考虑浮点精度）
     */
    private static boolean isEqualPoint(float x1, float y1, float x2, float y2) {
        return Math.abs(x1 - x2) < EPSILON && Math.abs(y1 - y2) < EPSILON;
    }
}