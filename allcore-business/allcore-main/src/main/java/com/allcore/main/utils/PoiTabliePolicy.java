package com.allcore.main.utils;

import com.allcore.main.code.inspection.entity.InspectionPicture;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.policy.RenderPolicy;
import com.deepoove.poi.template.ElementTemplate;
import com.deepoove.poi.template.run.RunTemplate;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.List;

/**
 * 导出报告附加
 * <AUTHOR>
 */
public class PoiTabliePolicy implements RenderPolicy {

    @Override
    public void render(ElementTemplate eleTemplate, Object data, XWPFTemplate template) {
        RunTemplate runTemplate = (RunTemplate)eleTemplate;

        XWPFRun run = ((RunTemplate) eleTemplate).getRun();
        run.setText("",0);
        XWPFTableCell tagCell = (XWPFTableCell)((XWPFParagraph)run.getParent()).getBody();

        XWPFTable table = tagCell.getTableRow().getTable();

		List<InspectionPictureVO> inspectionPictures = (List<InspectionPictureVO>) data;
		if (inspectionPictures==null||inspectionPictures.size()==0) {
			return;
		}
        for (int i = 0; i < inspectionPictures.size(); i++) {

            XWPFTableRow row=table.createRow();
            XWPFTableCell cell=row.getCell(0);
            XWPFParagraph paragraph = cell.getParagraphArray(0);
            if(i!=0){
                paragraph.setPageBreak(true);
            }
            XWPFRun r1= paragraph.createRun();
            r1.setFontFamily("宋体");
            r1.setFontSize(12);
			//加粗
            r1.setBold(true);
            r1.setText((i+1)+"、"+inspectionPictures.get(i).getOriginalName());
            r1.addBreak();

            XWPFParagraph paragraph1= cell.addParagraph();
            XWPFRun r2= paragraph1.createRun();
            r2.setFontFamily("宋体");
            r2.setFontSize(12);
			//加粗
            r2.setBold(false);
			List<InspectionPictureTaggingVO> taggingList=inspectionPictures.get(i).getInspectionPictureTaggings();
			for (int i1 = 0; i1 < taggingList.size(); i1++) {
				r2.setText((i1+1)+"、"+taggingList.get(i1).getDefectName() + taggingList.get(i1).getExtendDesc());
				r2.addBreak();
			}
            XWPFParagraph paragraph2= cell.addParagraph();
            paragraph2.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun r3= paragraph2.createRun();
            try {
                String path=inspectionPictures.get(i).getFileGuid();
                FileInputStream fi=new FileInputStream(new File(path));
                r3.addPicture(fi,XWPFDocument.PICTURE_TYPE_JPEG,inspectionPictures.get(i).getOriginalName(),Units.toEMU(442),Units.toEMU(276));
                fi.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            r3.addBreak();
            r3.setFontFamily("宋体");
            r3.setFontSize(12);
            //加粗
            r3.setBold(false);
            r3.setText("缺陷放大图");


            XWPFParagraph paragraph3= cell.addParagraph();
            XWPFRun r4= paragraph3.createRun();
            r4.setFontFamily("宋体");
            r4.setFontSize(12);
            try {
                for (int i1 = 0; i1 < taggingList.size(); i1++) {
                    String path=taggingList.get(i1).getFilePath();
                    String pathName=taggingList.get(i1).getDefectName();
                    URL url=new URL(path);
                    InputStream inputStream = url.openStream();
                    r4.setText((i1+1)+"、");
                    r4.addPicture(inputStream,XWPFDocument.PICTURE_TYPE_JPEG,pathName,Units.toEMU(113),Units.toEMU(113));
                    r4.setText("    ");
                    inputStream.close();
                    if ((i1+1)%3==0) {
                        r4.addBreak();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //r4.addBreak();

        }
    }


}
