package com.allcore.main.code.source.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.main.code.source.dto.BoosterUpNameDTO;
import com.allcore.main.code.source.entity.BoosterRegion;
import com.allcore.main.code.source.mapper.BoosterRegionMapper;
import com.allcore.main.code.source.service.IBoosterRegionService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
public class BoosterRegionServiceImpl extends ZxhcServiceImpl<BoosterRegionMapper, BoosterRegion> implements IBoosterRegionService {

    @Override
    public boolean updateName(BoosterUpNameDTO dto) {
        // 判断是否有其他记录使用了该名称
        boolean exists = baseMapper.exists(Wrappers.<BoosterRegion>lambdaQuery()
                .eq(BoosterRegion::getRegionName, dto.getBoosterUpName())
                // 排除当前要修改的记录
                .ne(BoosterRegion::getId, dto.getId()));
        if (exists) {
            throw new ServiceException("名称已存在，不能重复");
        }
        // 更新
        BoosterRegion boosterRegion = new BoosterRegion();
        boosterRegion.setId(dto.getId());
        boosterRegion.setRegionName(dto.getBoosterUpName());
        return baseMapper.updateById(boosterRegion) > 0;
    }

    @Override
    public boolean saveBoosterRegion(BoosterRegion boosterRegion) {
        // 判断是否有其他记录使用了该名称
        boolean exists = baseMapper.exists(Wrappers.<BoosterRegion>lambdaQuery()
                .eq(BoosterRegion::getRegionName, boosterRegion.getRegionName()));
        if (exists) {
            throw new ServiceException("名称已存在，不能重复");
        }
        return this.save(boosterRegion);
    }
}
