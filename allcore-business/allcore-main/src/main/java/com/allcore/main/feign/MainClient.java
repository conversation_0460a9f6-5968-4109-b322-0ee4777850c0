package com.allcore.main.feign;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.allcore.app.code.flightsorties.vo.*;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.dict.entity.DictBiz;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.defect.dto.DefectTaggingInfoCollectDTO;
import com.allcore.main.code.fences.service.IFencesService;
import com.allcore.main.code.fences.vo.FenceAlarmVO;
import com.allcore.main.code.flywatch.service.IRouteService;
import com.allcore.main.code.flywatch.vo.PvTrackVO;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.feign.MainForAppClient;
import com.allcore.main.code.inspection.service.IInspectionDeviceDetailService;
import com.allcore.main.code.inspection.service.IInspectionPictureService;
import com.allcore.main.code.inspection.service.IInspectionPictureTaggingService;
import com.allcore.main.code.inspection.service.IInspectionTaskService;
import com.allcore.main.code.inspection.vo.AppRouteResponseVO;
import com.allcore.main.code.inspection.vo.InspectionTaskOnlineReportingVO;
import com.allcore.main.code.inspection.vo.InspectionTaskVO;
import com.allcore.main.code.source.dto.AllDeviceInfoForDefectDTO;
import com.allcore.main.code.source.dto.DeviceForMainNameDTO;
import com.allcore.main.code.source.dto.QrCodeQueryDTO;
import com.allcore.main.code.source.feign.IMainSourceClient;
import com.allcore.main.code.source.service.IDeviceForMainService;
import com.allcore.main.code.source.service.IDeviceQrCodeService;
import com.allcore.main.code.source.service.IFanService;
import com.allcore.main.code.source.vo.*;
import com.allcore.main.code.station.service.IStationManagementService;
import com.allcore.system.cache.SysCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@Slf4j
public class MainClient implements MainForAppClient, IMainSourceClient {


    @Resource
    private IInspectionTaskService taskService;

    @Resource
    private IInspectionDeviceDetailService inspectionDeviceDetailService;
    @Resource
    private IInspectionPictureTaggingService inspectionPictureTaggingService;

    @Resource
    private IInspectionPictureService inspectionPictureService;

    @Resource
    private IUserClient userClient;

    @Resource
    private IDeviceForMainService deviceForMainService;

    @Resource
    private IRouteService routeService;

    @Resource
    private CommonService commonService;

    @Resource
    private IDeviceQrCodeService deviceQrCodeService;

    @Resource
    private IFanService fanService;

    @Resource
    private IFencesService fencesService;
    @Resource
    private IStationManagementService stationManagementService;

    /**
     * 人员进入围栏是否告警校验
     */
    @Override
    public R<FenceAlarmVO> fenceAlarmVerification(String userId, String longitude, String latitude) {
        return fencesService.fenceAlarmVerification(userId, longitude, latitude);
    }

    /**
     * 获取工单信息
     *
     * @param inspectionTaskId
     * @return
     */
    @Override
    public R<InspectionTask> getOrderByGuid(String inspectionTaskId) {
        return R.data(taskService.getById(inspectionTaskId));
    }

    @Override
    public R fanFolderUpload(FolderUpLoadDTO dto) {
        R r = inspectionPictureService.folderUpload(dto);

        if (r.getCode() == ResultCode.SUCCESS.getCode() && r.getData() != null) {
            return R.data(inspectionPictureService.folderUpload(dto).getData());
        }
        return R.data(ResultCode.FAILURE.getCode(), "", "保存风机巡检图片信息失败");
    }

    @Override
    public R<AppPageVO<AppInspectionTaskVO>> getAppInspectionTaskVO(Map<String, Object> map) {
        try {
            Query query = new Query();
            query.setCurrent((Integer) map.get("pageNo"));
            query.setSize((Integer) map.get("pageSize"));
            // 拼参数
            InspectionTaskDTO dto = new InspectionTaskDTO();
            dto.setInspectionTaskStatus((String) map.get("inspectionTaskStatus"));
            dto.setInspectionTaskName((String) map.get("inspectionTaskName"));
            dto.setDeviceType((String) map.get("deviceType"));
            dto.setDeptCode((String) map.get("deptCode"));
            dto.setInspectionMethod("AIRPORT");
            //分页查询
            log.info("查询工单分页入参:{}", JSON.toJSONString(dto));
            IPage<InspectionTaskVO> pages = taskService.selectInspectionTaskPage(Condition.getPage(query), dto);
            AppPageVO<AppInspectionTaskVO> pageVo = new AppPageVO<>();
            pageVo.setTotal(pages.getTotal());
            pageVo.setCurrent(pages.getCurrent());
            //处理返回数据
            List<AppInspectionTaskVO> workOrder = convertInspectionTaskData(pages);
            pageVo.setData(workOrder);
            return R.data(pageVo);
        } catch (Exception e) {
            log.error("Failed to get inspection tasks", e);
            return R.fail("Failed to retrieve inspection tasks");
        }
    }

    @Override
    public R<AppInspectionTaskOnlineReportingVO> queryAppTaskReport(Map<String, Object> map) {
        // 拼参数
        InspectionTaskPicQueryDTO dto = new InspectionTaskPicQueryDTO();
        dto.setInspectionTaskId((String) map.get("inspectionTaskId"));
        dto.setDeviceId((String) map.get("deviceId"));
        dto.setDeviceType((String) map.get("deviceType"));
        log.info("查询任务统计入参:{}", JSON.toJSONString(dto));
        // 调接口
        R<InspectionTaskOnlineReportingVO> r = inspectionPictureTaggingService.getTaskReport(dto);
        //处理返回数据
        AppInspectionTaskOnlineReportingVO data = convertAppTaskReport(r);

        return R.data(data);
    }

    @Override
    public R<List<AppPvTrackVO>> appGetRoute(Map<String, Object> map) {
        // 1. 参数校验
        if (map == null || !map.containsKey("inspectionTaskId") || !map.containsKey("deviceId")) {
            return R.fail("参数缺失：需要inspectionTaskId和deviceId");
        }
        String inspectionTaskId = (String) map.get("inspectionTaskId");
        String deviceId = (String) map.get("deviceId");
        log.info("查询航线入参 inspectionTaskId:{};deviceId:{}", inspectionTaskId, deviceId);
        try {
            // 2. 调用服务层
            List<PvTrackVO> sourceVo = routeService.deviceNestTrack(inspectionTaskId, deviceId);

            // 3. 对象转换（修正BeanUtil的错误用法）
            List<AppPvTrackVO> targetVo = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(sourceVo)) {
                targetVo = sourceVo.stream()
                        .map(vo -> {
                            AppPvTrackVO appVo = new AppPvTrackVO();
                            BeanUtil.copyProperties(vo, appVo);
                            return appVo;
                        })
                        .collect(Collectors.toList());
            }

            return R.data(targetVo);
        } catch (Exception e) {
            log.error("查询航线异常", e);
            return R.fail("查询航线失败：" + e.getMessage());
        }
    }

    @Override
    public R<List<List<String>>> appGetPicByDevice(Map<String, Object> map) {
        String inspectionTaskId = (String) map.get("inspectionTaskId");
        String deviceId = (String) map.get("deviceId");
        List<List<String>> data = inspectionPictureService.getAppPicInfo(inspectionTaskId, deviceId);
        return R.data(data);
    }

    @Override
    public R<AppStatisticVO> getPvStatistics(Map<String, Object> map) {
        return stationManagementService.getPvStatistics(map);
    }


    private AppInspectionTaskOnlineReportingVO convertAppTaskReport(R<InspectionTaskOnlineReportingVO> r) {
        AppInspectionTaskOnlineReportingVO targetVo = new AppInspectionTaskOnlineReportingVO();
        InspectionTaskOnlineReportingVO sourceVo = r.getData();

        if (ObjectUtil.isNotEmpty(sourceVo)) {
            // 1. 复制基本属性
            BeanUtil.copyProperties(sourceVo, targetVo);

            // 2. 手动转换 defectInfo（如果有）
            if (CollectionUtil.isNotEmpty(sourceVo.getDefectInfo())) {
                List<AppDefectTypeNameNumVO> detailList = sourceVo.getDefectInfo().stream()
                        .map(sourceDetail -> {
                            AppDefectTypeNameNumVO targetDetail = new AppDefectTypeNameNumVO();
                            BeanUtil.copyProperties(sourceDetail, targetDetail);
                            return targetDetail;
                        })
                        .collect(Collectors.toList());
                targetVo.setDefectInfo(detailList);
            }

        }
        return targetVo;
    }

    private List<AppInspectionTaskVO> convertInspectionTaskData(IPage<InspectionTaskVO> pages) {
        List<AppInspectionTaskVO> data = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(pages.getRecords())) {
            for (InspectionTaskVO sourceVo : pages.getRecords()) {
                // 1. 复制基本属性
                AppInspectionTaskVO targetVo = new AppInspectionTaskVO();
                BeanUtil.copyProperties(sourceVo, targetVo);

                // 2. 手动转换 deviceDetailList（如果有）
                if (sourceVo.getDeviceDetailList() != null) {
                    List<AppInspectionDeviceDetailVO> detailList = sourceVo.getDeviceDetailList().stream()
                            .map(sourceDetail -> {
                                AppInspectionDeviceDetailVO targetDetail = new AppInspectionDeviceDetailVO();
                                BeanUtil.copyProperties(sourceDetail, targetDetail);
                                return targetDetail;
                            })
                            .collect(Collectors.toList());
                    targetVo.setDeviceDetailList(detailList);
                }

                data.add(targetVo);
            }
        }
        return data;
    }

    /**
     * 根据工单负责人id查询工单
     *
     * @param map
     * @return
     */
    @Override
    public R<AppPageVO<AppWorkOrderVO>> getOrderByCommander(Map map) {
        List<String> strings = Arrays.asList("0", "3");
        Query query = new Query();
        query.setCurrent((Integer) map.get("pageNo"));
        query.setSize((Integer) map.get("pageSize"));
        LambdaQueryWrapper<InspectionTask> queryWrapper = new LambdaQueryWrapper<>();
        //结束时间要大于等于当前时间
        queryWrapper.ge(InspectionTask::getEndDate, new Date());
//        //飞手是是当前用户
//        queryWrapper.eq(InspectionTask::getUavOperator, userId);
        //工单是执行中状态
        queryWrapper.notIn(InspectionTask::getInspectionTaskStatus, strings);
        queryWrapper.likeRight(InspectionTask::getDeptCode, AuthUtil.getDeptCode());
        //暂无升压站 后续接入删除此语句
//        queryWrapper.ne(WorkOrder::getBusinessSystem, BizDictEnum.DEVICE_TYPE_BOOSTER_STATION.getCode());
        //id降序
        queryWrapper.orderByDesc(InspectionTask::getCreateTime);
        //分页查询
        IPage<InspectionTask> pages = taskService.page(Condition.getPage(query), queryWrapper);
        AppPageVO<AppWorkOrderVO> pageVo = new AppPageVO<>();
        List<AppWorkOrderVO> workOrder = convertWorkerData(pages);
        pageVo.setData(workOrder);
        pageVo.setTotal(pages.getTotal());
        pageVo.setCurrent(pages.getCurrent());
        return R.data(pageVo);
    }


    private List<AppWorkOrderVO> convertWorkerData(IPage<InspectionTask> pages) {
        List<AppWorkOrderVO> data = new ArrayList<>();
        //分页数据不为空
        if (CollectionUtil.isNotEmpty(pages.getRecords())) {
            data = pages.getRecords().stream().map(x -> {
                AppWorkOrderVO vo = new AppWorkOrderVO();
                vo.setInspectGuid(String.valueOf(x.getId()));
                //工单名称传工作票号
                vo.setInspectName(x.getInspectionTaskNo());
                //签发人传创建人，国华新能源中创建人和负责人基本是同一个人
                vo.setSigner(convertUserId(x.getCreateUser()));
                //暂无详细审核流程，审核人和负责人一致
                vo.setAppRover(vo.getSigner());
                vo.setPlanStartTime(getStringDateCodeByDate(x.getStartDate()));
                vo.setPlanEndTime(getStringDateCodeByDate(x.getEndDate()));
                vo.setPlanPermitTime(getStringDateCodeByDate(x.getStartDate()));
                vo.setWorkPermitPeople(vo.getSigner());
                vo.setWorkerPeople(vo.getSigner());
                vo.setOrderType("0");
                vo.setWorkerGroupName(SysCache.getDeptNameByDeptCode(x.getDeptCode()));
                vo.setInspecStatus(DictBizCache.getValue(MainBizEnum.WORK_ORDER_STATUS.getCode(), x.getInspectionTaskStatus()));
                vo.setWorkTName(DictBizCache.getValue(BizDictEnum.INSPECTION_TYPE.getCode(), x.getInspectionType()));
                // app 要求升压站与线路返回的一致
                if ("TMS_LINE".equals(x.getInspectionType()) || "BOOSTER".equals(x.getInspectionType())) {
                    vo.setInspectMode(1);
                    vo.setInspectModeList(Arrays.asList("0", "1", "3"));
                }
                if ("FAN".equals(x.getInspectionType())) {
                    //风机巡检
                    vo.setInspectMode(20);
                    vo.setInspectModeList(Collections.singletonList("20"));
                }
                if ("PV".equals(x.getInspectionType())) {
                    //光伏巡检
                    vo.setInspectMode(19);
                    vo.setInspectModeList(Collections.singletonList("19"));
                }
                return vo;
            }).collect(Collectors.toList());
        }
        return data;
    }


    /**
     * 用户id转换成中文名
     */
    private String convertUserId(Object userId) {
        if (!StringUtil.isEmpty(userId)) {
            User user = new User();
            R<User> userR;
            if (userId instanceof String) {
                userR = userClient.userInfoById(String.valueOf(userId));
                if (userR.isSuccess()) {
                    user = userR.getData();
                }
            } else if (userId instanceof Long) {
                userR = userClient.userInfoById(String.valueOf(userId));
                if (userR.isSuccess()) {
                    user = userR.getData();
                }
            }
            return user == null ? "" : user.getRealName();
        } else {
            return "";
        }
    }

    /**
     * @return 日期转换成时间戳
     * @title: getStringDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public Long getStringDateCodeByDate(String date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long time = null;
        try {
            time = formatter.parse(date).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }


    /**
     * 查询工单设备
     *
     * @param workOrderGuid
     * @return
     */
    @Override
    public R<List<AppWorkOderDetailVO>> getOrderDeviceByOrderGuid(String workOrderGuid) {
        List<AppWorkOderDetailVO> list = new ArrayList<>();
        //查询工单巡检的设备
        List<InspectionDeviceDetailWithType> tbFlyTaskLineList = inspectionDeviceDetailService.listWithType(workOrderGuid);
        if (CollectionUtil.isNotEmpty(tbFlyTaskLineList)) {
            //Map<Integer, List<String>> deviceGroupByDeviceType = tbFlyTaskLineList.stream().collect(Collectors.groupingBy(InspectionDeviceDetail::getDeviceLevel, Collectors.collectingAndThen(Collectors.toList(), f -> f.stream().map(InspectionDeviceDetail::getDeviceId).collect(Collectors.toList()))));
            HashMap<String, List<String>> deviceGroupByDeviceType = Maps.newHashMap();
            //按设备类型分组
            Map<String, List<InspectionDeviceDetailWithType>> deviceGroupByType = tbFlyTaskLineList.stream().collect(Collectors.groupingBy(InspectionDeviceDetailWithType::getDeviceType));
            //按类型收集deviceGuid
            deviceGroupByType.forEach((key, value) -> deviceGroupByDeviceType.put(key, value.parallelStream().map(InspectionDeviceDetailWithType::getDeviceId).collect(Collectors.toList())));

            List<DictBiz> list1 = DictBizCache.getList(CommonConstant.INSPECTION_TYPE);
            Map<String, String> collect = list1.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
            Map<String, AllDeviceInfoForDefectDTO> deviceInformation = deviceForMainService.getDeviceInformationForDefect(deviceGroupByDeviceType);

            Map<Optional<String>, List<InspectionDeviceDetailWithType>> lineDevice;
            AppWorkOderDetailVO appWorkOderDetailVO;
            int i;
            List<AppWorkOderDeviceVO> devices;
            AllDeviceInfoForDefectDTO allDeviceInfoForDefectDTO;
            Map<String, List<InspectionDeviceDetailWithType>> groupByDeviceType;
            List<DictBiz> voltageLevelDic = DictBizCache.getList("voltage_level");
            Map<String, String> voltageLevel = voltageLevelDic.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (k1, k2) -> k1));
            Map<String, List<InspectionDeviceDetailWithType>> groupByWorkOrder = tbFlyTaskLineList.stream().collect(Collectors.groupingBy(InspectionDeviceDetailWithType::getInspectionTaskId));
            for (Map.Entry<String, List<InspectionDeviceDetailWithType>> order : groupByWorkOrder.entrySet()) {
                groupByDeviceType = order.getValue().stream().collect(Collectors.groupingBy(InspectionDeviceDetailWithType::getDeviceType));
                for (Map.Entry<String, List<InspectionDeviceDetailWithType>> entry : groupByDeviceType.entrySet()) {
                    switch (entry.getKey()) {
                        case "TMS_LINE":
                            //按父级计划设备分组
                            lineDevice = entry.getValue().stream().collect(Collectors.groupingBy(e -> Optional.ofNullable(e.getParentId())));
                            //没有父级设备为线路 循环线路
                            for (Map.Entry<Optional<String>, List<InspectionDeviceDetailWithType>> workOrderDevice : lineDevice.entrySet()) {
                                appWorkOderDetailVO = new AppWorkOderDetailVO();
                                devices = new ArrayList<>();
                                i = 1;
                                appWorkOderDetailVO.setGroupGuid(workOrderDevice.getKey().get());
                                appWorkOderDetailVO.setGroupType(0);
                                for (InspectionDeviceDetailWithType orderDevice : workOrderDevice.getValue()) {
                                    allDeviceInfoForDefectDTO = deviceInformation.get(orderDevice.getDeviceId());
                                    if (ObjectUtil.isEmpty(allDeviceInfoForDefectDTO)) {
                                        continue;
                                    }
                                    appWorkOderDetailVO.setGroupName(allDeviceInfoForDefectDTO.getGroupName());
                                    devices.add(AppWorkOderDeviceVO.builder().
                                            deviceGuid(String.valueOf(orderDevice.getDeviceId())).
                                            deviceName(allDeviceInfoForDefectDTO.getDeviceName()).
                                            deviceType(0).
                                            groupGuid(workOrderDevice.getKey().get()).
                                            groupName(appWorkOderDetailVO.getGroupName()).
                                            la(allDeviceInfoForDefectDTO.getLa()).
                                            lo(allDeviceInfoForDefectDTO.getLo()).al(allDeviceInfoForDefectDTO.getAl()).
                                            deviceNumber(i++ + "").
                                            groupType(0).
                                            inspect(orderDevice.getInspect()).
                                            voltageLevel(voltageLevel.get(allDeviceInfoForDefectDTO.getVoltageLevel())).
                                            sort(allDeviceInfoForDefectDTO.getSort()).
                                            build());
                                }
                                try {
                                    devices = devices.stream().sorted(Comparator.comparing(AppWorkOderDeviceVO::getSort)).collect(Collectors.toList());
                                    for (int i1 = 0; i1 < devices.size(); i1++) {
                                        devices.get(i1).setDeviceNumber(String.valueOf(i1));
                                    }
                                } catch (Exception e) {
                                    log.error("杆塔排序失败！");
                                }
                                appWorkOderDetailVO.setDevices(devices);
                                appWorkOderDetailVO.setWorkType("");
                                appWorkOderDetailVO.setInspectGuid(workOrderDevice.getValue().get(0).getInspectionTaskId());
                                appWorkOderDetailVO.setInspectMode(1);
                                appWorkOderDetailVO.setInspectModeList(Arrays.asList("0", "1", "3"));
                                appWorkOderDetailVO.setWorkTName(collect.get(workOrderDevice.getValue().get(0).getInspectionType()));
                                list.add(appWorkOderDetailVO);
                            }
                            break;
                        case "FAN":
                            for (InspectionDeviceDetailWithType workOrderDevice : entry.getValue()) {
                                appWorkOderDetailVO = new AppWorkOderDetailVO();
                                devices = new ArrayList<>();
                                allDeviceInfoForDefectDTO = deviceInformation.get(workOrderDevice.getDeviceId());
                                if (ObjectUtil.isEmpty(allDeviceInfoForDefectDTO)) {
                                    continue;
                                }
                                appWorkOderDetailVO.setGroupName(allDeviceInfoForDefectDTO.getDeviceName());
                                appWorkOderDetailVO.setGroupGuid(workOrderDevice.getDeviceId());
                                appWorkOderDetailVO.setGroupType(1);
                                devices.add(AppWorkOderDeviceVO.builder().
                                        deviceGuid(workOrderDevice.getDeviceId()).
                                        deviceName(allDeviceInfoForDefectDTO.getDeviceName()).
                                        deviceType(1).
                                        groupGuid(workOrderDevice.getDeviceId()).
                                        groupName(appWorkOderDetailVO.getGroupName()).
                                        la(allDeviceInfoForDefectDTO.getLa()).
                                        lo(allDeviceInfoForDefectDTO.getLo()).
                                        al(allDeviceInfoForDefectDTO.getAl()).
                                        deviceNumber(allDeviceInfoForDefectDTO.getDeviceName()).
                                        groupType(1).
                                        inspect(workOrderDevice.getInspect()).
                                        bladeLength(allDeviceInfoForDefectDTO.getBladeLength()).
                                        deviceAltitude(allDeviceInfoForDefectDTO.getDeviceAltitude()).
                                        build());
                                appWorkOderDetailVO.setDevices(devices);
                                appWorkOderDetailVO.setWorkType("");
                                appWorkOderDetailVO.setInspectGuid(String.valueOf(workOrderDevice.getInspectionTaskId()));
                                appWorkOderDetailVO.setInspectMode(20);
                                appWorkOderDetailVO.setInspectModeList(Arrays.asList("20"));
                                appWorkOderDetailVO.setWorkTName(collect.get(workOrderDevice.getInspectionType()));
                                list.add(appWorkOderDetailVO);
                            }
                            break;
                        case "BOOSTER":
                            for (InspectionDeviceDetailWithType workOrderDevice : entry.getValue()) {
                                appWorkOderDetailVO = new AppWorkOderDetailVO();
                                devices = new ArrayList<>();
                                allDeviceInfoForDefectDTO = deviceInformation.get(workOrderDevice.getDeviceId());
                                if (ObjectUtil.isEmpty(allDeviceInfoForDefectDTO)) {
                                    continue;
                                }
                                appWorkOderDetailVO.setGroupName(allDeviceInfoForDefectDTO.getDeviceName());
                                appWorkOderDetailVO.setGroupGuid(workOrderDevice.getDeviceId());
                                appWorkOderDetailVO.setGroupType(3);  // app 未适配  设备类型改为0
                                devices.add(AppWorkOderDeviceVO.builder().
                                        deviceGuid(workOrderDevice.getDeviceId()).
                                        deviceName(allDeviceInfoForDefectDTO.getDeviceName() + "_booster").
                                        deviceType(3).
                                        groupGuid(workOrderDevice.getDeviceId()).
                                        groupName(appWorkOderDetailVO.getGroupName()).
                                        la(allDeviceInfoForDefectDTO.getLa()).
                                        lo(allDeviceInfoForDefectDTO.getLo()).
                                        al(StringUtil.isNotBlank(allDeviceInfoForDefectDTO.getAl()) ? allDeviceInfoForDefectDTO.getAl() : "0").
                                        deviceNumber(1 + "").
                                        groupType(3).   // app 未适配  设备类型改为0
                                                inspect(workOrderDevice.getInspect()).
                                        bladeLength(allDeviceInfoForDefectDTO.getBladeLength()).
                                        deviceAltitude(allDeviceInfoForDefectDTO.getDeviceAltitude()).
                                        build());
                                appWorkOderDetailVO.setDevices(devices);
                                appWorkOderDetailVO.setWorkType("");
                                appWorkOderDetailVO.setInspectGuid(String.valueOf(workOrderDevice.getInspectionTaskId()));
                                appWorkOderDetailVO.setInspectMode(1);
                                appWorkOderDetailVO.setInspectModeList(Arrays.asList("0", "1", "3"));
                                appWorkOderDetailVO.setWorkTName(collect.get(workOrderDevice.getInspectionType()));
                                list.add(appWorkOderDetailVO);
                            }
                            break;
                        case "PV":
                            for (InspectionDeviceDetailWithType workOrderDevice : entry.getValue()) {
                                appWorkOderDetailVO = new AppWorkOderDetailVO();
                                devices = new ArrayList<>();
                                allDeviceInfoForDefectDTO = deviceInformation.get(workOrderDevice.getDeviceId());
                                if (ObjectUtil.isEmpty(allDeviceInfoForDefectDTO)) {
                                    continue;
                                }
                                appWorkOderDetailVO.setGroupName(allDeviceInfoForDefectDTO.getDeviceName());
                                appWorkOderDetailVO.setGroupGuid(workOrderDevice.getDeviceId());
                                appWorkOderDetailVO.setGroupType(2);
                                devices.add(AppWorkOderDeviceVO.builder().
                                        deviceGuid(workOrderDevice.getDeviceId()).
                                        deviceName(allDeviceInfoForDefectDTO.getDeviceName()).
                                        deviceType(2).
                                        groupGuid(workOrderDevice.getDeviceId()).
                                        groupName(appWorkOderDetailVO.getGroupName()).
                                        la(allDeviceInfoForDefectDTO.getLa()).
                                        lo(allDeviceInfoForDefectDTO.getLo()).
                                        al(allDeviceInfoForDefectDTO.getAl()).
                                        deviceNumber(1 + "").
                                        groupType(2).
                                        inspect(workOrderDevice.getInspect()).
                                        build());
                                appWorkOderDetailVO.setDevices(devices);
                                appWorkOderDetailVO.setWorkType("");
                                appWorkOderDetailVO.setInspectGuid(String.valueOf(workOrderDevice.getInspectionTaskId()));
                                appWorkOderDetailVO.setInspectMode(19);
                                appWorkOderDetailVO.setInspectModeList(Arrays.asList("19"));
                                appWorkOderDetailVO.setWorkTName(collect.get(workOrderDevice.getInspectionType()));
                                list.add(appWorkOderDetailVO);
                            }
                            break;
                    }
                }
            }
        }
        return R.data(list);
    }

    /**
     * app更新执行状态
     *
     * @param workOrderGuid
     * @param deviceGuid
     * @return
     */
    @Override
    public R updateAppInspectStatus(String workOrderGuid, String deviceGuid) {
        if (null == workOrderGuid || null == deviceGuid) {
            return R.fail("工单或设备参数缺失！");
        }
        return R.status(inspectionDeviceDetailService.update(new LambdaUpdateWrapper<InspectionDeviceDetail>()
                .eq(InspectionDeviceDetail::getInspectionTaskId, workOrderGuid)
                .eq(InspectionDeviceDetail::getDeviceId, deviceGuid)
                .set(InspectionDeviceDetail::getInspect, 1)));
    }

    @Override
    public R<Boolean> saveDefectTaggingInfoCollect(DefectTaggingInfoCollectDTO dto) {
        //return R.data(defectTaggingInfoCollectService.save(dto));
        return null;
    }

    @Override
    public R<HashMap<String, String>> getDeviceInformationForOrderPic(Map<String, List<Long>> deviceGuids) {
        return deviceForMainService.getDeviceInformationForOrderPic(deviceGuids);
    }

    @Override
    public R uploadPicForApp(AppInspectionPictureDTO param) {
        //巡检图片保存
        inspectionPictureService.saveInspectionPicture(param.getInspectionPicture());
        //绑定图片到设备
        inspectionPictureService.bindPic(param.getBindPicDTO());
        return R.status(true);
    }

    @Override
    public R<List<AppLedgerDetailVO>> getDeviceByDeptId(Map<String, Object> map) {
        return deviceForMainService.getDeviceByDeptId(map);
    }

    @Override
    public R<AppDeviceDetailVO> getDeviceByGroupGuid(Map<String, Object> map) {
        return deviceForMainService.getDeviceByGroupGuid(map);
    }

    @Override
    public R<Map<String, String>> getUavInformationForApp(String sn) {
        return deviceForMainService.getUavInformationForApp(sn);
    }

    @Override
    public R<AppRouteResponseVO> getRouteInfo(Map<String, String> map) {
        return routeService.getRouteInfo(map);
    }

    @Override
    public R<DeviceForMainNameDTO> getDeviceNameById(Map<String, String> map) {
        return deviceForMainService.getDeviceById(map);
    }

    @Override
    public R<QrCodeDeviceVO> getDeviceInfo(String deviceId, String deviceType) {
        return deviceQrCodeService.getDeviceInfo(deviceId, deviceType);
    }

    @Override
    public R<Page<QrCodeManagementVO>> getDevicePageByType(String current, String size, String deviceType, String deptCode) {
        Query query = new Query();
        if (!StringUtil.isAllBlank(current, size)) {
            query.setCurrent(Integer.parseInt(current));
            query.setSize(Integer.parseInt(size));
        }
        QrCodeQueryDTO dto = new QrCodeQueryDTO();
        dto.setDeviceType(deviceType);
        dto.setDeptCode(deptCode);
        Page<QrCodeManagementVO> page = (Page<QrCodeManagementVO>) deviceQrCodeService.selectQrCodePage(Condition.getPage(query), dto);
        return R.data(page);
    }

    @Override
    public R<FanVO> getDeviceFanInfoById(String fanId) {
        return R.data(BeanUtil.copy(fanService.getById(fanId), FanVO.class));
    }

    @Override
    public R<Map<String, BasicCommonVO>> getDeviceByDeviceIds(List<String> deviceIds, String deviceType) {
        return R.data(commonService.getDeviceByDeviceIds(deviceIds, deviceType));
    }

    @Override
    public R<Map<String, BasicCommonVO>> getCommonDeviceByIds(List<String> deviceIds, String deviceType, String deptCode) {
        Map<String, BasicCommonVO> collect = commonService.getCommonDeviceByIds(deviceIds, deviceType, deptCode).stream()
                .collect(Collectors.toMap(BasicCommonVO::getDeviceId, vo -> vo));
        return R.data(collect);
    }

    @Override
    public R<InspectionTaskVO> getTaskAndUserInfo(String userId) {
        try {
            // 最新任务
            InspectionTask task = taskService.getOne(new LambdaQueryWrapper<InspectionTask>()
                    .eq(InspectionTask::getResponsibleUser, userId)
                    .orderByDesc(InspectionTask::getCreateTime)
                    .last("limit 1"));
            if (task == null) {
                return R.data(null);
            }
            InspectionTaskVO taskVO = BeanUtil.copyProperties(task, InspectionTaskVO.class);

            // 获取任务的设备
            List<InspectionDeviceDetail> deviceDetailList = inspectionDeviceDetailService.list(new LambdaQueryWrapper<InspectionDeviceDetail>()
                    .eq(InspectionDeviceDetail::getInspectionTaskId, task.getId()));
            if (CollectionUtil.isNotEmpty(deviceDetailList)) {
                List<String> deviceIds = deviceDetailList.stream()
                        .map(InspectionDeviceDetail::getDeviceId)
                        .collect(Collectors.toList());

                Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, task.getInspectionType());
                String deviceNames = deviceMap.values().stream()
                        .map(BasicCommonVO::getDeviceName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));

                taskVO.setDeviceNames(StringUtil.isBlank(deviceNames) ? "设备" : deviceNames);
            }
            return R.data(taskVO);
        } catch (Exception e) {
            log.error("查询任务和用户信息异常", e);
            return R.fail("查询任务和用户信息失败：" + e.getMessage());
        }
    }


}
