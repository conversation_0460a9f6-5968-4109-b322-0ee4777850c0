package com.allcore.main.code.station.wrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.main.code.station.entity.StationManagement;
import com.allcore.main.code.station.vo.StationManagementVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;

/**
 * 翻译
 *
 * <AUTHOR>
 * @date 2023/10/07 09:36
 **/
public class StationManagementWrapper extends BaseEntityWrapper<StationManagement, StationManagementVO> {

    public static StationManagementWrapper build() {
        return new StationManagementWrapper();
    }

    @Override
    public StationManagementVO entityVO(StationManagement entity) {

        StationManagementVO vo = Objects.requireNonNull(BeanUtil.copy(entity, StationManagementVO.class));
        translate(vo);
        return vo;
    }

    private void translate(StationManagementVO vo) {

        Dept dept = SysCache.getDeptByDeptCode(vo.getDeptCode());
        if (Objects.nonNull(dept)) {
            vo.setParentDeptName(SysCache.getDeptName(dept.getParentId()));
        }
    }

    public List<StationManagementVO> listVoByVo(List<StationManagementVO> list) {

        return (List<StationManagementVO>)list.stream().map(vo -> {
            translate(vo);

            return vo;
        }).collect(Collectors.toList());
    }

}
