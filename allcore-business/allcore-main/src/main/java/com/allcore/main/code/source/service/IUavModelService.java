package com.allcore.main.code.source.service;

import java.util.List;
import java.util.Map;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.source.dto.UavModelDTO;
import com.allcore.main.code.source.dto.UavModelSaveDTO;
import com.allcore.main.code.source.entity.UavModel;
import com.allcore.main.code.source.vo.UavModelVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IUavModelService extends ZxhcService<UavModel> {

    /**
     * 自定义分页
     *
     * @param page
     * @param uavModel
     * @return
     */
    IPage<UavModelVO> selectUavModelPage(IPage<UavModelVO> page, UavModelDTO uavModel);

    /**
     * 新增
     *
     * @param uavModel
     * @return
     */
    R saveUavModel(UavModelSaveDTO uavModel);

    /**
     * 修改
     *
     * @param uavModel
     * @return
     */
    R updateUavModelById(UavModelSaveDTO uavModel);

    /**
     * 删除
     *
     * @param strList
     * @return
     */
    R deleteUavModelLogic(List<String> strList);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    R<UavModelVO> getUavModelOne(String id);

    /**
     *根据无人机类型,品牌查询型号列表
     *
     * @param uavType
     * @param uavBrand
     * @param deptCode
     * @return
     */
    R findListByUavTypeAndUavBrand(String uavType, String uavBrand,String deptCode);
}
