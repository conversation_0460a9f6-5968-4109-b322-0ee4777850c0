package com.allcore.main.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 坐标工具类
 * <AUTHOR>
 */
public class CoordinateUtil {

	public static Coordinate getCoordinate(String coordinateSet) {
		if (StrUtil.isNotBlank(coordinateSet) && coordinateSet.equals("[]")) {
			return new Coordinate();
		}
		int[][] coordinates = JSONObject.parseObject(coordinateSet, int[][].class);
		if (coordinates == null) {
			return new Coordinate();
		}
		List<Integer> xList = new ArrayList<>();
		List<Integer> yList = new ArrayList<>();
		for (int[] coordinate : coordinates) {
			xList.add(coordinate[0]);
			yList.add(coordinate[1]);
		}
		return new Coordinate(Collections.min(xList) + "", Collections.min(yList) + "", Collections.max(xList) + "", Collections.max(yList) + "");
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Coordinate {
		private String xMin;
		private String yMin;
		private String xMax;
		private String yMax;
	}
}
