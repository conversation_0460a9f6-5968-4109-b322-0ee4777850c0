package com.allcore.main.utils;

import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.main.code.source.entity.Tower;
import com.allcore.main.code.source.excel.TowerExcel;
import com.allcore.main.code.source.mapper.TowerMapper;
import com.allcore.system.entity.Region;
import com.allcore.system.feign.ISysClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import lombok.extern.slf4j.Slf4j;

import static com.allcore.main.constant.MainConstant.NINE;

/**
 * <p>
 * 风机模版导入校验
 * </p>
 *
 * @author: sunkun Date: 2023/8/21
 */
@Component
@Slf4j
public class TowerImportVerifyHandler implements IExcelVerifyHandler<TowerExcel> {

    @Autowired
    private ISysClient sysClient;

    @Autowired
    private TowerMapper towerMapper;
    private List<Region> regionList;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(TowerExcel towerExcel) {
        StringJoiner joiner = new StringJoiner(",");

        if (AuthUtil.getDeptCode().length() != NINE) {
            joiner.add("非场站级单位，没有权限导入杆塔!");
        }
        towerExcel.setDeptCode(AuthUtil.getDeptCode());
        checkRepeat(towerExcel, joiner);
        if (joiner.length() != 0) {
            return new ExcelVerifyHandlerResult(false, joiner.toString());
        }
        return new ExcelVerifyHandlerResult(true);
    }

    private void checkRepeat(TowerExcel tower, StringJoiner joiner) {
        Optional.ofNullable(
            towerMapper.selectOne(new LambdaQueryWrapper<Tower>().eq(Tower::getTowerName, tower.getTowerName())
                .eq(Tower::getDeptCode, tower.getDeptCode()).eq(Tower::getIsDeleted, 0)))
            .ifPresent(f -> {
                joiner.add("杆塔名称重复:" + tower.getTowerName());
            });
    }
}
