package com.allcore.main.code.inspection.service.impl;

import static com.allcore.common.constant.BasicConstant.*;
import static com.allcore.common.enums.BizDictEnum.TAGGING_TYPE_ALREADY_VANISH_DEFECT;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.main.code.inspection.dto.InspectionTaggingQueryDTO;
import com.allcore.main.code.source.mapper.DeviceQrCodeMapper;
import com.allcore.main.code.source.vo.QrCodeManagementVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.*;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.dict.entity.DictBiz;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.inspection.dto.InspectionTaskPicQueryDTO;
import com.allcore.main.code.inspection.entity.InspectionPicture;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.mapper.InspectionPictureMapper;
import com.allcore.main.code.inspection.mapper.InspectionPictureTaggingMapper;
import com.allcore.main.code.inspection.mapper.InspectionTaskMapper;
import com.allcore.main.code.inspection.service.IInspectionPictureTaggingService;
import com.allcore.main.code.inspection.vo.*;
import com.allcore.main.code.inspection.wrapper.InspectionPictureTaggingWrapper;
import com.allcore.main.code.solve.entity.RemovePictureTagging;
import com.allcore.main.code.solve.entity.RemovePictureTaggingDetail;
import com.allcore.main.code.solve.entity.RemovePictureTaggingFile;
import com.allcore.main.code.solve.service.IRemovePictureTaggingDetailService;
import com.allcore.main.code.solve.service.IRemovePictureTaggingFileService;
import com.allcore.main.code.solve.service.IRemovePictureTaggingService;
import com.allcore.main.code.solve.vo.DefectHistoryDetailVO;
import com.allcore.main.code.solve.vo.RemovePictureTaggingFileVO;
import com.allcore.main.code.solve.vo.RemoveTaskVO;
import com.allcore.main.code.source.entity.PvComponents;
import com.allcore.main.code.source.service.IPvAreaService;
import com.allcore.main.code.source.service.IPvComponentsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@AllArgsConstructor
@Slf4j
public class InspectionPictureTaggingServiceImpl
        extends ZxhcServiceImpl<InspectionPictureTaggingMapper, InspectionPictureTagging>
        implements IInspectionPictureTaggingService {

    private final CommonService commonService;
    private final IOssClient ossClient;
    private final InspectionPictureMapper inspectionPictureMapper;

    private final InspectionTaskMapper inspectionTaskMapper;

    private final IPvComponentsService pvComponentsService;
    private final IPvAreaService pvAreaService;

    private final IRemovePictureTaggingDetailService removePictureTaggingDetailService;

    private final InspectionPictureTaggingMapper inspectionPictureTaggingMapper;

    private final IRemovePictureTaggingFileService removePictureTaggingFileService;

    private final IRemovePictureTaggingService removePictureTaggingService;

    private final DeviceQrCodeMapper deviceQrCodeMapper;

    @Override
    public R getTaskReport(InspectionTaskPicQueryDTO dto) {

        InspectionTaskOnlineReportingVO vo = new InspectionTaskOnlineReportingVO();
        // 缺陷图片集合
        List<InspectionPictureTagging> entityList = this.list(new LambdaQueryWrapper<InspectionPictureTagging>()
                .eq(InspectionPictureTagging::getInspectionTaskId, dto.getInspectionTaskId())
                .eq(StringUtil.isNotBlank(dto.getDefectLevel()), InspectionPictureTagging::getDefectLevel,
                        dto.getDefectLevel())
                .eq(StringUtil.isNotBlank(dto.getDefectCode()), InspectionPictureTagging::getDefectDescription,
                        dto.getDefectCode())
                // .eq(StringUtil.isNotBlank(dto.getDeviceId()), InspectionPictureTagging::getDeviceId, dto.getDeviceId())
                .eq(InspectionPictureTagging::getDefectType, "defect"));
        if (CollectionUtil.isEmpty(entityList)) {
            return R.data(vo);
        }

        // 过滤解绑的缺陷
        List<String> picBindIds = getBindPicIds(dto);
        this.filterUnBindPicDefect(entityList, picBindIds);
        // 针对光伏过滤可见光缺陷和重复缺陷
        if (StringUtil.equals(PV, dto.getDeviceType()) || StringUtil.equals(PV_AREA, dto.getDeviceType())) {
            // 过滤可见光缺陷
            this.filterLightDefect(entityList);
            entityList = this.filterRepeatDefect(entityList);
        }
        if (StringUtil.isNotBlank(dto.getDeviceId())) {
            // 为了解决异常场景：缺陷在两个区域中都存在，把dto.getDeviceId()在sql中去掉，放在代码中过滤
            entityList = getDeviceIdFilter(entityList, dto.getDeviceId());

        }
        // 缺陷数量
        vo.setAllDefectNum(entityList.size() + "");
        List<DictBiz> defectCode = null;
        switch (dto.getDeviceType()) {
            case FAN:
                defectCode = DictBizCache.getList(BizDictEnum.DEFECT_CODE_FAN.getCode());
                break;
            case PV:
                defectCode = DictBizCache.getList(BizDictEnum.DEFECT_CODE_PV.getCode());
                break;
            case TMS_LINE:
                defectCode = DictBizCache.getList(BizDictEnum.DEFECT_CODE_TMS_TOWER.getCode());
                break;
            default:
                break;
        }
        if (CollectionUtil.isEmpty(defectCode) || CollectionUtil.isEmpty(entityList)) {
            return R.data(vo);
        }
        // 截取前6位
        String str = entityList.get(0).getDefectDescription().substring(0, 6);
        List<DefectTypeNameNumVO> defectInfo = Lists.newArrayList();
        for (DictBiz type : defectCode) {
            List<InspectionPictureTagging> filteredList = entityList.stream()
                    .filter(tagging -> StringUtil.equals(tagging.getDefectDescription().substring(0, 6), type.getDictKey()))
                    .collect(Collectors.toList());
            List<InspectionPictureTagging> filteredRemoveList = entityList.stream()
                    .filter(tagging -> StringUtil.equals(tagging.getDefectDescription().substring(0, 6), type.getDictKey())
                            && "already_vanish_defect".equals(tagging.getEliminateStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(filteredList)) {
                DefectTypeNameNumVO defectVo = new DefectTypeNameNumVO();
                defectVo.setDefectType(type.getDictKey());
                defectVo.setDefectName(type.getDictValue());
                defectVo.setNum(filteredList.size() + "");
                defectVo.setRemoveNum(filteredRemoveList.size() + "");
                defectInfo.add(defectVo);
            }
        }
        vo.setDefectInfo(defectInfo);
        return R.data(vo);
    }

    private List<InspectionPictureTagging> getDeviceIdFilter(List<InspectionPictureTagging> entityList,
                                                             String deviceId) {
        List<InspectionPictureTagging> filteredList =
                entityList.stream().filter(tagging -> tagging.getDeviceId().equals(deviceId)).collect(Collectors.toList());
        return filteredList;
    }

    @Override
    public R getDefectDetailInfo(InspectionTaskPicQueryDTO dto) {
        List<InspectionPictureTagging> entityList = this.list(new LambdaQueryWrapper<InspectionPictureTagging>()
                .eq(InspectionPictureTagging::getInspectionTaskId, dto.getInspectionTaskId())
                .eq(StringUtil.isNotBlank(dto.getDefectLevel()), InspectionPictureTagging::getDefectLevel,
                        dto.getDefectLevel())
                // .eq(StringUtil.isNotBlank(dto.getDeviceId()), InspectionPictureTagging::getDeviceId, dto.getDeviceId())
                .eq(InspectionPictureTagging::getDefectType, "defect")
                .apply(StringUtil.isNotBlank(dto.getDefectCode()), "LEFT(defect_description, 6) = " + dto.getDefectCode()));
        if (CollectionUtil.isEmpty(entityList)) {
            return R.data(new ArrayList<DefectInfoOnlineReportingVO>());
        }
        // 过滤解绑的缺陷
        List<String> picBindIds = getBindPicIds(dto);
        this.filterUnBindPicDefect(entityList, picBindIds);
        // 针对光伏过滤可见光缺陷和重复缺陷
        if (StringUtil.equals(PV, dto.getDeviceType()) || StringUtil.equals(PV_AREA, dto.getDeviceType())) {
            // 过滤可见光缺陷
            this.filterLightDefect(entityList);
            // 过滤重复缺陷
            entityList = this.filterRepeatDefect(entityList);
        }
        if (StringUtil.isNotBlank(dto.getDeviceId())) {
            // 为了解决异常场景：缺陷在两个区域中都存在，把dto.getDeviceId()在sql中去掉，放在代码中过滤
            entityList = getDeviceIdFilter(entityList, dto.getDeviceId());

        }
        List<DefectInfoOnlineReportingVO> vo = BeanUtil.copy(entityList, DefectInfoOnlineReportingVO.class);
        // 设置相关信息
        this.setRemoveDefectInfo(vo, dto);

        List<DefectInfoOnlineReportingVO> returnVo = InspectionPictureTaggingWrapper.build().listVoByVo(vo);
        if (StringUtil.equals(FAN, dto.getDeviceType())) {
            if (StringUtil.isNotBlank(dto.getPhotoType())) {
                returnVo = returnVo.stream().filter(fi -> dto.getPhotoType().equals(fi.getBigPosition()))
                        .collect(Collectors.toList());
            }
            // 解析图片名称给出对应叶片、缺陷位置信息
            setFanBladeInfo(returnVo, dto);
            return R.data(processData(returnVo));
        }
        return R.data(returnVo);
    }

    @Override
    public R pvRemoveDefectDetailInfo(InspectionTaskPicQueryDTO dto) {
        dto.setDeviceType(PV);
        List<InspectionPictureTagging> entityList = new ArrayList<>();
        InspectionPictureTagging entity =
                inspectionPictureTaggingMapper.selectById(dto.getInspectionPictureTaggingId());
        dto.setInspectionTaskId(entity.getInspectionTaskId());
        entityList.add(entity);
        if (CollectionUtil.isEmpty(entityList)) {
            return R.data(new ArrayList<DefectInfoOnlineReportingVO>());
        }

        List<DefectInfoOnlineReportingVO> vo = BeanUtil.copy(entityList, DefectInfoOnlineReportingVO.class);
        // 设置相关信息
        this.setRemoveDefectInfo(vo, dto);

        List<DefectInfoOnlineReportingVO> returnVo = InspectionPictureTaggingWrapper.build().listVoByVo(vo);

        return R.data(returnVo);
    }

    private List<String> getBindPicIds(InspectionTaskPicQueryDTO dto) {
        List<InspectionPicture> picList = inspectionPictureMapper.selectList(Wrappers.<InspectionPicture>lambdaQuery()
                .eq(InspectionPicture::getInspectionTaskId, dto.getInspectionTaskId())
                .isNotNull(InspectionPicture::getDeviceId));
        return picList.stream().map(m -> m.getId()).collect(Collectors.toList());

    }

    public List<InspectionPictureTagging> filterRepeatDefect(List<InspectionPictureTagging> entityList) {
        return entityList.stream()
                .collect(
                        Collectors
                                .toMap(
                                        entity -> Arrays.asList(entity.getDefectDescription(), entity.getPvComponentId(),
                                                entity.getPvComponentName()),
                                        entity -> entity, (existingEntity, newEntity) -> existingEntity))
                .values().stream().collect(Collectors.toList());
    }

    /**
     * （遍历 如果defect_key 在 light_defect_key中存在 则舍弃）
     *
     * @param taggingIds
     * @return void
     * <AUTHOR>
     * @date 2024/01/18 15:58
     */
    private void filterLightDefect(List<InspectionPictureTagging> taggingIds) {
        Iterator<InspectionPictureTagging> iterator = taggingIds.iterator();
        while (iterator.hasNext()) {
            InspectionPictureTagging currentObj = iterator.next();

            for (int i = 0; i < taggingIds.size(); i++) {
                if (!currentObj.equals(taggingIds.get(i))
                        && currentObj.containsFieldInAnotherField(taggingIds.get(i))) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    private void filterUnBindPicDefect(List<InspectionPictureTagging> taggingIds, List<String> picBindIds) {
        Iterator<InspectionPictureTagging> iterator = taggingIds.iterator();
        while (iterator.hasNext()) {
            InspectionPictureTagging tagging = iterator.next();
            if (!picBindIds.contains(tagging.getInspectionPictureId())) {
                iterator.remove();
            }
        }

    }

    public static List<DefectInfoOnlineReportingVO> areaProcessData(List<DefectInfoOnlineReportingVO> returnVo) {
        Map<String, List<DefectInfoOnlineReportingVO>> groupedData = returnVo.stream()
                .collect(Collectors.groupingBy(vo -> vo.getFileName().substring(0, vo.getFileName().length() - 5)));

        return groupedData.values().stream().map(group -> {
            List<DefectInfoOnlineReportingVO> zGroup =
                    group.stream().filter(vo -> vo.getFileName().charAt(vo.getFileName().length() - 5) == 'Z')
                            .collect(Collectors.toList());

            List<DefectInfoOnlineReportingVO> tGroup =
                    group.stream().filter(vo -> vo.getFileName().charAt(vo.getFileName().length() - 5) == 'T').peek(tVo -> {
                        List<String> redFilePaths = zGroup.stream()
                                .filter(zVo -> tVo.getFileName().substring(1, tVo.getFileName().length() - 5)
                                        .equals(zVo.getFileName().substring(1, zVo.getFileName().length() - 5)))
                                .map(DefectInfoOnlineReportingVO::getFilePath).collect(Collectors.toList());
                        tVo.setRedFilePath(redFilePaths.get(0));
                    }).collect(Collectors.toList());

            return tGroup;
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    private void setFanBladeInfo(List<DefectInfoOnlineReportingVO> returnVo, InspectionTaskPicQueryDTO dto) {

        for (DefectInfoOnlineReportingVO vo : returnVo) {
            String bigPositionZh =
                    DictBizCache.getValue(BizDictEnum.FAN_BIG_POSITION.getCode(), vo.getBigPosition().toLowerCase());
            vo.setBigPositionZh(StringUtil.isBlank(bigPositionZh) ? "" : bigPositionZh.replace("-机舱", ""));

            if (StringUtil.isNotBlank(vo.getBladeOrder())) {
                String bladeOrderZh =
                        DictBizCache.getValue(BizDictEnum.FAN_BLADE_ORDER.getCode(), vo.getBladeOrder().toUpperCase());
                vo.setBladeOrderZh(StringUtil.isBlank(bladeOrderZh) ? "" : bladeOrderZh);
            } else {
                vo.setBladeOrderZh("");
            }
            if (StringUtil.isNotBlank(vo.getDetailPosition())) {
                String detailPositionZh =
                        DictBizCache.getValue(BizDictEnum.FAN_DETAIL_POSITION.getCode(), vo.getDetailPosition().toUpperCase());
                vo.setDetailPositionZh(StringUtil.isBlank(detailPositionZh) ? "" : detailPositionZh);
            }
            /*if (StringUtil.isBlank(vo.getFileName())) {
                continue;
            }
            if (StringUtil.isNotBlank(dto.getFanDefectPosition())
                && vo.getFileName().contains(dto.getFanDefectPosition())) {
                continue;
            }
            String[] parts;
            if (vo.getInspectionPictureFileName().contains("/")) {
                parts = vo.getInspectionPictureFileName().split("/")[1].split("_");
            } else {
                parts = vo.getInspectionPictureFileName().split("_");
            }
            vo.setBigPosition(parts[0]);
            String bigPositionZh =
                DictBizCache.getValue(BizDictEnum.FAN_BIG_POSITION.getCode(), vo.getBigPosition().toLowerCase());
            vo.setBigPositionZh(StringUtil.isBlank(bigPositionZh) ? "" : bigPositionZh);
            if (StringUtil.equals("Blade", parts[0])) {
                vo.setBladeOrder(parts.length > 1 ? parts[1] : "");
                String bladeOrderZh =
                    DictBizCache.getValue(BizDictEnum.FAN_BLADE_ORDER.getCode(), parts[1].toLowerCase());
                vo.setBladeOrderZh(StringUtil.isBlank(bladeOrderZh) ? "" : bladeOrderZh);
                // 叶片需要具体划分A、B、C
                vo.setBigPositionZh(StringUtil.isBlank(bladeOrderZh) ? "" : bladeOrderZh);
                vo.setDetailPosition(parts.length > 2 ? parts[2] : "");
                vo.setDetailPositionZh(getDetailPositionZhValue(vo.getDetailPosition()));
                vo.setPositionName(parts.length > 2
                    ? vo.getDetailPositionZh() + "-" + parts[3].substring(0, parts[3].lastIndexOf(".")) : "");
            } else {
                vo.setDetailPosition(parts.length > 1 ? parts[1] : "");
                vo.setDetailPositionZh(getDetailPositionZhValue(vo.getDetailPosition()));
            
                if (parts.length >= 3 && parts[2].contains(".")) {
                    String result = parts[2].substring(0, parts[2].lastIndexOf("."));
                    vo.setPositionName(vo.getDetailPositionZh() + "-" + result);
                } else {
                    vo.setPositionName("");
                }
            }
            // 兜底
            if (StringUtil.isBlank(vo.getPositionName()) && StringUtil.isNotBlank(vo.getDeviceName())) {
                vo.setPositionName(vo.getDeviceName());
            }*/

        }

    }

    private String getDetailPositionZhValue(String detailPosition) {
        String detailPositionZh =
                DictBizCache.getValue(BizDictEnum.FAN_DETAIL_POSITION.getCode(), detailPosition.toLowerCase());
        return StringUtil.isBlank(detailPositionZh) ? "" : detailPositionZh;
    }

    /*public static List<DefectInfoOnlineFanReportingVO> extractDeviceInfo(List<DefectInfoOnlineReportingVO> returnVo) {
        Map<String, List<DefectInfoOnlineReportingVO>> groupByDevice =
            returnVo.stream().collect(Collectors.groupingBy(vo -> vo.getDeviceId() + vo.getDeviceName()));
    
        return groupByDevice.entrySet().stream()
            .map(entry -> new DefectInfoOnlineFanReportingVO(entry.getValue().get(0).getDeviceId(),
                entry.getValue().get(0).getDeviceName(), entry.getValue()))
            .collect(Collectors.toList());
    }*/

    public static List<DefectInfoOnlineFanReportingVO> processData(List<DefectInfoOnlineReportingVO> returnVo) {
        // 按照deviceName分组，并将deviceName赋值给name
        Map<String, List<DefectInfoOnlineReportingVO>> deviceNameGroup =
                returnVo.stream().collect(Collectors.groupingBy(DefectInfoOnlineReportingVO::getDeviceName));

        // 处理每个deviceName分组，按照bigPositionZh分组，并将bigPositionZh赋值给name
        List<DefectInfoOnlineFanReportingVO> result = deviceNameGroup.entrySet().stream().map(entry -> {
            String name = entry.getKey();
            List<DefectInfoOnlineReportingVO> voList = entry.getValue();
            Map<String, List<DefectInfoOnlineReportingVO>> bigPositionZhGroup =
                    voList.stream().collect(Collectors.groupingBy(DefectInfoOnlineReportingVO::getBigPositionZh));

            List<DefectInfoOnlineFanReportingVO> bladeList = bigPositionZhGroup.entrySet().stream().map(bladeEntry -> {
                String bigPositionZh = bladeEntry.getKey();
                List<DefectInfoOnlineReportingVO> bladeVoList = bladeEntry.getValue();
                return new DefectInfoOnlineFanReportingVO(bigPositionZh, null, bladeVoList, null);
            }).collect(Collectors.toList());

            return new DefectInfoOnlineFanReportingVO(name, String.valueOf(voList.size()), null, bladeList);
        }).collect(Collectors.toList());

        return result;
    }

    private void
    setRemoveDefectInfo(List<DefectInfoOnlineReportingVO> vo, InspectionTaskPicQueryDTO dto) {
        // 已消缺tagging
        List<DefectInfoOnlineReportingVO> filteredList = vo.stream().filter(
                        tagging -> StringUtil.equals(tagging.getEliminateStatus(), TAGGING_TYPE_ALREADY_VANISH_DEFECT.getCode()))
                .collect(Collectors.toList());
        // 设备ids
        List<String> deviceIds = vo.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());

        if(TMS_LINE.equals(vo.get(0).getDeviceType())){
            List<QrCodeManagementVO> qrCodeManagementVOS = deviceQrCodeMapper.selectByDeviceIdsWithType(TMS_TOWER, deviceIds);
            vo.forEach(v->{
                qrCodeManagementVOS.stream().filter(qr->v.getDeviceId().equals(qr.getDeviceId())).findFirst().ifPresent(qr->v.setDeviceName(qr.getDeviceNo()+"_"+qr.getDeviceName()));
            });
        }
        if(FAN.equals(vo.get(0).getDeviceType())){
            List<QrCodeManagementVO> qrCodeManagementVOS = deviceQrCodeMapper.selectByDeviceIdsWithType(FAN, deviceIds);
            vo.forEach(v->{
                qrCodeManagementVOS.stream().filter(qr->v.getDeviceId().equals(qr.getDeviceId())).findFirst().ifPresent(qr->v.setDeviceName("风机"+"_"+qr.getDeviceName()));
            });
        }

        // 设置缺陷原图guid
        setInspectionPictureGuid(vo);
        // 巡检图、缺陷原图guids
        List<String> inspectionPictureFileGuids =
                vo.stream().map(m -> m.getInspectionPictureGuid()).collect(Collectors.toList());
        // 巡检图、缺陷原图guidmap
        Map<String, AllcoreFileVO> inspectionPictureMap = getFileMap(inspectionPictureFileGuids);
        // 缺陷图guids
        List<String> fileGuids = vo.stream().map(m -> m.getFileGuid()).collect(Collectors.toList());
        // 缺陷图guidmap
        Map<String, AllcoreFileVO> fileMap = getFileMap(fileGuids);
        // 缺陷图bigguids
        List<String> bigFileGuids = vo.stream().map(m -> m.getBigFileGuid()).collect(Collectors.toList());
        // 缺陷图bigfileguidmap
        Map<String, AllcoreFileVO> bigFileMap = getFileMap(bigFileGuids);
        // 线路缺陷设备都是杆塔
        String deviceType = dto.getDeviceType();
        if (StringUtil.equals(TMS_LINE, deviceType)) {
            deviceType = TMS_TOWER;
        }
        // 缺陷图bigfileguidmap
        Map<String, AllcoreFileVO> pvAllFileMap = Maps.newHashMap();

        // 缺陷图LightFileMap
        Map<String, AllcoreFileVO> pvLightFileMap = Maps.newHashMap();
        if (StringUtil.equals(deviceType, PV) || StringUtil.equals(deviceType, PV_AREA)) {
            pvLightFileMap = getDefectFileMap(vo, dto.getInspectionTaskId());
        }
        // 设备信息处理成map
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, deviceType);
        // 查消缺任务信息处理成map
        Map<String, Map<String, String>> taggingMap = getTaggingMap(filteredList);
        // 组件设备map
        Map<String, BasicCommonVO> pvComponentMap = getPvComponentMap(deviceType, vo);
        // 组串设备map
        Map<String, BasicCommonVO> pvStringMap = getPvStringMap(pvComponentMap);
        // 返回数据body
        setBody(vo, deviceMap, pvComponentMap, pvStringMap, taggingMap, fileMap, inspectionPictureMap, bigFileMap,
                pvAllFileMap, pvLightFileMap);
    }

    private Map<String, AllcoreFileVO> getDefectFileMap(List<DefectInfoOnlineReportingVO> vo, String taskId) {
        List<String> lightDefectKey = vo.stream().map(m -> m.getLightDefectKey()).collect(Collectors.toList());
        // 可见光缺陷
        List<InspectionPictureTagging> entityList = inspectionPictureTaggingMapper.selectList(
                new LambdaQueryWrapper<InspectionPictureTagging>().eq(InspectionPictureTagging::getInspectionTaskId, taskId)
                        .in(CollectionUtil.isNotEmpty(lightDefectKey), InspectionPictureTagging::getDefectKey, lightDefectKey));
        vo.forEach(defectInfo -> {
            entityList.stream().filter(tagging -> tagging.getDefectKey().equals(defectInfo.getLightDefectKey()))
                    .findFirst().ifPresent(tagging -> defectInfo.setLightDefectFileGuid(tagging.getFileGuid()));
        });
        List<String> fileGuid = vo.stream().map(s -> s.getLightDefectFileGuid()).collect(Collectors.toList());
        return getFileMap(fileGuid);
    }

    private static void setBody(List<DefectInfoOnlineReportingVO> vo, Map<String, BasicCommonVO> deviceMap,
                                Map<String, BasicCommonVO> pvComponentMap, Map<String, BasicCommonVO> pvStringMap,
                                Map<String, Map<String, String>> taggingMap, Map<String, AllcoreFileVO> fileMap,
                                Map<String, AllcoreFileVO> inspectionPictureMap, Map<String, AllcoreFileVO> bigFileMap,
                                Map<String, AllcoreFileVO> pvAllFileMap, Map<String, AllcoreFileVO> pvLightFileMap) {
        for (DefectInfoOnlineReportingVO boyd : vo) {
            // 设备名称
            if (ObjectUtil.isNotEmpty(deviceMap) && ObjectUtil.isNotEmpty(deviceMap.get(boyd.getDeviceId()))) {
                String devicename = deviceMap.get(boyd.getDeviceId()).getDeviceName();
                String towerNum = deviceMap.get(boyd.getDeviceId()).getTowerNum();
                String parentDeviceId = deviceMap.get(boyd.getDeviceId()).getParentDeviceId();
                boyd.setDeviceName(StringUtil.isNotBlank(devicename) ? devicename : "-");
                boyd.setTowerNum(StringUtil.isNotBlank(towerNum) ? towerNum : "-");
                boyd.setParentDeviceId(StringUtil.isNotBlank(parentDeviceId) ? parentDeviceId : "");
            }
            // 组件名称
            if (ObjectUtil.isNotEmpty(pvComponentMap)
                    && ObjectUtil.isNotEmpty(pvComponentMap.get(boyd.getPvComponentId()))) {
                BasicCommonVO pvComponentVo = pvComponentMap.get(boyd.getPvComponentId());
                boyd.setPvComponentName(pvComponentVo.getDeviceName());
                boyd.setPvComponentLongitude(pvComponentVo.getLongitude());
                boyd.setPvComponentLatitude(pvComponentVo.getLatitude());
                boyd.setPvComponentCoordinates(pvComponentVo.getCoordinates());
                boyd.setPvStringId(pvComponentVo.getPvStringId());
            }
            // 组串信息
            if (ObjectUtil.isNotEmpty(pvStringMap) && ObjectUtil.isNotEmpty(pvStringMap.get(boyd.getPvStringId()))) {
                BasicCommonVO pvStringVo = pvStringMap.get(boyd.getPvStringId());
                boyd.setPvStringName(pvStringVo.getDeviceName());
                boyd.setPvStringLongitude(pvStringVo.getLongitude());
                boyd.setPvStringLatitude(pvStringVo.getLatitude());
                boyd.setPvStringCoordinates(pvStringVo.getCoordinates());
            }
            // 消缺时间和消缺任务编号
            boyd.setRemoveTime("-");
            if (ObjectUtil.isNotEmpty(taggingMap)) {
                Map<String, String> voMap = taggingMap.get(boyd.getId());
                boyd.setRemoveTime(ObjectUtil.isNotEmpty(voMap) && ObjectUtil.isNotEmpty(voMap.get("removeTime"))
                        ? new SimpleDateFormat("yyyy-MM-dd").format(voMap.get("removeTime")) : "--");
            }
            // 图片路径和名称
            if (ObjectUtil.isNotEmpty(fileMap) && ObjectUtil.isNotEmpty(fileMap.get(boyd.getFileGuid()))) {
                boyd.setFilePath(fileMap.get(boyd.getFileGuid()).getDynamicPath());
                boyd.setFileName(fileMap.get(boyd.getFileGuid()).getOriginalName());
            }
            if (ObjectUtil.isNotEmpty(inspectionPictureMap)
                    && ObjectUtil.isNotEmpty(inspectionPictureMap.get(boyd.getInspectionPictureGuid()))) {
                boyd.setInspectionPictureFilePath(
                        inspectionPictureMap.get(boyd.getInspectionPictureGuid()).getDynamicPath());
                boyd.setInspectionPictureFileName(
                        inspectionPictureMap.get(boyd.getInspectionPictureGuid()).getOriginalName());
                boyd.setLongitude(inspectionPictureMap.get(boyd.getInspectionPictureGuid()).getLongitude());
                boyd.setLatitude(inspectionPictureMap.get(boyd.getInspectionPictureGuid()).getLatitude());
            }
            if (ObjectUtil.isNotEmpty(bigFileMap) && ObjectUtil.isNotEmpty(bigFileMap.get(boyd.getBigFileGuid()))) {
                boyd.setBigFilePath(bigFileMap.get(boyd.getBigFileGuid()).getDynamicPath());
            }
            String inspectionPictureFileName = boyd.getInspectionPictureFileName();
            if (ObjectUtil.isNotEmpty(pvAllFileMap) && StringUtil.isNotBlank(inspectionPictureFileName)
                    && inspectionPictureFileName.contains("_T")) {
                String lightFileName = CommonUtil.filterDjTime(inspectionPictureFileName.replace("_T.", "_Z."));
                AllcoreFileVO lightPic = pvAllFileMap.get(lightFileName);
                // 兼容_Z;_W
                if (ObjectUtil.isEmpty(lightPic)) {
                    lightFileName = CommonUtil.filterDjTime(inspectionPictureFileName.replace("_T.", "_W."));
                    lightPic = pvAllFileMap.get(lightFileName);
                }
                boyd.setLightPictureGuid(ObjectUtil.isNotEmpty(lightPic) ? lightPic.getFileGuid() : "");
                boyd.setLightPictureFileName(ObjectUtil.isNotEmpty(lightPic) ? lightPic.getOriginalName() : "");
                boyd.setLightPictureFilePath(ObjectUtil.isNotEmpty(lightPic) ? lightPic.getDynamicThumbPath() : "");
            }
            String lightDefectFileGuid = boyd.getLightDefectFileGuid();
            if (ObjectUtil.isNotEmpty(pvLightFileMap) && StringUtil.isNotBlank(lightDefectFileGuid)) {
                AllcoreFileVO lightDefectPic = pvLightFileMap.get(lightDefectFileGuid);
                boyd.setLightDefectPicPath(
                        ObjectUtil.isNotEmpty(lightDefectPic) ? lightDefectPic.getDynamicPath() : "");

            }
        }
    }

    private Map<String, BasicCommonVO> getPvStringMap(Map<String, BasicCommonVO> pvComponentMap) {
        Map<String, BasicCommonVO> pvStringMap = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(pvComponentMap)) {
            List<String> pvStringIdList =
                    pvComponentMap.values().stream().map(BasicCommonVO::getPvStringId).collect(Collectors.toList());
            pvStringMap = commonService.getDeviceByDeviceIds(pvStringIdList, PV_STRING);
        }
        return pvStringMap;
    }

    private void setInspectionPictureGuid(List<DefectInfoOnlineReportingVO> vo) {
        // inspectionPictureIds
        List<String> inspectionPictureIds =
                vo.stream().map(m -> m.getInspectionPictureId()).collect(Collectors.toList());
        Map<String, InspectionPicture> inspectionPicturemap = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(inspectionPictureIds)) {
            List<InspectionPicture> inspectionPicture =
                    inspectionPictureMapper.selectList(new LambdaQueryWrapper<InspectionPicture>()
                            .in(ObjectUtil.isNotEmpty(inspectionPictureIds), InspectionPicture::getId, inspectionPictureIds));
            inspectionPicturemap =
                    inspectionPicture.stream().collect(Collectors.toMap(InspectionPicture::getId, Function.identity()));
            for (DefectInfoOnlineReportingVO d : vo) {
                InspectionPicture inspectionPic = inspectionPicturemap.get(d.getInspectionPictureId());
                d.setInspectionPictureGuid(inspectionPic.getFileGuid());
                d.setBigPosition(inspectionPic.getFanBigPosition());
                d.setBladeOrder(inspectionPic.getFanBladeOrder());
                d.setDetailPosition(inspectionPic.getFanDetailPosition());

            }
        }
    }

    private Map<String, BasicCommonVO> getPvComponentMap(String deviceType, List<DefectInfoOnlineReportingVO> vo) {
        Map<String, BasicCommonVO> pvComponentMap = Maps.newHashMap();
        if (StringUtil.equals(PV, deviceType) || StringUtil.equals(PV_AREA, deviceType)) {
            // 光伏才有组件信息
            List<String> pvComponentIds = vo.stream().map(m -> m.getPvComponentId()).collect(Collectors.toList());
            // 组件设备信息处理成map
            pvComponentMap = commonService.getDeviceByDeviceIds(pvComponentIds, PV_COMPONENTS);
        }
        return pvComponentMap;
    }

    private Map<String, Map<String, String>> getTaggingMap(List<DefectInfoOnlineReportingVO> filteredList) {
        if (CollectionUtil.isEmpty(filteredList)) {
            return Maps.newHashMap();
        }
        // 已消缺picids
        List<String> picTaggingIds = filteredList.stream().map(m -> m.getId()).collect(Collectors.toList());
        List<Map<String, String>> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(picTaggingIds)) {
            list = baseMapper.selectRemoveNameMap(picTaggingIds);
        }

        Map<String, Map<String, String>> taggingMap = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(list)) {
            // key--taggingID 消缺映射关系
            taggingMap = list.stream().filter(map -> map.containsKey("inspectionPictureTaggingId"))
                    .collect(Collectors.toMap(map -> map.get("inspectionPictureTaggingId"), map -> map));
        }
        return taggingMap;
    }

    private Map<String, AllcoreFileVO> getFileMap(List<String> fileGuids) {
        Map<String, AllcoreFileVO> fileMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(fileGuids)) {
            // 文件信息处理成map
            R<List<AllcoreFileVO>> fileR = ossClient.getFilesDetail(fileGuids);
            if (fileR.isSuccess()) {
                // 放进map方便获取
                fileMap =
                        fileR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
            }
        }

        return fileMap;
    }

    private Map<String, AllcoreFileVO> getFileMapKeyName(List<String> fileGuids) {
        Map<String, AllcoreFileVO> fileMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(fileGuids)) {
            // 文件信息处理成map
            R<List<AllcoreFileVO>> fileR = ossClient.getFilesDetail(fileGuids);
            if (fileR.isSuccess()) {
                // 放进map方便获取
                fileMap = fileR.getData().stream().collect(
                        Collectors.toMap(file -> CommonUtil.filterDjTime(file.getOriginalName()), Function.identity()));
            }
        }

        return fileMap;
    }

    @Override
    public InspectionDefectVO defectDetail(String inspectionPictureTaggingId) {
        InspectionPictureTagging tagging = getById(inspectionPictureTaggingId);
        RemovePictureTagging pictureTagging =
                removePictureTaggingService.getOne(new LambdaQueryWrapper<RemovePictureTagging>()
                        .eq(RemovePictureTagging::getInspectionPictureTaggingId, tagging.getId()));

        InspectionTask entity = inspectionTaskMapper.selectById(tagging.getInspectionTaskId());

        PvComponents components = pvComponentsService.getById(tagging.getPvComponentId());

        InspectionDefectVO rst = buildInspectionDefectForApp(entity, components, tagging, pictureTagging);

        return rst;
    }

    private void setFileData(List<InspectionPictureVO> picturesVO) {

        List<String> fileGuids = picturesVO.stream().map(InspectionPictureVO::getFileGuid).collect(Collectors.toList());

        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
        if (rstR.isSuccess()) {
            picturesVO.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setFilePath(r.getStaticPath());
                        e.setOriginalName(r.getOriginalName());
                    }
                });
            });
        }
    }

    private void setGroupId(List<InspectionPictureVO> picturesVO) {
        // 分组
        for (InspectionPictureVO pic1 : picturesVO) {
            if (StringUtil.isBlank((pic1.getGroupId()))) {
                for (InspectionPictureVO pic2 : picturesVO) {
                    // 不和自己比较
                    if (pic1.getId().equals(pic2.getId())) {
                        continue;
                    }
                    // 根据名称判断是否是同一组
                    if (CommonUtil.isGroup(pic1.getOriginalName(), pic2.getOriginalName())) {
                        // 判断是否是有组id，没有则加上，有就沿用
                        if (StringUtil.isBlank(pic2.getGroupId())) {
                            pic2.setGroupId(CommonUtil.generateUuid());
                        }
                        pic1.setGroupId(pic2.getGroupId());
                    }
                }
            }

        }
    }

    @Override
    public DefectHistoryDetailVO historyDetail(String inspectionPictureTaggingId, String inspectionTaskId) {
        DefectHistoryDetailVO vo = new DefectHistoryDetailVO();
        InspectionPictureTagging tagging = baseMapper.selectById(inspectionPictureTaggingId);
        InspectionTask entity = inspectionTaskMapper.selectById(tagging.getInspectionTaskId());
        PvComponents components = pvComponentsService.getById(tagging.getPvComponentId());

        RemovePictureTagging pictureTagging =
                removePictureTaggingService.getOne(new LambdaQueryWrapper<RemovePictureTagging>()
                        .eq(RemovePictureTagging::getInspectionPictureTaggingId, tagging.getId()));

        InspectionDefectVO rst = buildInspectionDefect(entity, components, tagging, pictureTagging);
        vo.setInspectionDefectVO(rst);

        RemoveTaskVO removeTaskVO = buildRemoveTask(tagging, pictureTagging);
        vo.setRemoveTaskVO(removeTaskVO);
        return vo;
    }

    private RemoveTaskVO buildRemoveTask(InspectionPictureTagging tagging, RemovePictureTagging pictureTagging) {
        RemoveTaskVO removeTaskVO = new RemoveTaskVO();
        removeTaskVO.setRemoveTime(pictureTagging.getRemoveTime());
        // 缺陷等级
        InspectionPictureTaggingVO pictureTaggingVO =
                inspectionPictureTaggingMapper.getPvRemoveTaskDetail(tagging.getId());
        removeTaskVO.setDefectLevel(pictureTaggingVO.getDefectLevel());
        removeTaskVO.setDefectLevelZh(
                DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), pictureTaggingVO.getDefectLevel()));
        removeTaskVO.setRemoveTime(pictureTagging.getRemoveTime());
        // 消缺详情:实际原因和备注
        RemovePictureTaggingDetail detail =
                removePictureTaggingDetailService.getOne(new LambdaQueryWrapper<RemovePictureTaggingDetail>()
                        .eq(RemovePictureTaggingDetail::getInspectionPictureTaggingId, tagging.getId()));
        if (Func.isNotEmpty(detail)) {
            removeTaskVO.setRealTagReason(detail.getRealTagReason());
            removeTaskVO.setRealTagReasonZh(CommonUtil.transDefectCode(detail.getRealTagReason(), PV));
            removeTaskVO.setRemark(detail.getRemark());
        }
        // 消缺详情:文件
        List<RemovePictureTaggingFile> files =
                removePictureTaggingFileService.list(new LambdaQueryWrapper<RemovePictureTaggingFile>()
                        .eq(RemovePictureTaggingFile::getInspectionPictureTaggingId, tagging.getId()));
        List<RemovePictureTaggingFileVO> fileVOList = new ArrayList<>();
        R<List<AllcoreFileVO>> rstListR = ossClient
                .getFilesDetail(files.stream().map(RemovePictureTaggingFile::getFileGuid).collect(Collectors.toList()));
        if (rstListR.isSuccess()) {
            files.forEach(e -> {
                rstListR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        RemovePictureTaggingFileVO fileVO = new RemovePictureTaggingFileVO();
                        fileVO.setRemoveTaggingFileType(e.getRemoveTaggingFileType());
                        fileVO.setFileGuid(e.getFileGuid());
                        fileVO.setFilePath(r.getStaticPath());
                        fileVOList.add(fileVO);
                    }
                });
            });
        }
        removeTaskVO.setZFileVOS(
                fileVOList.stream().filter(e -> e.getRemoveTaggingFileType().contains("_z")).collect(Collectors.toList()));
        removeTaskVO.setTFileVOS(
                fileVOList.stream().filter(e -> e.getRemoveTaggingFileType().contains("_t")).collect(Collectors.toList()));
        return removeTaskVO;
    }
    private InspectionDefectVO buildInspectionDefectForApp(InspectionTask entity, PvComponents components,
                                                     InspectionPictureTagging tagging, RemovePictureTagging pictureTagging) {

        InspectionDefectVO rst = new InspectionDefectVO();
        rst.setStartToEndTime(entity.getStartDate());
        rst.setInspectionTaskNo(entity.getInspectionTaskNo());
        rst.setInspectionTaskId(entity.getId());
        if (Func.isNotEmpty(components)) {
            rst.setDeviceName(components.getDeviceName());
            rst.setLongitude(Double.valueOf(components.getLongitude()));
            rst.setLatitude(Double.valueOf(components.getLatitude()));
        }
        rst.setDefectDescription(tagging.getDefectDescription());
        rst.setDefectDescriptionZh(CommonUtil.transDefectCode(tagging.getDefectDescription(), PV));
        rst.setDefectLevel(tagging.getDefectLevel());
        rst.setDefectLevelZh(DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), tagging.getDefectLevel()));
        rst.setTemperature(tagging.getTemperature());
        rst.setRemoveTaskNo(pictureTagging.getRemoveTaskNo());
        String tFilePath = "", zFilePath = "";
        // 该缺陷id对应的红外大图
        R<AllcoreFileVO> rstR = ossClient.getFileDetail(tagging.getBigFileGuid());
        if (rstR.isSuccess()) {
            tFilePath = rstR.getData().getStaticPath();
        }
        // 可见
        if(StringUtil.equals("yes",tagging.getIsZ())){
            rst.setZFilePath(tFilePath);
        } else {
            rst.setTFilePath(tFilePath);
            // 找对应可见光
            try {
                InspectionPictureTagging lightPictureTagging = getOne(new LambdaQueryWrapper<InspectionPictureTagging>()
                        .eq(InspectionPictureTagging::getDefectKey, tagging.getLightDefectKey()));
                if (Func.isNotEmpty(lightPictureTagging)) {
                    rstR = ossClient.getFileDetail(lightPictureTagging.getBigFileGuid());
                    if (rstR.isSuccess()) {
                        zFilePath = rstR.getData().getStaticPath();
                    }
                    // 可见光
                    rst.setZFilePath(zFilePath);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return rst;
    }
    private InspectionDefectVO buildInspectionDefect(InspectionTask entity, PvComponents components,
                                                     InspectionPictureTagging tagging, RemovePictureTagging pictureTagging) {

        InspectionDefectVO rst = new InspectionDefectVO();
        rst.setStartToEndTime(entity.getStartDate());
        rst.setInspectionTaskNo(entity.getInspectionTaskNo());
        rst.setInspectionTaskId(entity.getId());
        if (Func.isNotEmpty(components)) {
            rst.setDeviceName(components.getDeviceName());
            rst.setLongitude(Double.valueOf(components.getLongitude()));
            rst.setLatitude(Double.valueOf(components.getLatitude()));
        }
        rst.setDefectDescription(tagging.getDefectDescription());
        rst.setDefectDescriptionZh(CommonUtil.transDefectCode(tagging.getDefectDescription(), PV));
        rst.setDefectLevel(tagging.getDefectLevel());
        rst.setDefectLevelZh(DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), tagging.getDefectLevel()));
        rst.setTemperature(tagging.getTemperature());
        rst.setRemoveTaskNo(pictureTagging.getRemoveTaskNo());
        String tFilePath = "", zFilePath = "";
        // 该缺陷id对应的红外大图
        R<AllcoreFileVO> rstR = ossClient.getFileDetail(tagging.getBigFileGuid());
        if (rstR.isSuccess()) {
            tFilePath = rstR.getData().getStaticPath();
        }
        // 可见
        if(StringUtil.equals("yes",tagging.getIsZ())){
            rst.setZFilePath(tFilePath);
        } else {
            rst.setTFilePath(tFilePath);
            // 找对应可见光
            try {
                InspectionPictureTagging lightPictureTagging = getOne(new LambdaQueryWrapper<InspectionPictureTagging>()
                        .eq(InspectionPictureTagging::getDefectKey, tagging.getLightDefectKey()));
                if (Func.isNotEmpty(lightPictureTagging)) {
                    rstR = ossClient.getFileDetail(lightPictureTagging.getBigFileGuid());
                    if (rstR.isSuccess()) {
                        zFilePath = rstR.getData().getStaticPath();
                    }
                    // 可见光
                    rst.setZFilePath(zFilePath);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        return rst;
    }

    @Override
    public String stationFanInfo(String deviceType) {

        return inspectionPictureTaggingMapper.getLastModelPath(deviceType);

    }

    @Override
    public R fanDefectBeforeInfo(InspectionTaskPicQueryDTO dto) {
        if (!StringUtil.equals(dto.getDeviceType(), FAN)) {
            return R.fail("类型错误");
        }
        // 全量巡检图
        List<InspectionPicture> picList = inspectionPictureMapper.selectList(Wrappers.<InspectionPicture>lambdaQuery()
                .eq(InspectionPicture::getInspectionTaskId, dto.getInspectionTaskId())
                .isNotNull(InspectionPicture::getDeviceId));
        // 全量缺陷数据
        List<InspectionPictureTagging> picTaggingList =
                inspectionPictureTaggingMapper.selectList(Wrappers.<InspectionPictureTagging>lambdaQuery()
                        .eq(InspectionPictureTagging::getInspectionTaskId, dto.getInspectionTaskId())
                        .isNotNull(InspectionPictureTagging::getDeviceId));

        List<FanBeforeDefectVO> list = getFanBeforeDefectVO(picList, picTaggingList);

        return R.data(list);
    }

    @Override
    public List<InspectionTaggingVO> defectDetailInfoByDeviceId(InspectionTaggingQueryDTO dto) {
        List<InspectionPictureTagging> list = list(new LambdaQueryWrapper<InspectionPictureTagging>()
                .eq(InspectionPictureTagging::getDeviceId, dto.getDeviceId())
                .eq(InspectionPictureTagging::getDeviceType, dto.getDeviceType())
                .ne(InspectionPictureTagging::getEliminateStatus, TAGGING_TYPE_ALREADY_VANISH_DEFECT.getCode())
                .orderByDesc(InspectionPictureTagging::getCreateTime)
                .groupBy(InspectionPictureTagging::getPvComponentId)
        );
        if (CollectionUtil.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        List<String> componentsIds = new ArrayList<>();
        List<String> deviceIds = new ArrayList<>();
        List<String> groupIds = new ArrayList<>();
        list.forEach(item -> {
            componentsIds.add(item.getPvComponentId());
            deviceIds.add(item.getDeviceId());
            groupIds.add(item.getGroupId());
        });
        List<InspectionPicture> pictures = inspectionPictureMapper.selectList(new LambdaQueryWrapper<InspectionPicture>()
                .in(InspectionPicture::getGroupId, groupIds)
                .select(InspectionPicture::getGroupId, InspectionPicture::getFileGuid)
        );
        List<String> fileIds = pictures.stream().map(InspectionPicture::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> filesDetailR = ossClient.getFilesDetail(fileIds);
        if (!filesDetailR.isSuccess()) {
            throw new ServiceException("获取文件失败: " + filesDetailR.getMsg());
        }
        List<AllcoreFileVO> fileList = filesDetailR.getData();
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, dto.getDeviceType());
        List<BasicCommonVO> componentsDeviceList = commonService.getCommonDeviceByIds(componentsIds, BizDictEnum.DEVICE_TYPE_PV_COMPONENTS.getCode(), AuthUtil.getDeptCode());
        return list.stream().map(item -> {
            InspectionTaggingVO vo = BeanUtil.copy(item, InspectionTaggingVO.class);
            vo.setDefectDescriptionZh(CommonUtil.transDefectCode(vo.getDefectDescription(), vo.getDeviceType()));
            // 缺陷等级
            String defectLevelZh = DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), vo.getDefectLevel());
            vo.setDefectLevelZh(StringUtil.isBlank(defectLevelZh) ? "" : defectLevelZh);
            if (deviceMap.containsKey(vo.getDeviceId())) {
                vo.setDeviceName(deviceMap.get(vo.getDeviceId()).getDeviceName());
            }
            pictures.stream().filter(e -> e.getGroupId().equals(vo.getGroupId())).forEach(e -> {
                String fileGuid = e.getFileGuid();
                fileList.stream().filter(file -> file.getFileGuid().equals(fileGuid)).findFirst().ifPresent(file -> {
                    if (StringUtils.equals(CommonUtil.getPicType(file.getOriginalName()), "_T")) {
                        vo.setInfraredFilePath(file.getName());
                    } else {
                        vo.setVisibleLightFilePath(file.getName());
                    }
                });
            });
            componentsDeviceList.stream().filter(
                    components -> components.getDeviceId().equals(vo.getPvComponentId())
            ).findFirst().ifPresent(components -> {
                vo.setLongitude(components.getLongitude());
                vo.setLatitude(components.getLatitude());
                vo.setCoordinates(components.getCoordinates());
            });
            return vo;
        }).collect(Collectors.toList());
    }

    private List<FanBeforeDefectVO> getFanBeforeDefectVO(List<InspectionPicture> picList,
                                                         List<InspectionPictureTagging> picTaggingList) {
        List<FanBeforeDefectVO> returnVo = new ArrayList<>();
        // 按照设备分类的巡检图
        Map<String, List<InspectionPicture>> picMap =
                picList.stream().collect(Collectors.groupingBy(InspectionPicture::getDeviceId));
        // 设备ids
        List<String> deviceIds = picList.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());
        // 设备信息处理成map
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, FAN);
        picMap.forEach((key, value) -> {
            FanBeforeDefectVO vo = new FanBeforeDefectVO();
            vo.setDeviceId(key);
            vo.setDeviceName(deviceMap.get(key).getDeviceName());
            // 解析数据
            List<InspectionPicture> aBlade = new ArrayList();
            List<InspectionPicture> bBlade = new ArrayList();
            List<InspectionPicture> cBlade = new ArrayList();
            List<InspectionPicture> tower = new ArrayList();
            List<InspectionPicture> other = new ArrayList();
            picMap.get(key).stream().forEach(e -> {
                String kind = e.getFanBladeOrder();
                String type = e.getFanBigPosition();
                if (StringUtil.equals(type, "blade") && kind.contains("A")) {
                    aBlade.add(e);
                } else if (StringUtil.equals(type, "blade") && kind.contains("B")) {
                    bBlade.add(e);
                } else if (StringUtil.equals(type, "blade") && kind.contains("C")) {
                    cBlade.add(e);
                } else if (StringUtil.equals(type, "tower")) {
                    tower.add(e);
                } else {
                    other.add(e);
                }
            });
            List<String> aBladePicId = aBlade.stream().map(m -> m.getId()).collect(Collectors.toList());
            List<String> bBladePicId = bBlade.stream().map(m -> m.getId()).collect(Collectors.toList());
            List<String> cBladePicId = cBlade.stream().map(m -> m.getId()).collect(Collectors.toList());
            List<String> towerPicId = tower.stream().map(m -> m.getId()).collect(Collectors.toList());
            List<String> otherPicId = other.stream().map(m -> m.getId()).collect(Collectors.toList());
            int aBladeDefectNum = getCount(picTaggingList, aBladePicId);
            int bBladeDefectNum = getCount(picTaggingList, bBladePicId);
            int cBladeDefectNum = getCount(picTaggingList, cBladePicId);
            int towerDefectNum = getCount(picTaggingList, towerPicId);
            int otherDefectNum = getCount(picTaggingList, otherPicId);

            vo.setABladePicNum(aBlade.size());
            vo.setABladeDefectNum(aBladeDefectNum);
            vo.setBBladePicNum(bBlade.size());
            vo.setBBladeDefectNum(bBladeDefectNum);
            vo.setCBladePicNum(cBlade.size());
            vo.setCBladeDefectNum(cBladeDefectNum);

            vo.setTowerPicNum(tower.size());
            vo.setTowerDefectNum(towerDefectNum);

            vo.setOtherPicNum(other.size());
            vo.setOtherDefectNum(otherDefectNum);

            vo.setAllPicNum(vo.getTowerPicNum() + vo.getNacellePicNum() + vo.getABladePicNum() + vo.getBBladePicNum()
                    + vo.getCBladePicNum());
            vo.setAllDefectNum(vo.getCBladeDefectNum() + vo.getBBladeDefectNum() + vo.getABladeDefectNum()
                    + vo.getNacelleDefectNum() + vo.getTowerDefectNum());

            returnVo.add(vo);
        });
        Collections.sort(returnVo);
        return returnVo;
    }

    private static int getCount(List<InspectionPictureTagging> picTaggingList, List<String> aBladePicId) {
        return (int) picTaggingList.stream()
                .filter(l -> CollectionUtil.isNotEmpty(aBladePicId) && aBladePicId.contains(l.getInspectionPictureId()))
                .count();
    }

}
