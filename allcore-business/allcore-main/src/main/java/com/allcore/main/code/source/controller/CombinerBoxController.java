package com.allcore.main.code.source.controller;

import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.source.dto.CombinerBoxDTO;
import com.allcore.main.code.source.service.ICombinerBoxService;
import com.allcore.main.code.source.vo.CombinerBoxVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @program: bl
 * @description: 汇流箱接口类
 * @author: fanxiang
 * @create: 2025-05-26 11:20
 **/


@RestController
@Api(value="汇流箱接口",tags = "汇流箱管理")
@RequestMapping("/combinerBox")
@AllArgsConstructor
public class CombinerBoxController {

    private final ICombinerBoxService combinerBoxService;

    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value="新增",notes = "传入CombinerDTO")
    public R save(@Valid @RequestBody CombinerBoxDTO combinerBoxDTO){
        return R.status(combinerBoxService.save(combinerBoxDTO));
    }

    @GetMapping("/page")
    @ApiOperationSupport(order=2)
    @ApiOperation(value="分页查询",notes="")
    public R<IPage<CombinerBoxVO>>page(CombinerBoxDTO combinerBoxDTO,  Query query){
        IPage<CombinerBoxVO> pages=combinerBoxService.selectCombinerBoxPage(combinerBoxDTO, Condition.getPage(query));
        return R.data(pages);
    }

    @PostMapping("/update")
    @ApiOperationSupport(order=3)
    @ApiOperation(value="修改",notes="传入CombinerDTO")
    public R update(@Valid @RequestBody CombinerBoxDTO combinerBoxDTO){
        return R.status(combinerBoxService.updateById(combinerBoxDTO));
    }

    @PostMapping("/remove")
    @ApiOperationSupport(order=4)
    @ApiOperation(value="删除",notes="传入ids")
    public R remove(@Valid @RequestBody IdsDTO dto){
        return combinerBoxService.deleteCombinerBox(Func.toStrList(dto.getIds()));
    }
}
