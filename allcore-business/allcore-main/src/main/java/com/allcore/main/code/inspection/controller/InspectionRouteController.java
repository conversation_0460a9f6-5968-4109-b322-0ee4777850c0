package com.allcore.main.code.inspection.controller;

import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.inspection.dto.InspectionRouteDTO;
import com.allcore.main.code.inspection.dto.InspectionRouteSaveDTO;
import com.allcore.main.code.inspection.service.IInspectionRouteService;
import com.allcore.main.code.inspection.vo.InspectionRouteVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @program: bl
 * @description: 巡检路线接口类
 * @author: fanxiang
 * @create: 2025-06-05 20:06
 **/

@RestController
@RequestMapping("/inspectionRoute")
@AllArgsConstructor
@Api(value="巡检路线管理",tags="巡检路线管理")
public class InspectionRouteController {

    private final IInspectionRouteService inspectionRouteService;


    @PostMapping("/save")
    @ApiOperationSupport(order=1)
    @ApiOperation(value = "新增")
    public R save(@Valid @RequestBody InspectionRouteSaveDTO dto){
        return inspectionRouteService.saveInspectionRoute(dto);
    }

    @GetMapping("/page")
    @ApiOperationSupport(order=2)
    @ApiOperation(value="分页查询",notes = "传入查询条件")
    public R<IPage<InspectionRouteVO>> page(InspectionRouteDTO dto, Query query){
        return inspectionRouteService.selectInspectionRoutePage(Condition.getPage(query), dto);
    }

    @PostMapping("/update")
    @ApiOperationSupport(order=3)
    @ApiOperation(value = "修改")
    public R update(@Valid @RequestBody InspectionRouteSaveDTO dto){
        return inspectionRouteService.updateInspectionRoute(dto);
    }

    @PostMapping("/remove")
    @ApiOperationSupport(order=4)
    @ApiOperation(value = "删除",notes="传入路线id")
    public R remove(@Valid @RequestBody IdsDTO dto){
        return inspectionRouteService.deleteInspectionRoute(Func.toStrList(dto.getIds()));
    }


}
