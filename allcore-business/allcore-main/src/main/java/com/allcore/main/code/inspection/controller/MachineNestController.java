package com.allcore.main.code.inspection.controller;

import com.alibaba.fastjson.JSONObject;
import com.allcore.core.tool.api.R;
import com.allcore.external.dto.BackStatusDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.external.dto.TaskCallBackDTO;
import com.allcore.main.code.inspection.service.IInspectionPictureService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/machineNest")
@Api(value = "远程巡检图片", tags = "巡检接口")
public class MachineNestController {


    @Resource
    private  IInspectionPictureService inspectionPictureService;

    @PostMapping("/newUploadReceivePic")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "远程巡检-无人机拍摄图片批量上传", notes = "")
    public R newUploadReceivePic(@RequestParam("files") MultipartFile[] files,
                                 @RequestParam(value = "paramData") String paramData,
                                 @RequestHeader MultiValueMap<String, String> headers) {
        String hcJcAcsId = headers.get("HC-JC-ACCESSKEYID")==null?headers.get("hc-jc-accesskeyid").get(0):headers.get("HC-JC-ACCESSKEYID").get(0);
        String hcJcAcsSecret = headers.get("HC-JC-ACCESSKEYSECRET")==null?headers.get("hc-jc-accesskeysecret").get(0):headers.get("HC-JC-ACCESSKEYSECRET").get(0);
        paramData = StringEscapeUtils.unescapeHtml4(paramData);
        ReceivePicNewDTO dto = JSONObject.parseObject(paramData,ReceivePicNewDTO.class);

        return inspectionPictureService.newUploadReceivePic(files,dto,hcJcAcsId,hcJcAcsSecret);
    }

    @PostMapping("/backStatus")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "任务执行状态回传", notes = "任务执行状态回传")
    public R backStatus(@RequestBody BackStatusDTO dto,
                        @RequestHeader MultiValueMap<String, String> headers) {
        String hcJcAcsId = headers.get("HC-JC-ACCESSKEYID")==null?headers.get("hc-jc-accesskeyid").get(0):headers.get("HC-JC-ACCESSKEYID").get(0);
        String hcJcAcsSecret = headers.get("HC-JC-ACCESSKEYSECRET")==null?headers.get("hc-jc-accesskeysecret").get(0):headers.get("HC-JC-ACCESSKEYSECRET").get(0);
        return inspectionPictureService.backStatus(dto,hcJcAcsId,hcJcAcsSecret);
    }

    @PostMapping("/taskEndNotification")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "任务执行状态回传", notes = "任务执行状态回传")
    public R backStatus(@RequestBody TaskCallBackDTO dto,
                        @RequestHeader MultiValueMap<String, String> headers) {
        String hcJcAcsId = headers.get("HC-JC-ACCESSKEYID")==null?headers.get("hc-jc-accesskeyid").get(0):headers.get("HC-JC-ACCESSKEYID").get(0);
        String hcJcAcsSecret = headers.get("HC-JC-ACCESSKEYSECRET")==null?headers.get("hc-jc-accesskeysecret").get(0):headers.get("HC-JC-ACCESSKEYSECRET").get(0);
        return inspectionPictureService.taskEndNotification(dto,hcJcAcsId,hcJcAcsSecret);
    }
}
