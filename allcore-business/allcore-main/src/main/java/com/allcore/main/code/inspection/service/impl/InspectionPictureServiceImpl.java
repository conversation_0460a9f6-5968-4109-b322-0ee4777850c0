package com.allcore.main.code.inspection.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.MessageConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.*;
import com.allcore.external.dto.AirportUploadTaskPicDTO;
import com.allcore.external.dto.BackStatusDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.external.dto.TaskCallBackDTO;
import com.allcore.external.entity.ReceivePicDetail;
import com.allcore.external.feign.IAirportClient;
import com.allcore.external.feign.IAirportTaskClient;
import com.allcore.external.feign.INestClient;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.defect.entity.RecognitionTask;
import com.allcore.main.code.defect.entity.RecognitionTaskAlgorithm;
import com.allcore.main.code.defect.mapper.RecognitionTaskMapper;
import com.allcore.main.code.defect.service.IRecognitionTaskAlgorithmService;
import com.allcore.main.code.inspection.dto.*;
import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.entity.InspectionPicture;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.mapper.*;
import com.allcore.main.code.inspection.props.MainProperties;
import com.allcore.main.code.inspection.service.IInspectionPictureService;
import com.allcore.main.code.inspection.vo.ImagesFileVO;
import com.allcore.main.code.inspection.vo.ImagesVO;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.allcore.main.code.inspection.vo.PatrolDataImageVO;
import com.allcore.main.code.inspection.wrapper.InspectionPictureWrapper;
import com.allcore.main.code.source.entity.Fan;
import com.allcore.main.code.source.mapper.FanMapper;
import com.allcore.main.code.system.entity.Strategy;
import com.allcore.main.code.system.service.IAlgorithmManufacturersService;
import com.allcore.main.code.system.service.IStrategyService;
import com.allcore.main.constant.MainConstant;
import com.allcore.main.utils.InspectionPictureComparator;
import com.allcore.main.utils.InspectionPictureImageComparator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.allcore.common.constant.BasicConstant.*;
import static com.allcore.main.constant.MainConstant.NO_DEVICE;
import static com.allcore.main.constant.MainConstant.TWO;
import static java.util.stream.Collectors.toList;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Service
@AllArgsConstructor
@Slf4j
public class InspectionPictureServiceImpl extends ZxhcServiceImpl<InspectionPictureMapper, InspectionPicture>
        implements IInspectionPictureService {
    private final RecognitionTaskMapper recognitionTaskMapper;
    private final IStrategyService strategyService;
    private final IAlgorithmManufacturersService algorithmManufacturersService;
    private final IRecognitionTaskAlgorithmService recognitionTaskAlgorithmService;
    private final InspectionPictureMapper inspectionPictureMapper;
    private final InspectionDeviceDetailMapper inspectionDeviceDetailMapper;
    private final IOssClient ossClient;
    private final CommonService commonService;
    private final InspectionTaskMapper inspectionTaskMapper;
    private final InspectionDeviceTypeMapper inspectionDeviceTypeMapper;
    private final InspectionPictureTaggingMapper inspectionPictureTaggingMapper;

    private final FanMapper fanMapper;

    private final INestClient nestClient;
    private final IAirportTaskClient airportTaskClient;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveInspectionPicture(List<InspectionPictureSaveDTO> inspectionPicture) {
        List<InspectionPicture> entityList = BeanUtil.copy(inspectionPicture, InspectionPicture.class);
        String taskId = entityList.get(0).getInspectionTaskId();
        //********************************************新需求 - 识别任务在办结后创建 *******************************************************
        // 巡检任务信息
//        InspectionTask taskEntity = inspectionTaskMapper.selectById(taskId);
        // 巡检类型信息
//        InspectionDeviceType type = inspectionDeviceTypeMapper.selectOne(
//                new LambdaQueryWrapper<InspectionDeviceType>().eq(InspectionDeviceType::getInspectionTaskId, taskId));
        // 查消缺任务
//        RecognitionTask recognitionTask = recognitionTaskMapper
//                .selectOne(new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, taskId));
//        if (Func.isEmpty(recognitionTask)) {
//            this.extracted(taskEntity.getInspectionTaskNo(), type.getDeviceType(), recognitionTask, taskId,
//                    taskEntity.getCreateDept(), taskEntity.getCreateUser(), taskEntity.getDeptCode());
//            recognitionTask = recognitionTaskMapper
//                    .selectOne(new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, taskId));
//        }
        //********************************************新需求 - 识别任务在办结后创建 *******************************************************
        for (InspectionPicture entity : entityList) {
            entity.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
            entity.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode());
//            entity.setRecognitionTaskId(ObjectUtil.isNotEmpty(recognitionTask) ? recognitionTask.getId() : "");
        }
        // 提交数据后修改任务状态:2-数据提交中
        this.updateTaskStatus(taskId);

        return R.status(this.saveBatch(entityList, entityList.size()));
    }

    private void updateTaskStatus(String taskId) {

        InspectionTask status = new InspectionTask();
        status.setId(taskId);
        status.setInspectionTaskStatus(TWO);
        this.inspectionTaskMapper.updateById(status);
    }

    @Override
    public R queryUnassociatedData(String taskId) {
        List<InspectionPicture> unassociatedData =
                this.list(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getInspectionTaskId, taskId)
                        .orderByDesc(InspectionPicture::getCreateTime).eq(InspectionPicture::getBindFlag, StringPool.NO));
        if (CollectionUtil.isEmpty(unassociatedData)) {
            return R.data(new InspectionPictureVO());
        }
        return R.data(getPicUrl(BeanUtil.copy(unassociatedData, InspectionPictureVO.class)));
    }

    @Override
    public R queryAssociatedData(PatrolDataImageQueryDTO dto) {
        return R.data(this.getPatrolDataImages(dto));
    }

    @Override
    public R deleteInspectionPicLogic(List<String> strList) {

        // 校验缺陷任务是否正在识别
        Integer cnt = baseMapper.checkRecognitionTaskStatus(strList);
        if (cnt > 0) {
            throw new ServiceException("算法识别中,请稍后再试!");
        }

        //查询当前巡检任务
        InspectionPicture inspectionPicture = inspectionPictureMapper.selectById(strList.get(0));
        InspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionPicture.getInspectionTaskId());

        boolean flag = this.deleteLogic(strList);

        List<InspectionPicture> inspectionPictureList = this.list(Wrappers.<InspectionPicture>lambdaQuery().eq(InspectionPicture::getInspectionTaskId, inspectionTask.getId()));
        if (Func.isEmpty(inspectionPictureList)) {
            inspectionTask.setInspectionTaskStatus(MainConstant.ONE);
            inspectionTaskMapper.updateById(inspectionTask);
        }
        // 删除该图片的缺陷tag记录
        List<InspectionPictureTagging> tagList =
                inspectionPictureTaggingMapper.selectList(new LambdaQueryWrapper<InspectionPictureTagging>()
                        .in(InspectionPictureTagging::getInspectionPictureId, strList));
        if (CollectionUtil.isNotEmpty(tagList)) {
            List<String> tagIds = tagList.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList());
            inspectionPictureTaggingMapper.deleteBatchIds(tagIds);
        }
        return R.status(flag);
    }

    @Override
    public R bindPic(BindPicDTO bindPicDTO) {
        // 查消缺任务
        RecognitionTask recognitionTask = recognitionTaskMapper.selectOne(
                new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, bindPicDTO.getTaskId()));
        if (Objects.nonNull(recognitionTask)) { // --  新流程 办结后才创建识别任务
            // 单文件夹按钮 手动绑定了新图片 识别任务改成未识别 允许继续算法识别
            recognitionTask.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_UNIDENTIFICATION.getCode());
            recognitionTaskMapper.updateById(recognitionTask);
        }

        return R.status(this.update(
                new LambdaUpdateWrapper<InspectionPicture>().eq(InspectionPicture::getFileGuid, bindPicDTO.getFileGuid())
                        .eq(InspectionPicture::getInspectionTaskId, bindPicDTO.getTaskId())
//                        .set(Objects.nonNull(recognitionTask), InspectionPicture::getRecognitionTaskId, recognitionTask.getId())
                        .set(StringUtil.isNotBlank(bindPicDTO.getDeviceId()), InspectionPicture::getBindFlag, StringPool.YES)
                        .set(StringUtil.isNotBlank(bindPicDTO.getDeviceId()), InspectionPicture::getDeviceId,
                                bindPicDTO.getDeviceId())));
    }

    /**
     * 删除图片
     *
     * @param strList
     * @return
     */
    @Override
    public R deletedPic(List<String> strList) {
        List<InspectionPicture> list =
                this.list(new LambdaQueryWrapper<InspectionPicture>().in(InspectionPicture::getFileGuid, strList));
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> ids = list.stream().map(InspectionPicture::getId).collect(Collectors.toList());

            // 校验缺陷任务是否正在识别
            Integer cnt = baseMapper.checkRecognitionTaskStatus(ids);
            if (cnt > 0) {
                throw new ServiceException("算法识别中,请稍后再试!");
            }

            //查询当前巡检任务
            InspectionPicture inspectionPicture = inspectionPictureMapper.selectById(strList.get(0));
            InspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionPicture.getInspectionTaskId());

            this.removeBatchByIds(ids);

            List<InspectionPicture> inspectionPictureList = this.list(Wrappers.<InspectionPicture>lambdaQuery().eq(InspectionPicture::getInspectionTaskId, inspectionTask.getId()));
            if (Func.isEmpty(inspectionPictureList)) {
                inspectionTask.setInspectionTaskStatus(MainConstant.ONE);
                inspectionTaskMapper.updateById(inspectionTask);
            }

            // 删除该图片的缺陷tag记录
            List<InspectionPictureTagging> tagList =
                    inspectionPictureTaggingMapper.selectList(new LambdaQueryWrapper<InspectionPictureTagging>()
                            .in(InspectionPictureTagging::getInspectionPictureId, ids));
            if (CollectionUtil.isNotEmpty(tagList)) {
                List<String> tagIds =
                        tagList.stream().map(InspectionPictureTagging::getId).collect(Collectors.toList());
                inspectionPictureTaggingMapper.deleteBatchIds(tagIds);
            }

        }
        return R.success(MessageConstant.OPERATE_SUCCESS);
    }

    @Override
    public List<PatrolDataImageVO> getPatrolDataImages(PatrolDataImageQueryDTO dto) {
        // 获取设备树
        R<List<BasicCommonVO>> commonDeviceListR = commonService.getCommonDeviceList(
                DeviceCommonDTO.builder().deviceType(dto.getDeviceType()).deptCode(dto.getDeptCode()).child(true).build());
        if (!commonDeviceListR.isSuccess() || CollectionUtils.isEmpty(commonDeviceListR.getData())) {
            return null;
        }
        List<BasicCommonVO> deviceListSource = commonDeviceListR.getData();
        List<BasicCommonVO> deviceList = deviceListSource;
        if (StringUtil.isNotBlank(dto.getDeviceName())) {
            deviceList = deviceListSource.stream().filter(vo -> vo.getDeviceName().contains(dto.getDeviceName()))
                    .collect(Collectors.toList());
        }
        // 设备id集合
        List<String> deviceIds = getDeviceIds(deviceList, dto.getDeviceType());
        String taskId = "";
        if (StringUtils.isNotBlank(dto.getWorkOrderNum())) {
            taskId = inspectionTaskMapper.selectOne(
                            new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getInspectionTaskNo, dto.getWorkOrderNum()))
                    .getId();
        }

        // 根据条件查巡检图片
        List<InspectionPicture> list = this.list(new LambdaQueryWrapper<InspectionPicture>()
                .eq(StringUtils.isNotBlank(dto.getDeviceId()), InspectionPicture::getDeviceId, dto.getDeviceId())
                .eq(StringUtils.isNotBlank(taskId), InspectionPicture::getInspectionTaskId, taskId)
                .eq(Objects.nonNull(dto.getUploadImgTime()), InspectionPicture::getCreateTime, dto.getUploadImgTime())
                .between(Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()),
                        InspectionPicture::getCreateTime, dto.getStartTime(), dto.getEndTime())
                .in(CollectionUtil.isNotEmpty(deviceIds), InspectionPicture::getDeviceId, deviceIds)
                .likeRight(StringUtils.isNotBlank(dto.getDeptCode()), InspectionPicture::getDeptCode, dto.getDeptCode()));
        List<PatrolDataImageVO> resultVo = assembleVO(deviceList, list);
        if (StringUtil.equals(dto.getDeviceType(), PV) && CollectionUtil.isNotEmpty(resultVo)) {
            resultVo.stream().forEach(e -> {
                // 图片分组
                Collections.sort(e.getFileList(), new InspectionPictureImageComparator());
                e.getFileMap().forEach((key, value) -> Collections.sort(value, new InspectionPictureImageComparator()));

            });

        }
        return resultVo;
    }

    @Override
    public R folderUpload(FolderUpLoadDTO dto) {
        if (ObjectUtil.isEmpty(dto)) {
            return R.status(false);
        }
        // 文件夹上传的数据
        List<InspectionPictureSaveDTO> folderUploadList = getUploadInfo(dto);
        List<InspectionPicture> entityList = BeanUtil.copy(folderUploadList, InspectionPicture.class);
        // 提交数据后修改任务状态:2-数据提交中
        this.updateTaskStatus(dto.getInspectionTaskId());
        return R.status(this.saveBatch(entityList));
    }

    @Override
    public R getPicInfo(InspectionTaskPicQueryDTO dto) {
        List<InspectionPicture> entity = this.list(new LambdaQueryWrapper<InspectionPicture>()
                .eq(InspectionPicture::getInspectionTaskId, dto.getInspectionTaskId()));
        List<InspectionPictureVO> vo = BeanUtil.copy(entity, InspectionPictureVO.class);
        if (CollectionUtil.isNotEmpty(vo)) {
            setPicProperty(vo, dto);
        }

        return R.data(InspectionPictureWrapper.build().listVoByVo(vo));
    }

    @Override
    public List<List<String>> getAppPicInfo(String inspectionTaskId, String deviceId) {
        List<InspectionPicture> entity = this.list(new LambdaQueryWrapper<InspectionPicture>()
                .eq(InspectionPicture::getInspectionTaskId, inspectionTaskId)
                .eq(InspectionPicture::getDeviceId,deviceId));
        List<InspectionPictureVO> vo = BeanUtil.copy(entity, InspectionPictureVO.class);
        if (CollectionUtil.isNotEmpty(vo)) {
            InspectionTaskPicQueryDTO dto  = new InspectionTaskPicQueryDTO();
            dto.setDeviceType("PV");
            setPicProperty(vo, dto);
        }

        return this.processInspectionPictures(vo);
    }
    public String removeBetweenNthUnderscores(String filename, int n) {
        if (filename == null || n <= 0) {
            return filename;
        }

        int start = 0;
        int firstUnderscore = -1;
        int secondUnderscore = -1;

        // 找到第n个和第n+1个下划线
        for (int i = 0; i <= n; i++) {
            start = (i == 0) ? 0 : secondUnderscore + 1;
            firstUnderscore = secondUnderscore;
            secondUnderscore = filename.indexOf('_', start);
            if (secondUnderscore == -1) {
                return filename; // 下划线不足n+1个
            }
        }

        return filename.substring(0, firstUnderscore + 1) + filename.substring(secondUnderscore);
    }
    private void setGroupIdForApp(List<InspectionPictureVO> picturesVO) {
        // 先确保所有图片都有 groupId（即使是独立分组）
        for (InspectionPictureVO pic : picturesVO) {
            if (StringUtil.isBlank(pic.getGroupId())) {
                pic.setGroupId(CommonUtil.generateUuid()); // 默认独立分组
            }
        }

        // 再处理成对分组逻辑
        for (InspectionPictureVO pic1 : picturesVO) {
            for (InspectionPictureVO pic2 : picturesVO) {
                // 跳过自身比较
                if (pic1.getId().equals(pic2.getId())) {
                    continue;
                }
                String picOne = removeBetweenNthUnderscores(pic1.getOriginalName(),1);
                String picTwo = removeBetweenNthUnderscores(pic2.getOriginalName(),1);



                // 判断是否属于同一组
                if (CommonUtil.isGroup(picOne, picTwo)) {
                    // 统一使用 pic1 的 groupId（避免循环依赖）
                    pic2.setGroupId(pic1.getGroupId());
                }
            }
        }
    }
    public List<List<String>> processInspectionPictures(List<InspectionPictureVO> voList) {
        this.setGroupIdForApp(voList);
        // 按groupId分组
        Map<String, List<InspectionPictureVO>> groupedByGroupId = voList.stream()
                .collect(Collectors.groupingBy(InspectionPictureVO::getGroupId));

        List<List<String>> result = new ArrayList<>();

        for (List<InspectionPictureVO> pair : groupedByGroupId.values()) {
            // 确保每组有两个元素
            if (pair.size() != 2) {
                continue; // 或者根据需求处理异常情况
            }

            // 初始化大小为2的列表，填充null
            List<String> fileDetailList = new ArrayList<>(Collections.nCopies(2, null));

            // 区分以"_T"结尾的和不以"_T"结尾的
            for (InspectionPictureVO vo : pair) {
                String originalName = vo.getOriginalName();
                if (StringUtil.isNotBlank(originalName) &&
                        originalName.substring(0, originalName.lastIndexOf('.')).contains("_T")) {
                    fileDetailList.set(0, vo.getFilePath());
                } else {
                    fileDetailList.set(1, vo.getFilePath());
                }
            }

            result.add(fileDetailList);
        }

        return result;
    }
    @Override
    public R unbindPic(BindPicDTO bindPicDTO) {

        // 校验缺陷任务是否正在识别
        Integer cnt = baseMapper.checkRecognitionTaskStatusByFileGuid(bindPicDTO.getFileGuid());
        if (cnt > 0) {
            throw new ServiceException("算法识别中,请稍后再试!");
        }

        return R.status(this.update(
                new LambdaUpdateWrapper<InspectionPicture>().eq(InspectionPicture::getFileGuid, bindPicDTO.getFileGuid())
                        .eq(InspectionPicture::getInspectionTaskId, bindPicDTO.getTaskId())
                        .set(InspectionPicture::getRecognitionTaskId, null).set(InspectionPicture::getDeviceId, null)
                        .set(InspectionPicture::getBindFlag, StringPool.NO)));
    }

    private void setPicProperty(List<InspectionPictureVO> vo, InspectionTaskPicQueryDTO dto) {
        // 设备ids
        List<String> deviceIds = vo.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());
        Map<String, BasicCommonVO> deviceMap = commonService.getDeviceByDeviceIds(deviceIds, dto.getDeviceType());
        // 查图像列表
        List<String> fileGuidList = vo.stream().map(m -> m.getFileGuid()).collect(Collectors.toList());
        R<List<AllcoreFileVO>> listR = ossClient.getFilesDetail(fileGuidList);
        if (!listR.isSuccess()) {
            return;
        }
        // 封装fileMap
        Map<String, AllcoreFileVO> fileMap =
                listR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
        vo.stream().forEach(e -> {
            e.setDeviceName(ObjectUtil.isEmpty(deviceMap.get(e.getDeviceId())) ? ""
                    : deviceMap.get(e.getDeviceId()).getDeviceName());
            e.setFilePath(
                    ObjectUtil.isEmpty(fileMap.get(e.getFileGuid())) ? "" : fileMap.get(e.getFileGuid()).getStaticPath());
            e.setOriginalName(
                    ObjectUtil.isEmpty(fileMap.get(e.getFileGuid())) ? "" : fileMap.get(e.getFileGuid()).getOriginalName());
        });
    }

    private List<InspectionPictureSaveDTO> getUploadInfo(FolderUpLoadDTO dto) {
        String inspectionTaskId = dto.getInspectionTaskId();
        String deviceType = dto.getDeviceType();
        // 任务下设备
        List<InspectionDeviceDetail> taskDeviceList =
                inspectionDeviceDetailMapper.selectList(new LambdaQueryWrapper<InspectionDeviceDetail>()
                        .eq(InspectionDeviceDetail::getInspectionTaskId, inspectionTaskId));
        // 任务下全部设备
        List<String> taskDeviceIds = taskDeviceList.stream().map(m -> m.getDeviceId()).collect(Collectors.toList());
        // 消缺任务信息
        //********************************************新需求 - 识别任务在办结后创建 *******************************************************
//        RecognitionTask recognitionTaskEntity = recognitionTaskMapper.selectOne(
//            new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, inspectionTaskId));
//        if (Func.isEmpty(recognitionTaskEntity)) {
//            this.extracted(dto.getInspectionTaskNo(), dto.getDeviceType(), recognitionTaskEntity, inspectionTaskId, "",
//                "", "");
//            recognitionTaskEntity = recognitionTaskMapper.selectOne(
//                new LambdaQueryWrapper<RecognitionTask>().eq(RecognitionTask::getInspectionTaskId, inspectionTaskId));
//        }
//        String recognitionId = recognitionTaskEntity.getId();
        String recognitionId = null;
        //********************************************新需求 - 识别任务在办结后创建 *******************************************************
        // 上传的文件
        List<FileGuidUpLoadDTO> lineUpInfo = dto.getFolderUploadList();
        List<InspectionPictureSaveDTO> upInfo = Lists.newArrayList();
        if (StringUtil.equals(BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode(), dto.getDeviceType())) {
            List<String> deviceNames = getDecviceNames(dto);
            // 获取设备信息 线路的二级是塔线
            Map<String, BasicCommonVO> commonDevice =
                    commonService.getDeviceByNames(deviceNames, BizDictEnum.DEVICE_TYPE_TMS_TOWER.getCode());
            if (deviceNames.size() != commonDevice.size()) {
                throw new ServiceException(NO_DEVICE_NAME);
            }
            for (FileGuidUpLoadDTO folderInfoDTO : lineUpInfo) {
                // 根据deviceName从Map中获取对应的BasicCommonVO
                BasicCommonVO basicCommonVO = commonDevice.get(folderInfoDTO.getFoladerName().get(1));
                if (ObjectUtil.isEmpty(basicCommonVO)) {
                    throw new ServiceException(NO_DEVICE + folderInfoDTO.getFoladerName().get(1));
                }
                // 文件夹名称对应的设备id
                String folderDeviceId = basicCommonVO.getDeviceId();
                if (!taskDeviceIds.contains(folderDeviceId)) {
                    throw new ServiceException(NO_FOLDER_DEVICE + folderInfoDTO.getFoladerName().get(1));
                }
                InspectionPictureSaveDTO save = setInspectionPictureSaveDTO(folderInfoDTO, basicCommonVO,
                        inspectionTaskId, recognitionId, deviceType);
                upInfo.add(save);
            }
        } else if (StringUtil.equals(BizDictEnum.DEVICE_TYPE_FAN.getCode(), dto.getDeviceType())) {
            // 全部的风机编号
            Set<String> fanNums = lineUpInfo.stream()
                    .map(m -> m.getFoladerName().get(0).substring(ordinalIndexOf(m.getFoladerName().get(0), "_", 3) + 1,
                            ordinalIndexOf(m.getFoladerName().get(0), "-", 1)))
                    .collect(Collectors.toSet());
            List<Fan> fanEntity = fanMapper.selectList(
                    new LambdaQueryWrapper<Fan>().likeRight(Fan::getDeptCode, dto.getDeptCode()).in(Fan::getFanNum, fanNums));
            if (fanNums.size() != fanEntity.size()) {
                log.info("存在不能识别的风机编号，请排查后上传");
                throw new ServiceException("存在不能识别的风机编号，请排查后上传");
            }

            for (Fan fan : fanEntity) {
                if (!taskDeviceIds.contains(fan.getId())) {
                    log.info("此风机号" + fan.getFanNum() + "不存在于此次任务中");
                    throw new ServiceException("此风机号" + fan.getFanNum() + "不存在于此次任务中");
                }
            }


            // 风机编号对应的风机对象
            Map<String, Fan> fanMap = fanEntity.stream().collect(Collectors.toMap(Fan::getFanNum, Function.identity()));

            for (FileGuidUpLoadDTO folderInfoDTO : lineUpInfo) {
                InspectionPictureSaveDTO save =
                        setFanInspectionPictureSaveDTO(folderInfoDTO, fanMap, inspectionTaskId, recognitionId, deviceType);
                upInfo.add(save);
            }

        } else {
            String deviceName = dto.getFolderUploadList().get(0).getFoladerName().get(0);
            List deviceNameList = Lists.newArrayList();
            deviceNameList.add(deviceName);
            Map<String, BasicCommonVO> commonDevice = commonService.getDeviceByNames(deviceNameList, deviceType);
            BasicCommonVO basicCommonVO = commonDevice.get(deviceName);
            if (ObjectUtil.isEmpty(basicCommonVO)) {
                throw new ServiceException(NO_DEVICE + deviceName);
            }
            // 文件夹名称对应的设备id
            String folderDeviceId = basicCommonVO.getDeviceId();
            if (!taskDeviceIds.contains(folderDeviceId)) {
                throw new ServiceException(NO_FOLDER_DEVICE + deviceName);
            }
            for (FileGuidUpLoadDTO folderInfoDTO : lineUpInfo) {
                InspectionPictureSaveDTO save = setInspectionPictureSaveDTO(folderInfoDTO, basicCommonVO,
                        inspectionTaskId, recognitionId, deviceType);
                upInfo.add(save);
            }
        }
        if (CollectionUtil.isNotEmpty(upInfo)) {
            // 文件夹有新的绑定图片 识别任务改成未识别 允许继续算法识别
//            recognitionTaskEntity.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_UNIDENTIFICATION.getCode());
//            recognitionTaskMapper.updateById(recognitionTaskEntity);
        }
        return upInfo;
    }

    private InspectionPictureSaveDTO setFanInspectionPictureSaveDTO(FileGuidUpLoadDTO folderInfoDTO,
                                                                    Map<String, Fan> fanMap, String inspectionTaskId, String recognitionId, String deviceType) {
        String foladerName = folderInfoDTO.getFoladerName().get(0);
        String fanBladeOrder = foladerName.split("-")[1];
        String fanDetailPosition = foladerName.split("-")[2];
        String fanNum =
                foladerName.substring(ordinalIndexOf(foladerName, "_", 3) + 1, ordinalIndexOf(foladerName, "-", 1));
        String deviceId = fanMap.get(fanNum).getId();

        InspectionPictureSaveDTO save = new InspectionPictureSaveDTO();
        save.setFileGuid(folderInfoDTO.getFileGuid());
        save.setDeviceId(deviceId);
        save.setBindFlag(StringPool.YES);
        save.setInspectionTaskId(inspectionTaskId);
        save.setRecognitionTaskId(recognitionId);
        save.setDeviceType(deviceType);
        save.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
        save.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode());

        if (fanBladeOrder.contains("A") || fanBladeOrder.contains("B") || fanBladeOrder.contains("C")) {
            save.setFanBigPosition("blade");
            save.setFanBladeOrder(fanBladeOrder);
        } else {
            save.setFanBigPosition("tower");
        }
        save.setFanDetailPosition(BizDictEnum.getCodeByName(fanDetailPosition));
        return save;
    }

    /*    public static void main(String[] args) {
        String str = "DJI_202402210853_032_9090-塔筒-机舱";
    
        // 获取正数第三个下划线的位置
        int thirdUnderscoreIndex = ordinalIndexOf(str, "_", 3);
        // 获取从后数第二个 '-' 的位置
        int secondLastDashIndex = ordinalIndexOf(str, "-", 1);
    
        if (thirdUnderscoreIndex != -1 && secondLastDashIndex != -1) {
            String result = str.substring(thirdUnderscoreIndex + 1, secondLastDashIndex);
    
            System.out.println(result);
        } else {
            System.out.println("未找到指定字符串");
        }
    }*/

    // 辅助方法，用于查找字符串中指定字符的第 n 次出现的位置
    public static int ordinalIndexOf(String str, String c, int n) {
        int pos = str.indexOf(c, 0);
        while (--n > 0 && pos != -1)
            pos = str.indexOf(c, pos + 1);
        return pos;
    }

    private InspectionPictureSaveDTO setInspectionPictureSaveDTO(FileGuidUpLoadDTO folderInfoDTO,
                                                                 BasicCommonVO basicCommonVO, String inspectionTaskId, String recognitionId, String deviceType) {
        InspectionPictureSaveDTO save = new InspectionPictureSaveDTO();
        save.setFileGuid(folderInfoDTO.getFileGuid());
        save.setDeviceId(basicCommonVO.getDeviceId());
        save.setBindFlag(StringPool.YES);
        save.setInspectionTaskId(inspectionTaskId);
        save.setRecognitionTaskId(recognitionId);
        save.setDeviceType(deviceType);
        save.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
        save.setPicDefectFlg(BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode());
        return save;
    }

    private List<String> getDecviceNames(FolderUpLoadDTO dto) {
        List<String> folderNames = new ArrayList<>();
        for (FileGuidUpLoadDTO fileUpload : dto.getFolderUploadList()) {
            List<String> folderName = fileUpload.getFoladerName();
            if (folderName.size() > 1) {
                String value = folderName.get(1);
                if (!folderNames.contains(value)) {
                    folderNames.add(value);
                }
            }
        }
        return folderNames;
    }

    public void extracted(String inspectionTaskNo, String deviceType, String inspectionTaskId) {
        extracted(inspectionTaskNo, deviceType, null
                , inspectionTaskId, "", "", "");

    }

    private void extracted(String inspectionTaskNo, String deviceType, RecognitionTask recognitionTaskEntity,
                           String inspectionTaskId, String createDept, String createUser, String deptCode) {
        recognitionTaskEntity = new RecognitionTask();
        if (StringUtil.isNotBlank(deptCode)) {
            recognitionTaskEntity.setCreateDept(createDept);
            recognitionTaskEntity.setCreateUser(createUser);
            recognitionTaskEntity.setDeptCode(deptCode);
        } else {
            recognitionTaskEntity.setDeptCode(AuthUtil.getDeptCode());
            recognitionTaskEntity.setCreateUser(AuthUtil.getUserId());
            recognitionTaskEntity.setCreateDept(AuthUtil.getDeptId());
        }
        recognitionTaskEntity.setInspectionTaskId(inspectionTaskId);
        recognitionTaskEntity.setInspectionTaskNo(inspectionTaskNo);
        recognitionTaskEntity.setRecognitionTaskName(inspectionTaskNo);
        recognitionTaskEntity.setDeviceType(deviceType);

        //任务办结后，自动启动算法识别，任务状态改为"识别中"
        recognitionTaskEntity.setRecognitionTaskStatus(BizDictEnum.RECOGNITION_STATUS_TO_IDENTIFY.getCode());
        // 查询当前启用的策略(默认只允许启用一个策略)
        Strategy strategy =
                strategyService.getOne(new LambdaQueryWrapper<Strategy>().eq(Strategy::getIsEnable, StringPool.YES));
        recognitionTaskEntity.setStrategyId(strategy.getId());
        recognitionTaskMapper.insert(recognitionTaskEntity);
        // 保存识别任务的算法信息
        List<String> list = algorithmManufacturersService.getAlgorithmManufacturers(deviceType);
        List<RecognitionTaskAlgorithm> recognitionTaskAlgorithms = new ArrayList<>();
        for (String e : list) {
            RecognitionTaskAlgorithm recognitionTaskAlgorithm = new RecognitionTaskAlgorithm();
            recognitionTaskAlgorithm.setRecognitionTaskId(recognitionTaskEntity.getId());
            recognitionTaskAlgorithm.setAlgorithmManufacturersId(e);
            recognitionTaskAlgorithms.add(recognitionTaskAlgorithm);
        }
        recognitionTaskAlgorithmService.saveBatch(recognitionTaskAlgorithms);
    }

    private PropertyFilter getFilePropertyFilter() {
        return (object, name, value) -> {
            List<String> collect = Stream.of("fileGuid", "originalName", "staticPath", "staticThumbPath", "domain",
                    "dynamicPath", "dynamicThumbPath").collect(toList());
            return collect.contains(name);
        };
    }

    private List<PatrolDataImageVO> assembleVO(List<BasicCommonVO> deviceList, List<InspectionPicture> workOrderList) {
        // 获取图片列表
        List<String> fileGuids = workOrderList.stream().map(InspectionPicture::getFileGuid).collect(toList());
        R<List<AllcoreFileVO>> listR = ossClient.getFilesDetail(fileGuids);
        if (!listR.isSuccess() || CollectionUtils.isEmpty(listR.getData())) {
            return null;
        }
        // 过滤文件返回的数据
        List<AllcoreFileVO> fileList = listR.getData().stream().map(fileCommon -> {
            String str = JSON.toJSONString(fileCommon, this.getFilePropertyFilter());
            return JSON.parseObject(str, AllcoreFileVO.class);
        }).collect(toList());
        // 组装巡检影像视图
        return deviceList.stream().map(basicCommonVO -> {
            PatrolDataImageVO dataImageVO = PatrolDataImageVO.builder().deviceName(basicCommonVO.getDeviceName())
                    .deviceId(basicCommonVO.getDeviceId()).num(0).build();
            // 填充数据
            this.assembleDataImgVO(workOrderList, dataImageVO, fileList);
            if (!CollectionUtils.isEmpty(basicCommonVO.getChildren())) {
                basicCommonVO.getChildren().forEach(childCommonVO -> {
                    PatrolDataImageVO children = PatrolDataImageVO.builder().deviceName(childCommonVO.getDeviceName())
                            .deviceId(childCommonVO.getDeviceId()).num(0).build();
                    // 填充子级数据
                    this.assembleDataImgVO(workOrderList, children, fileList);
                    if (children.getNum() > 0) {
                        // 更新父级数据
                        dataImageVO.getChildren().add(children);
                        dataImageVO.setNum(dataImageVO.getNum() + children.getNum());
                        dataImageVO.getFileList().addAll(children.getFileList());
                    }
                });
            }
            // 过滤掉没有图片的设备
            if (dataImageVO.getNum() > 0) {
                return dataImageVO;
            }
            return null;
        }).filter(Objects::nonNull).collect(toList());
    }

    /**
     * 组装巡检数据图片视图
     *
     * @param workOrderList 巡检图片列表
     * @param dataImageVO   巡检数据图片实体
     * @param fileList      文件列表
     */
    private void assembleDataImgVO(List<InspectionPicture> workOrderList, PatrolDataImageVO dataImageVO,
                                   List<AllcoreFileVO> fileList) {
        workOrderList.stream()
                .filter(workOrderPic -> Objects.equals(dataImageVO.getDeviceId(), workOrderPic.getDeviceId()))
                .forEach(workOrderPic -> {
                    fileList.stream()
                            .filter(fileCommon -> Objects.equals(fileCommon.getFileGuid(), workOrderPic.getFileGuid()))
                            .findAny().ifPresent(fileCommon -> {
                                dataImageVO.getFileList().add(fileCommon);
                                dataImageVO.setNum(dataImageVO.getNum() + 1);
                                String dateStr = DateUtil.formatDate(workOrderPic.getCreateTime());
                                if (CollectionUtils.isEmpty(dataImageVO.getFileMap().get(dateStr))) {
                                    dataImageVO.getFileMap().put(dateStr, Stream.of(fileCommon).collect(toList()));
                                } else {
                                    dataImageVO.getFileMap().get(dateStr).add(fileCommon);
                                }
                            });
                });
    }

    /**
     * 获取设备id列表
     *
     * @param list       list of BasicCommonVO
     * @param deviceType device type
     * @return list of Long
     */
    private List<String> getDeviceIds(List<BasicCommonVO> list, String deviceType) {
        if (StringUtils.equals(deviceType, BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode())) {
            // 两级层级 线路-杆塔
            return list.stream().map(basicCommonVO -> {
                List<String> guid = Stream.of(basicCommonVO.getDeviceId()).collect(toList());
                if (!CollectionUtils.isEmpty(basicCommonVO.getChildren())) {
                    val ids = basicCommonVO.getChildren().stream().map(BasicCommonVO::getDeviceId).collect(toList());
                    guid.addAll(ids);
                }
                return guid;
            }).flatMap(Collection::stream).collect(toList());
        }
        return list.stream().map(BasicCommonVO::getDeviceId).collect(toList());
    }

    /**
     * 查询图片相对地址
     *
     * @param picList
     * @return
     */
    private List<InspectionPictureVO> getPicUrl(List<InspectionPictureVO> picList) {
        List<String> fileGuidList =
                picList.stream().map(InspectionPictureVO::getFileGuid).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(fileGuidList)) {
            return picList;
        }
        R<List<AllcoreFileVO>> listFileData = ossClient.getFilesDetail(fileGuidList);
        if (!listFileData.isSuccess()) {
            log.error("查询图片地址失败");
        } else {
            Map<String, AllcoreFileVO> picInfoMap = listFileData.getData().stream()
                    .collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
            picList.stream().forEach(pic -> {
                AllcoreFileVO picInfo = picInfoMap.get(pic.getFileGuid());
                // 返回前端的fileLine的数据处理
                if (picInfo != null) {
                    pic.setFilePath(picInfo.getDynamicPath());
                    pic.setThumbnailFilePath(picInfo.getDynamicThumbPath());
                    String originalName = picInfo.getOriginalName();
                    if (StringUtil.isNotBlank(originalName)) {
                        pic.setOriginalName(originalName);
                        pic.setShortOriginalName(InspectionPictureImageComparator.returnShortName(originalName));
                    }

                }
            });
        }
        // 图片分组
        Collections.sort(picList, new InspectionPictureComparator());
        return picList;
    }

    @Override
    public R newUploadReceivePic(MultipartFile[] files, ReceivePicNewDTO dto, String hcJcAcsId, String hcJcAcsSecret) {
        /*if (!hcJcAcsId.equals(mainProperties.getAccessKeyId())
            || !hcJcAcsSecret.equals(mainProperties.getAccessKeySecret())) {
            return R.fail("header校验失败");
        }*/

        // 根据任务id查询工单id 得到 工单创建的人员单位相关信息
        String inspectionTaskId = baseMapper.getInspectionTaskByTaskId(dto.getTaskId());

        InspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionTaskId);
        dto.setCreateUser(inspectionTask.getCreateUser());
        dto.setCreateDept(inspectionTask.getCreateDept());
        dto.setDeptCode(inspectionTask.getDeptCode());

        // 存储上报图片相关信息
        nestClient.saveReceivePic(dto);

        List<ReceivePicDetail> picDetails = BeanUtil.copy(dto.getPicDetails(), ReceivePicDetail.class);
        if (CollectionUtil.isNotEmpty(picDetails)) {

            // 2上传巡检图片
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return R.fail("上传失败,未选择文件");
                }
                String fileName = file.getOriginalFilename();
                try {
                    // 文件上传
                    String fileGuid = "";

                    R<AllcoreFileVO> putFileResult =
                            ossClient.putFileAttach(BizEnum.BIZ_CODE_ORIGINAL_PICTURE.getCode(), file, StringPool.YES, "");
                    if (putFileResult.isSuccess()) {
                        // =====================1张1张的保存 好查出进度变化=====================================
                        // 从picDetails中过滤出某个杆塔的图片详情数据 通过文件名称查找
                        ReceivePicDetail receivePicDetail =
                                picDetails.stream().filter(e -> e.getPicName().equals(fileName)).findFirst().get();
                        if (Func.isNotEmpty(receivePicDetail)) {
                            AllcoreFileVO vo = putFileResult.getData();
                            log.info("获取图片Guid" + vo.getFileGuid());
                            fileGuid = vo.getFileGuid();
                            List<InspectionPictureSaveDTO> inspectionPicture = new ArrayList<>();

                            InspectionPictureSaveDTO saveDTO = new InspectionPictureSaveDTO();
                            saveDTO.setDeptCode(inspectionTask.getDeptCode());
                            saveDTO.setDeviceType(inspectionTask.getInspectionType());
                            saveDTO.setFileGuid(fileGuid);
                            saveDTO.setInspectionTaskId(inspectionTaskId);
                            saveDTO.setCreateUser(inspectionTask.getCreateUser());
                            saveDTO.setCreateDept(inspectionTask.getCreateDept());
                            saveDTO.setDeptCode(inspectionTask.getDeptCode());
                            inspectionPicture.add(saveDTO);
                            // 封装单个图片上传数据 进行上传
                            saveInspectionPicture(inspectionPicture);

                            // getDeviceGuid是航迹文件里面deviceGuid实际就是区域id值
                            BindPicDTO bindPicDTO = new BindPicDTO();
                            bindPicDTO.setDeviceId(receivePicDetail.getDeviceGuid());
                            bindPicDTO.setDeviceType(inspectionTask.getInspectionType());
                            bindPicDTO.setFileGuid(fileGuid);
                            bindPicDTO.setTaskId(inspectionTaskId);
                            bindPic(bindPicDTO);
                        }
                    } else {
                        return R.fail("上传图片异常,文件服务");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return R.fail(e.getMessage());
                }
            }
        }
        return R.success("成功");
    }

    @Override
    public R backStatus(BackStatusDTO dto, String hcJcAcsId, String hcJcAcsSecret) {

        // 跟进机场图片回传状态
        baseMapper.updateReceivePic(dto);

        return R.status(true);
    }

    @Override
    public String airPortPicProcess(String inspectionTaskNo) {
        InspectionTask inspectionTask = inspectionTaskMapper.selectOne(
                new LambdaQueryWrapper<InspectionTask>().eq(InspectionTask::getInspectionTaskNo, inspectionTaskNo));

        Integer picSum = baseMapper.getAllNum(inspectionTask.getId());
        if (Func.isNotEmpty(picSum) && picSum > 0) {
            long cnt = count(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getInspectionTaskId,
                    inspectionTask.getId()));
            return cnt + StringPool.SLASH + picSum;
        } else {
            return "1/100";
        }
    }

    @Override
    public R taskEndNotification(TaskCallBackDTO dto, String hcJcAcsId, String hcJcAcsSecret) {
        return R.status(true);
    }


    @Override
    public boolean uploadTaskPics(AirportUploadTaskPicDTO dto) {
        long count = count(new LambdaQueryWrapper<InspectionPicture>()
                .eq(InspectionPicture::getInspectionTaskId, dto.getInspectionTaskId())
                .eq(InspectionPicture::getDeviceId, dto.getDeviceId())
        );
        if(count > 0){
            throw new ServiceException("该设备下已有巡检图片，不支持再同步图片");
        }
        // todo 查询飞控任务历史详情 判断任务状态
        R r = airportTaskClient.uploadTaskPics(dto.getAirportTaskId());
        if(!r.isSuccess()){
            throw new ServiceException("同步巡检图片失败");
        }
        return true;
    }

    @Override
    public R<List<ImagesVO>> selectNewestImages(InspectionImageDTO dto) {
        List<ImagesVO> result = new ArrayList<>();
        List<ImagesFileVO> imagesFileVOS = baseMapper.selectImageFileByDeviceId(dto);
        if (CollectionUtil.isNotEmpty(imagesFileVOS)) {
            Map<String, List<ImagesFileVO>> collect = imagesFileVOS.stream()
                    .filter(vo -> vo.getShootTime() != null)
                    .collect(Collectors.groupingBy(vo -> vo.getShootTime().substring(0, 10)));

            for (Map.Entry<String, List<ImagesFileVO>> entry : collect.entrySet()) {
                ImagesVO imagesVO = new ImagesVO();
                imagesVO.setTimeAss(entry.getKey());
                imagesVO.setUrllist(entry.getValue());
                result.add(imagesVO);
            }
        }
        return R.data(result);
    }
}
