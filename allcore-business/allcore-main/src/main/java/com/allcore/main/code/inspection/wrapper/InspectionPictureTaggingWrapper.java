package com.allcore.main.code.inspection.wrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.vo.DefectInfoOnlineReportingVO;
import com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO;
import com.allcore.system.cache.SysCache;

/**
 * 翻译
 *
 * <AUTHOR>
 * @date 2023/10/07 09:36
 **/
public class InspectionPictureTaggingWrapper
    extends BaseEntityWrapper<InspectionPictureTagging, InspectionPictureTaggingVO> {

    public static InspectionPictureTaggingWrapper build() {
        return new InspectionPictureTaggingWrapper();
    }

    @Override
    public InspectionPictureTaggingVO entityVO(InspectionPictureTagging entity) {

        InspectionPictureTaggingVO vo = Objects.requireNonNull(BeanUtil.copy(entity, InspectionPictureTaggingVO.class));
        // translate(vo);
        return vo;
    }

    private void translate(DefectInfoOnlineReportingVO vo) {
        // 单位名称Zh
        String zh = SysCache.getDeptNameByDeptCode(vo.getDeptCode());
        vo.setDeptCodeZh(StringUtil.isBlank(zh) ? "" : zh);
        // 缺陷描述
        vo.setDefectDescriptionZh(CommonUtil.transDefectCode(vo.getDefectDescription(),vo.getDeviceType()));
        // 缺陷等级
        String defectLevelZh = DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), vo.getDefectLevel());
        vo.setDefectLevelZh(StringUtil.isBlank(defectLevelZh) ? "" : defectLevelZh);
        // 缺陷状态
        String eliminateStatusZh =
            DictBizCache.getValue(BizDictEnum.ELIMINATE_STATUS.getCode(), vo.getEliminateStatus());
        vo.setEliminateStatusZh(StringUtil.isBlank(eliminateStatusZh) ? "" : eliminateStatusZh);

    }

    public List<DefectInfoOnlineReportingVO> listVoByVo(List<DefectInfoOnlineReportingVO> list) {

        return (List<DefectInfoOnlineReportingVO>)list.stream().map(vo -> {
            translate(vo);
            return vo;
        }).collect(Collectors.toList());
    }

}
