package com.allcore.platform.task;


import com.allcore.platform.service.ISpecificService;
import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.HttpApiLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.allcore.common.constant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.List;

/**
 * 监控服务
 * <AUTHOR>
 * @date 2022/04/13 23:51
 **/
@Slf4j
@Component
public class MonitoringTask {

	@Autowired
	ISpecificService iSpecificService;



//	@Scheduled(cron = "0 */60 * * * ?")
	@XxlJob("connectionExternal")
	public ReturnT<String> connection(String param){
		List<Specific> list = iSpecificService.list();
		if (!CollectionUtils.isEmpty(list)){
			for (Specific spec : list){
				HttpApiLog httpApiLog = connectionHost(spec.getHost(), Integer.parseInt(spec.getPort()), Constant.TIME_OUT);
				if (httpApiLog.getHttpStatus()){
					spec.setStatus(Constant.SUCCESS_STATUS);
					iSpecificService.updateById(spec);
				}else {
					spec.setStatus(Constant.FAILED__STATUS);
					iSpecificService.updateById(spec);
				}
			}

		}
		return ReturnT.SUCCESS;
	}



	public HttpApiLog connectionHost(String host, int port, Integer timeOut) {
		Socket socket = new Socket();
		HttpApiLog httpApiLog = new HttpApiLog();
		try {
			socket.connect(new InetSocketAddress(host, port),timeOut);
		} catch (IOException e) {
			e.printStackTrace();
			log.info("服务信息"+e.getMessage());
			httpApiLog.setHttpStatus(false);
			httpApiLog.setMessage(e.getMessage());
			return httpApiLog;
		} finally {
			try {
				socket.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		httpApiLog.setHttpStatus(true);
		httpApiLog.setMessage("服务正常");
		return httpApiLog;
	}
}
