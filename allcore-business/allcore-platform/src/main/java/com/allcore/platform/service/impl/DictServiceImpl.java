package com.allcore.platform.service.impl;

import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.dict.entity.DictBiz;
import com.allcore.platform.service.DictService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/27 19:47
 */
@Service
@Lazy
public class DictServiceImpl implements DictService {

	/**
	 * 根据字典Code值类型获取相关的所有设备类型
	 *
	 * @return
	 */
	@Override
	public List<String> getDictBizV1(String dictCode) {

		// 获取字典列表
		List<String> result = new ArrayList<>();
		List<DictBiz> voltageDictBizs = DictBizCache.getList(dictCode);

		if (StringUtil.isBlank(dictCode)) {
			return voltageDictBizs.stream().map(DictBiz::getDictKey).collect(Collectors.toList());
		}

		Map<String, DictBiz> voltageDictBizKeyMap = voltageDictBizs.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity()));
		// 添加子级字典
		dictAddChildren(result, voltageDictBizKeyMap.get(dictCode), voltageDictBizs);
		return result;
	}

	/**
	 * 递归添加子字典
	 *
	 * @param result
	 * @param dictBiz
	 * @param deviceDictBizList
	 */
	private void dictAddChildren(List<String> result, DictBiz dictBiz, List<DictBiz> deviceDictBizList) {
		if (dictBiz == null) {
			return;
		}
		// 添加字典
		result.add(dictBiz.getDictKey());
		// 寻找children
		deviceDictBizList.forEach(
			e -> {
				if (e.getParentId().equals(dictBiz.getId())) {
					dictAddChildren(result, e, deviceDictBizList);
				}
			}
		);
	}


}
