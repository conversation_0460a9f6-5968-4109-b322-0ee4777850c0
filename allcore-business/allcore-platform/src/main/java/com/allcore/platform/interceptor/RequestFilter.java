package com.allcore.platform.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.utils.AESCrypt;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/09/26 16:34
 **/
@Slf4j
public class RequestFilter implements Filter {


	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

		// 初始化自定义HttpServletRequestWrapper
		ChangeRequestWrapper changeRequestWrapper = new ChangeRequestWrapper((HttpServletRequest) servletRequest);

		// 获取所有参数集合
		Map<String, String[]> parameterMap = changeRequestWrapper.getParameterMap();

		String[] params = servletRequest.getParameterValues("param");
		//取到加密参数后重置request里面的getParameterMap
		parameterMap = new HashMap<>();
		String encryptStr = params == null ? null : params[0];
		if (StrUtil.isNotEmpty(encryptStr)) {
			JSONObject jsonObject = null;
			try {
				String decrypt = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, encryptStr);
				jsonObject = JSONObject.parseObject(decrypt);
			} catch (Exception e) {
				log.error("转换json出错");
			}
			if (jsonObject != null) {
				for (String key : jsonObject.keySet()) {
					String value = jsonObject.getString(key);
					if (value == null) {
						parameterMap.put(key, null);
					} else {
						parameterMap.put(key, new String[]{value});
					}

				}
			}

		}


		// 将集合存到自定义HttpServletRequestWrapper
		changeRequestWrapper.setParameterMap(parameterMap);

		// 替换原本的request
		filterChain.doFilter(changeRequestWrapper, servletResponse);
	}

}

