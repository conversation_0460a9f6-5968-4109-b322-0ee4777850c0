<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.allcore.platform.mapper.TbQueryZtLogMapper" >
  <resultMap id="BaseResultMap" type="com.allcore.platform.entity.TbQueryZtLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="entry" property="entry" jdbcType="VARCHAR" />
    <result column="visit" property="visit" jdbcType="VARCHAR" />
    <result column="query_time" property="queryTime" jdbcType="TIMESTAMP" />
    <result column="interface_name" property="interfaceName" jdbcType="VARCHAR" />
    <result column="module" property="module" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, entry, visit, query_time, interface_name, module
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.allcore.platform.entity.TbQueryZtLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_query_zt_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from tb_query_zt_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_query_zt_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.allcore.platform.entity.TbQueryZtLogExample" >
    delete from tb_query_zt_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allcore.platform.entity.TbQueryZtLog" >
    insert into tb_query_zt_log ( entry, visit,
      query_time, interface_name, module
      )
    values ( #{entry,jdbcType=VARCHAR}, #{visit,jdbcType=VARCHAR},
      now(), #{interfaceName,jdbcType=VARCHAR}, #{module,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allcore.platform.entity.TbQueryZtLog" >
    insert into tb_query_zt_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="entry != null" >
        entry,
      </if>
      <if test="visit != null" >
        visit,
      </if>
      <if test="queryTime != null" >
        query_time,
      </if>
      <if test="interfaceName != null" >
        interface_name,
      </if>
      <if test="module != null" >
        module,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="entry != null" >
        #{entry,jdbcType=VARCHAR},
      </if>
      <if test="visit != null" >
        #{visit,jdbcType=VARCHAR},
      </if>
      <if test="queryTime != null" >
        #{queryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfaceName != null" >
        #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="module != null" >
        #{module,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allcore.platform.entity.TbQueryZtLogExample" resultType="java.lang.Integer" >
    select count(1) from tb_query_zt_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_query_zt_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.entry != null" >
        entry = #{record.entry,jdbcType=VARCHAR},
      </if>
      <if test="record.visit != null" >
        visit = #{record.visit,jdbcType=VARCHAR},
      </if>
      <if test="record.queryTime != null" >
        query_time = #{record.queryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.interfaceName != null" >
        interface_name = #{record.interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="record.module != null" >
        module = #{record.module,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_query_zt_log
    set id = #{record.id,jdbcType=BIGINT},
      entry = #{record.entry,jdbcType=VARCHAR},
      visit = #{record.visit,jdbcType=VARCHAR},
      query_time = #{record.queryTime,jdbcType=TIMESTAMP},
      interface_name = #{record.interfaceName,jdbcType=VARCHAR},
      module = #{record.module,jdbcType=TINYINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allcore.platform.entity.TbQueryZtLog" >
    update tb_query_zt_log
    <set >
      <if test="entry != null" >
        entry = #{entry,jdbcType=VARCHAR},
      </if>
      <if test="visit != null" >
        visit = #{visit,jdbcType=VARCHAR},
      </if>
      <if test="queryTime != null" >
        query_time = #{queryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfaceName != null" >
        interface_name = #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="module != null" >
        module = #{module,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allcore.platform.entity.TbQueryZtLog" >
    update tb_query_zt_log
    set entry = #{entry,jdbcType=VARCHAR},
      visit = #{visit,jdbcType=VARCHAR},
      query_time = #{queryTime,jdbcType=TIMESTAMP},
      interface_name = #{interfaceName,jdbcType=VARCHAR},
      module = #{module,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
