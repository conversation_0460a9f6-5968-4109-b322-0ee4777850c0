package com.allcore.platform.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.platform.param.params;
import com.allcore.platform.param.queryvo;
import com.allcore.platform.vo.PatrolOrderVo;
import com.allcore.platform.vo.ZhongTaiparamVo;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import com.allcore.common.config.ZtConfig;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * （中台请求util）
 * <AUTHOR>
 * @date 2022/10/21 11:45
 */
public class ZtHttpUtils {


    /**
     * 湖南中台 获取token
     */
    public static String getToken(String url) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        CloseableHttpResponse response = null;
        String tokenString = null;
        String accessToken = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded"); ;
            httpPost.addHeader("_api_version","1.0.0");
            httpPost.addHeader("_api_name","baseCenter.oauth2.accessToken");

            List<NameValuePair> paramList = new ArrayList<>();
            paramList.add(new BasicNameValuePair("grant_type", "credentials"));
//            paramList.add(new BasicNameValuePair("username", HuNanDataSyncConfig.USER_NAME));
//            paramList.add(new BasicNameValuePair("password", HuNanDataSyncConfig.PASS_WORD));
           paramList.add(new BasicNameValuePair("client_id", ZtConfig.CLIENT_ID));
            paramList.add(new BasicNameValuePair("client_secret", ZtConfig.CLIENT_SECRET));
            // 模拟表单添加添加请求内容
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            response = httpClient.execute(httpPost);
			tokenString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            JSONObject jsonObject = JSONObject.parseObject(tokenString);
//            JSONObject result = jsonObject.parseObject(jsonObject.get("result").toString());
			accessToken = jsonObject.get("access_token").toString();

        } catch (Exception e) {
//            e.printStackTrace();
        }
        return accessToken;
    }

    /**
    * 工单编制
    */
    public static String httpaddpatrolOrderVo(ZhongTaiparamVo param, PatrolOrderVo patrolOrderVo) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);

        Map<String,Object> map = new HashMap<>();
        map.put("professionalClass","SGSubstationmajor");
        try {

            map.put("patrolOrder",patrolOrderVo);
            String jsonentity = JSONObject.toJSONString(map);
            System.out.println("===========================================================注入的参数为:"+jsonentity);

            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);

        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }

    /**
    * 工单查询
    */
    public static String httpworkorderQuerycx(ZhongTaiparamVo param, params params) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);

        Map<String,Object> map = new HashMap<>();
        map.put("professionalClass","SGSubstationmajor");
        try {
            map.put("params",params);
            String jsonentity = JSONObject.toJSONString(map);
            System.out.println("===========================================================注入的参数为:"+jsonentity);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);

        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }

    /**
     * 工单查看
     */
    public static String httpworkorderQueryck(ZhongTaiparamVo param, queryvo params) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);
        try {
            String jsonentity = JSONObject.toJSONString(params);
            System.out.println("===========================================================注入的参数为:"+jsonentity);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }


    /**
     * 缺陷数据编制、查询、查看
     * @param postVo
     * @param object
     * @return
     */
    public static JSONObject doPostToJson(ZhongTaiparamVo postVo, Object object){
        JSONObject json = (JSONObject) JSONObject.toJSON(object);
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = getHttpPost(postVo);
        String result = null;
        JSONObject response = null;
        try {
            System.out.println("============================================"+json.toString());
            post.setEntity(new StringEntity(json.toString(), Charset.forName("UTF-8")));
            HttpResponse res = httpclient.execute(post);
            result = EntityUtils.toString(res.getEntity());
            response = JSONObject.parseObject(result);
            System.out.println("============================================中台响应结果"+response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return response;
    }

    /**
     * 计划接口调用
     */
    public static String httpPostSend(ZhongTaiparamVo param, Object params) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);
        try {
            String jsonentity = JSONObject.toJSONString(params);
            System.out.println("===========================================================注入的参数为:"+jsonentity);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }


    /**
     * 计划接口调用New
     */
    public static String httpPostSend2(ZhongTaiparamVo param, PatrolOrderVo patrolOrderVo) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);
        try {
            Map<String,Object> map = new HashMap<>();
            map.put("operateMode","update");
            map.put("patrolOrder",patrolOrderVo);
            String jsonentity = JSONObject.toJSONString(map);
            System.out.println("===========================================================注入的参数为:"+jsonentity);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }

    /**
     * 计划接口调用(json数组)
     */
    public static String httpPostJsonArraySend(ZhongTaiparamVo param, Object params) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);
        try {
            String jsonentity = JSONArray.toJSONString(params);
            System.out.println("===========================================================注入的参数为:"+jsonentity);
            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }

    /**
     * 工单变更
     */
    public static String httpworkorderChange(ZhongTaiparamVo param, PatrolOrderVo patrolOrderVo,String inpsecguid) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String resultString = null;
        HttpPost httpPost = getHttpPost(param);

        Map<String,Object> map = new HashMap<>();
        map.put("professionalClass","SGTransmissmajor");
        try {

            map.put("operateMode","update");
            map.put("objId",inpsecguid);
            map.put("workOrder",patrolOrderVo);

            String jsonentity = JSONObject.toJSONString(map);
            System.out.println("===========================================================注入的参数为:"+jsonentity);

            StringEntity entity = new StringEntity(jsonentity, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            System.out.println("===========================================================中台相应编码为:"+resultString);

        } catch (Exception e) {
//            e.printStackTrace();
        }
        return resultString;
    }





    public static HttpPost getHttpPost(ZhongTaiparamVo param){
        HttpPost httpPost = null;
        httpPost = new HttpPost(param.getUrl());
        httpPost.addHeader("Content-type","application/json; charset=utf-8");
        httpPost.addHeader("x-token",param.getToken());
        httpPost.addHeader("x-client-id", param.getClient());
        httpPost.addHeader("_api_name",param.get_api_name());
        httpPost.addHeader("_api_version",param.get_api_version());
        httpPost.addHeader("x-signature", param.getSignature());
        httpPost.addHeader("x-date", System.currentTimeMillis()+"");
        return httpPost;
    }





}
