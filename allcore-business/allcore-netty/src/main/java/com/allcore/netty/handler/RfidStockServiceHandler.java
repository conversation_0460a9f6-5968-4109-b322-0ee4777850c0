package com.allcore.netty.handler;

import cn.hutool.core.util.ByteUtil;
import com.allcore.common.constant.BasicConstant;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.netty.components.ServiceFactory;
import com.allcore.netty.dto.RfidBindAddDTO;
import com.allcore.netty.feign.IRfidBindClient;
import com.allcore.netty.protocol.Command;
import com.allcore.netty.protocol.Packet;
import com.allcore.netty.protocol.PacketCodeC;
import com.allcore.netty.protocol.RfidStockRequestPacket;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 请求天线开始工作监测rfid - 返回出入库情况
 * <AUTHOR>
 * @date 2022/12/01 10:24
 */
 
@Slf4j
@Component
public class RfidStockServiceHandler extends SeviceHandlerAbstract {

	@Resource
	private AllcoreRedis allcoreRedis;

	@Resource
	private IRfidBindClient iRfidBindClient;

    @PostConstruct
    @Override
    public void init() {
        ServiceFactory.seviceMap.put(Command.REQ_RFIDINFO, this);
    }

    @Override
    public void handler(Channel ch, Packet packet) {
		try {
		RfidStockRequestPacket rfidRequestPacket = decode(packet);
		//处理未开门的时获取的RFID,将其放到redis中
		if (null == allcoreRedis.get(rfidRequestPacket.getDeviceSn()+BasicConstant.XHG+rfidRequestPacket.getCabinetFloor())){
			allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+BasicConstant.XHG+rfidRequestPacket.getCabinetFloor(),rfidRequestPacket.getRfids(),BasicConstant.HOURS_1);
			allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+BasicConstant.IN_TEMP,BasicConstant.EMPTY,BasicConstant.HOURS_1);
			allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+BasicConstant.OUT_TEMP,BasicConstant.EMPTY,BasicConstant.HOURS_1);
		}else{
			int i1 = Integer.parseInt(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.LOPPNUM).toString());
			//开门时,处理返回的rfid
			List<String> oldRfids = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.XHG + rfidRequestPacket.getCabinetFloor());
			//log.info("老的RFIDS:"+oldRfids);
			Byte redisNo = rfidRequestPacket.getRedisNo();
			/*Object object = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + "_" + rfidRequestPacket.getCabinetFloor() + "_" + redisNo);
			if (null != object){*/
				for (int i = redisNo.intValue()-1; i >= 1; i--) {
					allcoreRedis.del(rfidRequestPacket.getDeviceSn() + BasicConstant.XHG + rfidRequestPacket.getCabinetFloor()+BasicConstant.XHG+i);
				}
				List<String > newRfids1 = rfidRequestPacket.getRfids();
			//log.info("新的RFIDS:"+newRfids1);
			//相同值
			List<String> list = intersectList(oldRfids, newRfids1);
			//差值
			List<String> subList = subList(newRfids1, oldRfids);
			subList.addAll(subList(oldRfids, newRfids1));
			List<String> allList = new ArrayList<>();
			allList.addAll(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + "_0"));
			allList.addAll(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + "_1"));
			allList.addAll(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + "_2"));
			allList.addAll(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + "_3"));
			//移动位置的ID
			List<String> list2 = intersectList(subList, allList);
			//加入的ID
			List<String> subList2 = subList(subList, allList);

			//log.info("差值和初始化数据的相同值:"+list2);
			//log.info("差值和初始化数据的差值值:"+subList2);
			StringBuffer buffer = new StringBuffer();
			//oldRfids.removeAll(list2);
			//newRfids1.removeAll(list2);
			//log.info("位置变化:"+allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ "_postin"));
			if (subList2.size()>0 || subList.size()>0){
				oldRfids.removeAll(list);
				newRfids1.removeAll(list);

				//log.info("出库:"+oldRfids);
				//log.info("入库:"+newRfids1);
				StringBuffer inString = new StringBuffer();
				StringBuffer outString = new StringBuffer();
				if (ObjectUtils.isNotEmpty(allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP))){
					String string = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.IN_TEMP).toString();
					List<String> ss =Arrays.asList(string.split(BasicConstant.COMMA));
					if (StringUtils.isNotEmpty(string)){
						//log.info("开始拼接1"+inString);
						inString.append(allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP).toString()).append(BasicConstant.COMMA);
						}else {
						//log.info("开始拼接2"+inString);
						inString.append(allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP).toString());
					}
				}
				if (ObjectUtils.isNotEmpty(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP))) {
						String string1 = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP).toString();
						List<String> ss1 = Arrays.asList(string1.split(BasicConstant.COMMA));
					//log.info("22:"+string1);
						if (StringUtils.isNotEmpty(string1)){
							//log.info("开始拼接2"+outString);

							outString.append(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP).toString()).append(BasicConstant.COMMA);
						}else{
							//log.info("开始拼接3"+outString);
							outString.append(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP).toString());
						}

				}

				allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP,inString.append(newRfids1.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA))).toString(),BasicConstant.HOURS_1);
				allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT_TEMP,outString.append(oldRfids.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA))).toString(),BasicConstant.HOURS_1);
				//log.info("出库buffer:{} 入库{}",outString,inString);
				//log.info("入库最终:"+allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP));
				//log.info("出库最终:"+allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT_TEMP));
			}else{
				String redisOut = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP);
				String redisIn = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.IN_TEMP);
				List<String> out = new ArrayList<>();
				if (ObjectUtils.isNotEmpty(redisOut)){
					out.addAll(Arrays.asList(redisOut));
				}
				List<String> in = new ArrayList<>();
				if (ObjectUtils.isNotEmpty(redisIn)){
					in.addAll(Arrays.asList(redisIn));
				}
				out.removeAll(list);
				out.removeAll(subList);
				if (subList.size() == 0){
				List<String> lastRfids = allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.XHG + rfidRequestPacket.getCabinetFloor()+BasicConstant.OLD);
				List<String> subList3 = subList(lastRfids, newRfids1);
					in.removeAll(subList3);
				}
				//log.info("out:"+out);
				//log.info("in:"+in);
				if (out.size()>0 && ObjectUtils.isNotEmpty(out.get(0)) || out.size() == 0){
					allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT_TEMP,out.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA)),BasicConstant.HOURS_1);

				}
				if (in.size()>0 && ObjectUtils.isNotEmpty(in.get(0))  || in.size() == 0){
					allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP,in.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA)),BasicConstant.HOURS_1);
				}
				//log.info("入库最终:"+allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN_TEMP));
				//log.info("出库最终:"+allcoreRedis.get(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT_TEMP));
			}
			allcoreRedis.set(rfidRequestPacket.getDeviceSn() + BasicConstant.LOPPNUM,i1+1);
			/*}*/
			if (i1 == 3){
				List<String> in =new ArrayList<>();
				if (StringUtils.isNotEmpty(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.IN_TEMP))){
					in.addAll(Arrays.asList(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.IN_TEMP).toString()));
				}
				List<String> out = new ArrayList<>();
				if (StringUtils.isNotEmpty(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP))){
					out.addAll(Arrays.asList(allcoreRedis.get(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP).toString()));
				}
				//log.info("out2:"+out);
				//log.info("in2:"+in);
				List<String> equal = intersectList(out, in);
				in.removeAll(equal);
				out.removeAll(equal);
				if (in.size()>0){
					allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN,in.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA)),BasicConstant.HOURS_1);
				}else{
					allcoreRedis.del(rfidRequestPacket.getDeviceSn()+ BasicConstant.IN);

				}
				if (out.size()>0){
					allcoreRedis.setEx(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT,out.stream().map(String::valueOf).collect(Collectors.joining(BasicConstant.COMMA)),BasicConstant.HOURS_1);

				}else{
					allcoreRedis.del(rfidRequestPacket.getDeviceSn()+ BasicConstant.OUT);

				}
				allcoreRedis.set(rfidRequestPacket.getDeviceSn() + BasicConstant.LOPPNUM,0);
				allcoreRedis.del(rfidRequestPacket.getDeviceSn() + BasicConstant.IN_TEMP);
				allcoreRedis.del(rfidRequestPacket.getDeviceSn() + BasicConstant.OUT_TEMP);
			}
		}
		allcoreRedis.setEx(rfidRequestPacket.getDeviceSn() + BasicConstant.XHG + rfidRequestPacket.getCabinetFloor()+BasicConstant.OLD,rfidRequestPacket.getRfids(),BasicConstant.HOURS_1);
        //send(ch, packet);
		} catch (Exception e) {
			log.error("RFID-netty处理异常异常-{}", e);
		}
    }

    public  RfidStockRequestPacket decode(Packet packet) {
        RfidStockRequestPacket rfidRequestPacket = (RfidStockRequestPacket) packet;
        try {
            byte[] data = packet.getData();
            if (data == null) {
                return rfidRequestPacket;
            }
			//序号
            byte[] no = Arrays.copyOfRange(data, 0, 1);
			//机柜层级
            byte[] floor = Arrays.copyOfRange(data, 1, 2);
			//RFID个数
            byte[] num = Arrays.copyOfRange(data, 2, 3);
			//RFID长度
            byte[] len = Arrays.copyOfRange(data, 3, 4);
			String cabinetFloor = String.valueOf(ByteUtil.bytesToInt(floor));
			int rfidNum = ByteUtil.bytesToInt(num);
			int rfidLen = ByteUtil.bytesToInt(len);
			//int redisNo = no[0];
			rfidRequestPacket.setCabinetFloor(cabinetFloor);
			int index = 4;
			List<String> strings = new ArrayList<>();
			List<RfidBindAddDTO> rfidBindAddDTOS = new ArrayList<>();
			for (int i = 0; i < rfidNum; i++) {
				//RFID
				byte[] bytes = Arrays.copyOfRange(data, index, index + rfidLen);
				String result="";
				for (int j = 0; j < bytes.length; j++) {
					result+=Integer.toHexString((bytes[j] & 0xFF) | 0x100).toUpperCase().substring(1, 3);
				}
				index +=rfidLen;
				strings.add(result.toString());
				String clientId = PacketCodeC.getClientId(packet.getDevicePosition()).split(BasicConstant.HG)[0]+BasicConstant.HG+(Integer.valueOf(rfidRequestPacket.getCabinetFloor())+1);
				log.info("rifd解析卡号"+result);
				log.info("客户端ID:"+clientId);
				allcoreRedis.setEx(result.toString(),clientId,BasicConstant.HOURS_1);
				RfidBindAddDTO rfidBindAddDTO = new RfidBindAddDTO();
				rfidBindAddDTO.setRfidGuid(result.toString());
				rfidBindAddDTO.setPosition(clientId);
				rfidBindAddDTOS.add(rfidBindAddDTO);
			}
			rfidRequestPacket.setRfids(strings);
			rfidRequestPacket.setRedisNo(no[0]);
			//自动注册RFID
			iRfidBindClient.batchAddRfidBind(rfidBindAddDTOS);

		} catch (Exception e) {
            log.error("rfid卡信息解析异常", e);
        }
        return rfidRequestPacket;
    }

    @Override
    public void send(Channel ch, Packet packet) {
		//发送机柜snCode
		RfidStockRequestPacket rfidStockRequestPacket = (RfidStockRequestPacket) packet;
		rfidStockRequestPacket.setLength((short) 27);
		ByteBuf encode = PacketCodeC.INSTANCE.encode(ch.alloc(), packet);
		final ChannelFuture f = ch.writeAndFlush(encode);
		f.addListener(new ChannelFutureListener() {
			@Override
			public void operationComplete(ChannelFuture future) {
				log.info("rfid监测指令已发送");
				if ("120".equals(Integer.valueOf(allcoreRedis.get(BasicConstant.NETTY_RFID_NUM).toString()))){
					allcoreRedis.setEx(BasicConstant.NETTY_RFID_NUM, BasicConstant.INT_ZERO,BasicConstant.HOURS_1);
				}
				allcoreRedis.setEx(BasicConstant.NETTY_RFID_NUM, Integer.valueOf(allcoreRedis.get(BasicConstant.NETTY_RFID_NUM).toString()) + 1,BasicConstant.HOURS_1);
			}
		});
    }


	/**
	 * 交集(基于java8新特性)优化解法2 适用于大数据量
	 * 求List1和List2中都有的元素
	 */
	public static List<String> intersectList(List<String> list1, List<String> list2){
		Map<String, String> tempMap = list2.parallelStream().collect(Collectors.toMap(Function.identity(), Function.identity(), (oldData, newData) -> newData));
		return list1.parallelStream().filter(str->{
			return tempMap.containsKey(str);
		}).collect(Collectors.toList());
	}

	/**
	 * 差集(基于java8新特性)优化解法2 适用于大数据量
	 * 求List1中有的但是List2中没有的元素
	 */
	public static List<String> subList(List<String> list1, List<String> list2) {
		Map<String, String> tempMap = list2.parallelStream().collect(Collectors.toMap(Function.identity(), Function.identity(), (oldData, newData) -> newData));
		return list1.parallelStream().filter(str->{
			return !tempMap.containsKey(str);
		}).collect(Collectors.toList());
	}

}
