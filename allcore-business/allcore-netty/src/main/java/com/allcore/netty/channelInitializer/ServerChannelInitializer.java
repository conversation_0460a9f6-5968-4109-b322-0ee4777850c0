package com.allcore.netty.channelInitializer;


import com.allcore.netty.handler.OtherServerHandler;
import com.allcore.netty.handler.ServerHeartHandler;
import com.allcore.netty.handler.Spliter;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component(value = "serverChannelInitializer")
public class ServerChannelInitializer extends ChannelInitializer<SocketChannel> {

    @Value("${server.READER_IDLE_TIME_SECONDS}")
    private int READER_IDLE_TIME_SECONDS;

    @Value("${server.WRITER_IDLE_TIME_SECONDS}")
    private int WRITER_IDLE_TIME_SECONDS;

    @Value("${server.ALL_IDLE_TIME_SECONDS}")
    private int ALL_IDLE_TIME_SECONDS;

    @Autowired
    @Qualifier("serverHeartHandler")
    private ChannelInboundHandlerAdapter serverHeartHandler;

    @Autowired
    @Qualifier("otherServerHandler")
    private ChannelInboundHandlerAdapter otherServerHandler;

    @Override
    protected void initChannel(SocketChannel socketChannel) throws Exception {
    	ChannelPipeline p = socketChannel.pipeline();
    	//检测空闲必须放在这里 因为pipeline是分顺序加载的
    	p.addLast("idleStateHandler", new IdleStateHandler(READER_IDLE_TIME_SECONDS
    			, WRITER_IDLE_TIME_SECONDS, ALL_IDLE_TIME_SECONDS, TimeUnit.SECONDS));
    	//解决粘包拆包问题
    	p.addLast(new Spliter());
//    	p.addLast(new PacketDecodeHandler());
        //自定义的hanlder
        p.addLast("serverHeartHandler", new ServerHeartHandler());
        p.addLast("otherServerHandler", new OtherServerHandler());
    }
}
