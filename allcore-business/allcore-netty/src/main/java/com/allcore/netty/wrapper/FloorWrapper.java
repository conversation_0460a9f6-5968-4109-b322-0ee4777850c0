package com.allcore.netty.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.netty.entity.EquipmentCabinetFloorEntity;
import com.allcore.netty.vo.EquipmentCabinetFloorVO;

/**
 * <AUTHOR>
 * @date 2022/12/08 10:45
 **/
public class FloorWrapper extends BaseEntityWrapper<EquipmentCabinetFloorEntity, EquipmentCabinetFloorVO> {

	public static FloorWrapper build() {
		return new FloorWrapper();
	}

	@Override
	public EquipmentCabinetFloorVO entityVO(EquipmentCabinetFloorEntity entity) {
		EquipmentCabinetFloorVO equipmentCabinetFloorVO = BeanUtil.copyProperties(entity, EquipmentCabinetFloorVO.class);
		equipmentCabinetFloorVO.setFloorTypeZh(DictBizCache.getValue(MainBizEnum.FLOOR_TYPE.getCode(), entity.getFloorType()));
		return equipmentCabinetFloorVO;
	}
}
