package com.allcore.netty.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.system.cache.SysCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserSearchClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * websocket获取用户
 *
 * <AUTHOR>
 * @date 2022/12/12 13:17
 **/
@Component
public class WsAccountUtil {

	private String loginSuffix = "*-login-*";

	private String largeScreenSuffix = "*-largeScreen-*";

	@Resource
	private IUserSearchClient userSearchClient;

	@Resource
	private AllcoreRedis allcoreRedis;


	/**
	 * 获取建立连接的用户和单位
	 *
	 * @param deptCode
	 * @return
	 */
	public WsUser getWsAccountByDeptCode(String deptCode) {
		WsUser wsAccount = getWsAccount();
		if (StrUtil.isEmpty(deptCode)) {
			return wsAccount;
		}
		wsAccount.setLoginUserList(wsAccount.getLoginUserList().stream().filter(user -> deptCode.contains(SysCache.getDept(user.getDeptId()).getDeptCode())).collect(Collectors.toList()));
		wsAccount.setLargeScreenCodeList(wsAccount.getLargeScreenCodeList().stream().filter(dept -> deptCode.contains(dept)).collect(Collectors.toList()));

		return wsAccount;
	}

	/**
	 * 获取建立连接的用户和单位
	 *
	 * @param userId
	 * @return
	 */
	public WsUser getWsAccountByUserId(String userId) {
		WsUser wsAccount = getWsAccount();
		if (StrUtil.isEmpty(userId)) {
			return wsAccount;
		}
		wsAccount.setLoginUserList(wsAccount.getLoginUserList().stream().filter(user -> userId.equals(user.getId())).collect(Collectors.toList()));
		wsAccount.setLargeScreenCodeList(new ArrayList<>());
		return wsAccount;
	}

	/**
	 * 获取建立连接的用户和单位
	 *
	 * @return
	 */
	public WsUser getWsAccount() {
		Set<String> loginKeyList = allcoreRedis.keys(loginSuffix);
		Set<String> largeScreenKeyList = allcoreRedis.keys(largeScreenSuffix);
		loginKeyList.addAll(largeScreenKeyList);
		Set<String> redisKeyList = loginKeyList;
		Set<String> loginUserKeyList = new HashSet<>();
		Set<String> largeScreenDeptCodeKeyList = new HashSet<>();
		for (String key : redisKeyList) {
			String[] split = key.split("-");
			if (split.length > 0) {
				if ("login".equals(split[1])) {
					loginUserKeyList.add(split[0]);
				} else if ("largeScreen".equals(split[1])) {
					largeScreenDeptCodeKeyList.add(split[0]);
				}
			}
		}
		WsUser wsUser = new WsUser();
		wsUser.setLoginUserList(new ArrayList<>());
		wsUser.setLargeScreenCodeList(largeScreenDeptCodeKeyList.stream().collect(Collectors.toList()));
		if (CollectionUtil.isNotEmpty(loginUserKeyList)) {
			R<List<User>> result = userSearchClient.listByUser(CollectionUtil.join(loginUserKeyList, ","));
			if (result.isSuccess()) {
				wsUser.setLoginUserList(result.getData());
			}
		}
		return wsUser;
	}

	public class WsUser {
		/**
		 * 登陆类型创建的连接用户
		 */
		private List<User> LoginUserList;

		/**
		 * 大屏创建的连接部门
		 */
		private List<String> largeScreenCodeList;

		public List<User> getLoginUserList() {
			return LoginUserList;
		}

		public void setLoginUserList(List<User> loginUserList) {
			LoginUserList = loginUserList;
		}

		public List<String> getLargeScreenCodeList() {
			return largeScreenCodeList;
		}

		public void setLargeScreenCodeList(List<String> largeScreenCodeList) {
			this.largeScreenCodeList = largeScreenCodeList;
		}
	}


}
