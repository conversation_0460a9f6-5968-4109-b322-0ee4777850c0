/*
package com.allcore.code.netty.protocol;

import lombok.Data;

*/
/**
 * <AUTHOR>
 * @date 2021-11-01 11:00
 *//*

@Data
public class RfidRequestPacket extends Packet {
    */
/**
     * rfid卡号
     *//*

    private String rfid;
    */
/**
     * rfid个数
     *//*

    private String rfidNum;
    */
/**
     * rfid长度
     *//*

    private String rfidLen;

    @Override
    public Byte getCommand() {
        return Command.RFID_STOCK;
    }
}
*/
