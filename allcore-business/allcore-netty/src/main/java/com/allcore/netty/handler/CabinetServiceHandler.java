package com.allcore.netty.handler;

import com.alibaba.fastjson.JSONObject;
import com.allcore.netty.components.ServiceFactory;
import com.allcore.netty.protocol.CabinetRequestPacket;
import com.allcore.netty.protocol.Command;
import com.allcore.netty.protocol.Packet;
import com.allcore.netty.protocol.PacketCodeC;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
    * @ClassName: RegisterServiceHandler
    * @Description: 机柜信息业务
    * <AUTHOR>
    * @date 2020年6月8日
    *
 */
@Component
@Slf4j
public class CabinetServiceHandler extends SeviceHandlerAbstract {

	@PostConstruct
	@Override
	public void init(){
		ServiceFactory.seviceMap.put(Command.REQ_CABINETINFO, this);
	}

	@Override
	public void handler(Channel ch, Packet packet) {
		log.info("机柜信息"+JSONObject.toJSONString(packet));
		//返回嵌入式响应包
	}
	@Override
	public void send(Channel ch, Packet packet) {
		//发解锁指令给嵌入式
		ByteBuf encode = PacketCodeC.INSTANCE.encode(ch.alloc(),packet);

		final ChannelFuture f = ch.writeAndFlush(encode);
		  f.addListener(new ChannelFutureListener() {
		       @Override
		       public void operationComplete(ChannelFuture future) {
		    	   //
		    	   System.out.println("机柜信息已发送");
		    }
		});
	}

	/**
	    * @Title: decode
	    * @Description: 解析温湿度信息
	    * @param @param packet
	    * @param @return    参数
	    * @return TemHumRequestPacket    返回类型
	    * @throws
	 */
	public static CabinetRequestPacket decode(Packet packet){
		CabinetRequestPacket cabinetRequestPacket = (CabinetRequestPacket)packet;
		return cabinetRequestPacket;
	}
}


