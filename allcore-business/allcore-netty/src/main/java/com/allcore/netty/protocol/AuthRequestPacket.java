package com.allcore.netty.protocol;

import lombok.Data;

/**
    * @ClassName: AuthRequestPacket
    * @Description: 客户端认证请求数据包
    * <AUTHOR>
    * @date 2020年6月16日
    *
 */
@Data
public class AuthRequestPacket extends Packet {

	/**
	 * 设备在机柜状态-是否有无
	 */
	private int deviceStatus;

	/**
	 * 设备id(rfid)
	 */
	private String deviceID;

	@Override
	public Byte getCommand() {
		return Command.REQ_AUTHENTICATION;
	}

}
