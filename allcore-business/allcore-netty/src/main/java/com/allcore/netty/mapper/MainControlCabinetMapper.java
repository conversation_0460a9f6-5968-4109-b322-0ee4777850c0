package com.allcore.netty.mapper;

import com.allcore.netty.entity.MainControlCabinetEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 主控柜表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-22 10:21:29
 */
@Mapper
public interface MainControlCabinetMapper extends BaseMapper<MainControlCabinetEntity> {

	void resetStoreRoom(String cabinetGuid);
}
