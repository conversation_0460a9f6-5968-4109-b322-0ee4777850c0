package com.allcore.netty.controller;


import com.allcore.common.base.ZxhcController;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.netty.dto.StoreRoomDTO;
import com.allcore.netty.service.EquipmentCabinetService;
import com.allcore.netty.service.StoreRoomService;
import com.allcore.netty.vo.StoreRoomInfoVO;
import com.allcore.netty.vo.StoreRoomVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 库房表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-22 10:21:29
 */
@RestController
@RequestMapping("storeroom")
@Api(value = "库房接口", tags = "库房接口")
public class StoreRoomController extends ZxhcController {

    @Resource
    private StoreRoomService storeRoomService;


	/**
	 * 保存
	 */
	@PostMapping("/saveStoreRoom")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "添加库房", notes = "传入StoreRoomDTO")
	public R saveStoreRoom(@RequestBody StoreRoomDTO dto){

		String storeRoomGuid = storeRoomService.saveStoreRoom(dto);
		return R.data(storeRoomGuid,"添加成功");
	}

	@GetMapping("/getStoreRoom")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询部门下的库房和机柜", notes = "传入deptCode")
	public R<List<TreeNode>> getStoreRoom(@RequestParam("deptCode") String deptCode) {
		List<TreeNode> treeNodes = storeRoomService.getStoreRoom(deptCode);
		return R.data(treeNodes);
	}

	@GetMapping("/updateStoreRoomName")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "库房重命名", notes = "传入storeRoomGUID、storeRoomName")
	public R updateStoreRoomName(@RequestParam("storeRoomGUID") String storeRoomGUID, @RequestParam("storeRoomName") String storeRoomName){
		storeRoomService.updateStoreRoomName(storeRoomGUID,storeRoomName);
		return R.success("重命名成功");
	}

	@GetMapping("/delStoreRoom")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除库房", notes = "传入storeRoomGUID")
	public R<StoreRoomVO> delStoreRoom(@RequestParam("storeRoomGUID") String storeRoomGUID) {
		storeRoomService.delStoreRoom(storeRoomGUID);
		return R.success("删除成功");
	}

	@GetMapping("/delCabinet")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除机柜", notes = "传入cabinetGuid")
	public R<StoreRoomVO> delCabinet(@RequestParam("cabinetGuid") String cabinetGuid) {
		storeRoomService.delCabinet(cabinetGuid);
		return R.success("删除成功");
	}

	@PostMapping("/editorStoreRoom")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "编辑库房", notes = "传入StoreRoomDTO")
	public R editorStoreRoom(@RequestBody StoreRoomDTO dto){

		storeRoomService.editorStoreRoom(dto);
		return R.success("编辑成功");
	}

	@GetMapping("/homeStoreRoom")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "首页库房", notes = "传入StoreRoomDTO")
	public R<List<StoreRoomVO>> homeStoreRoom(@RequestParam("deptCode") String deptCode){

		List<StoreRoomVO> storeRoomVos = storeRoomService.homeStoreRoom(deptCode);
		return R.data(storeRoomVos);
	}

	@GetMapping("/storeRoomInfo")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "查询库房信息", notes = "传入storeRoomGUID")
	public R<StoreRoomInfoVO> storeRoomInfo(@RequestParam("storeRoomGUID") String storeRoomGUID){

		StoreRoomInfoVO storeRoomInfo = storeRoomService.storeRoomInfo(storeRoomGUID);
		return R.data(storeRoomInfo);
	}

	@GetMapping("/updateVisitStatus")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "修改库房访问模式", notes = "传入storeRoomGUID")
	public R<StoreRoomInfoVO> updateVisitStatus(@RequestParam("storeRoomGUID") String storeRoomGUID,@RequestParam("visitStatus") String visitStatus){

		storeRoomService.updateVisitStatus(storeRoomGUID, visitStatus);
		return R.success("修改成功");
	}

	@GetMapping("/bindCabinet")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "绑定、解绑机柜", notes = "传入storeRoomGUID、cabinetGuid、isBind（true:绑定,false:解绑）")
	public R bindCabinet(@RequestParam("storeRoomGUID") String storeRoomGUID,
										  @RequestParam("cabinetGuid") String cabinetGuid,
										  @RequestParam("isBind") Boolean isBind){

		String msg = storeRoomService.bindCabinet(storeRoomGUID, cabinetGuid, isBind);
		return R.success(msg);
	}

	@GetMapping("/updateCabinetName")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "机柜重命名", notes = "传入storeRoomGUID、storeRoomName")
	public R updateCabinetName(@RequestParam("cabinetGuid") String cabinetGuid, @RequestParam("cabinetName") String cabinetName){
		storeRoomService.updateCabinetName(cabinetGuid,cabinetName);
		return R.success("重命名成功");
	}

}
