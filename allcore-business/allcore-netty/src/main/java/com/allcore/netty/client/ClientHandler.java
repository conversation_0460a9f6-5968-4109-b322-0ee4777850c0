package com.allcore.netty.client;

import java.util.Arrays;
import java.util.Date;

import com.allcore.netty.utils.CrcUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;


/**
    * @ClassName: ClientHandler
    * @Description: 模拟客户端发送协议
    * <AUTHOR>
    * @date 2020年6月17日
    *
 */
public class ClientHandler extends ChannelInboundHandlerAdapter {
    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        System.out.println(new Date() + ": 客户端开始登录");

        // 编码
        ByteBuf buffer = testTempHum();

        // 写数据
        ctx.channel().writeAndFlush(buffer);
    }


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
		int readerIndex = byteBuf.readerIndex();
		byteBuf.getBytes(readerIndex, bytes);
		System.out.println(bytes.length);
		System.out.println(Arrays.toString(bytes));
        /*Packet packet = PacketCodeC.INSTANCE.decode(byteBuf);
        if(packet.getDeviceType() == 1){
        	System.out.println("客户端认证成功...");
        }*/
        /*if (packet instanceof LoginResponsePacket) {
            LoginResponsePacket loginResponsePacket = (LoginResponsePacket) packet;

            if (loginResponsePacket.isSuccess()) {
                System.out.println(new Date() + ": 客户端登录成功");
            } else {
                System.out.println(new Date() + ": 客户端登录失败，原因：" + loginResponsePacket.getReason());
            }
        }*/
    }

    public static ByteBuf textAuth(){
    	// 1. 创建 ByteBuf 对象
        ByteBuf byteBuf = ByteBufAllocator.DEFAULT.ioBuffer();
        // 3. 实际编码过程
        byteBuf.writeByte(0xBB);//嵌入式传给工控机
        byteBuf.writeByte(1);//设备类型
        byteBuf.writeBytes(new byte[]{65,1,1,1});//货架号或卡槽号
        byteBuf.writeShort(25);//整个数据包长度

        byteBuf.writeByte(1);//返回类型
        byteBuf.writeByte(0);//命令类型
        byteBuf.writeShort(11);//数据内容长度
        byteBuf.writeByte(0);
        byteBuf.writeBytes("1234567890".getBytes());//数据内容
        byte[] bytes = new byte[byteBuf.readableBytes()];
        int readerIndex = byteBuf.readerIndex();
        byteBuf.getBytes(readerIndex, bytes);
//        byteBuf.writeShort(CRC16Util.crc16(bytes));
        return byteBuf;
    }

    public static ByteBuf textHeart(){
    	// 1. 创建 ByteBuf 对象
        ByteBuf byteBuf = ByteBufAllocator.DEFAULT.ioBuffer();
        // 3. 实际编码过程
        byteBuf.writeByte(0xBB);//嵌入式传给工控机
        byteBuf.writeByte(1);//设备类型
        byteBuf.writeBytes(new byte[]{65,1,1,1});//货架号或卡槽号
        byteBuf.writeShort(16);//整个数据包长度

        byteBuf.writeByte(1);//返回类型
        byteBuf.writeByte(100);//命令类型
        byteBuf.writeShort(4);//数据内容长度
        byteBuf.writeBytes("ping".getBytes());//数据内容
        return byteBuf;
    }



	public static ByteBuf testTempHum(){
		// 1. 创建 ByteBuf 对象
		ByteBuf byteBuf = ByteBufAllocator.DEFAULT.ioBuffer();
		// 3. 实际编码过程
		byteBuf.writeByte(0xBB);//嵌入式传给工控机
		byteBuf.writeByte(4);//设备类型
		byteBuf.writeBytes(new byte[]{65,1,1,1});//货架号或卡槽号
		byteBuf.writeShort(31);//整个数据包长度

		byteBuf.writeByte(0);//返回类型
		byteBuf.writeByte(200);//命令类型
		byteBuf.writeBytes("ZXHC001ZK001".getBytes());//机柜码
		byteBuf.writeShort(5);//数据内容长度
		byteBuf.writeShort(30);//数据内容-序号
		byteBuf.writeShort(70);//数据内容-天线号
		byteBuf.writeByte(1);//数据内容-开关门
		byte[] bytes = new byte[byteBuf.readableBytes()];
		int readerIndex = byteBuf.readerIndex();
		byteBuf.getBytes(readerIndex, bytes);
		byteBuf.writeShort(CrcUtil.calcCrc16(bytes));
		return byteBuf;
	}

//
//	public static void main(String[] args) {
//		System.out.println(ByteBufUtil.hexDump(testTempHum()));
//	}
}
