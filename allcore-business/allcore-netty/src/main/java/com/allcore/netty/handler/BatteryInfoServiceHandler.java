package com.allcore.netty.handler;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.constant.BasicConstant;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.netty.components.ServiceFactory;
import com.allcore.netty.consts.Const;
import com.allcore.netty.dto.BatteryDTO;
import com.allcore.netty.dto.WarnDeviceDTO;
import com.allcore.netty.feign.IBatteryClient;
import com.allcore.netty.feign.IWarnClient;
import com.allcore.netty.protocol.BatteryInfoRequestPacket;
import com.allcore.netty.protocol.Command;
import com.allcore.netty.protocol.Packet;
import com.allcore.netty.utils.ByteUtil;
import com.allcore.netty.utils.OrderUtil;
import com.allcore.netty.vo.BatteryVO;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.util.Attribute;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName: RegisterServiceHandler
 * @Description: 电池信息业务
 * @date 2020年6月8日
 */
@Component
@Slf4j
public class BatteryInfoServiceHandler extends SeviceHandlerAbstract {

	@Resource
	private AllcoreRedis allcoreRedis;
    @Autowired
    private IBatteryClient batteryClient;
	@Autowired
	private IWarnClient warnClient;

    @PostConstruct
    @Override
    public void init() {
        ServiceFactory.seviceMap.put(Command.REQ_BATTERYINFO, this);
    }

    @Override
    public void handler(Channel ch, Packet packet) {
        // 解析电池信息包
        BatteryInfoRequestPacket batteryPacket = decode(packet);
        // 保存电池连接
		String key = OrderUtil.handlerKey(packet);
        Attribute<String> attr = ch.attr(clientInfo);
        attr.set(key);
        channelRepository.put(key, ch);
		try {
			// 根据上传的字段判断，决定是否返回的响应包(优先应答)
			if(packet.getReturnType() == 1) {
				Packet ack = new BatteryInfoRequestPacket();
				ack.setResStatus(Const.RESPONSE_STATUS_SUCCESS);
				ack.setReturnType((byte)3);// 应答包
				ack.setLength((short)15); // 应答包定长15
				ack.setDeviceType(packet.deviceType);

				ack.setDevicePosition(packet.devicePosition);
				send(ch, ack);
			}
			log.info("机柜sn:{},位置:{},电池sn:{},电池状态:{},循环次数:{},电量:{},温度:{},电芯容量:{},电压{}",batteryPacket.getDeviceSn(), batteryPacket.getBatteryPosition().split("-0")[0], batteryPacket.getDeviceId(), batteryPacket.getDeviceStatus(), batteryPacket.getBatteryLife(), batteryPacket.getBatteryPower(),batteryPacket.getTemp(),batteryPacket.getFullCapacity(),batteryPacket.getVoltage());
			// 更新电池信息
			updateBatteryInfo(batteryPacket);
		} catch (Exception e) {
			log.error("电池信息更新异常");
			e.printStackTrace();
		}

    }

	private void updateBatteryInfo(BatteryInfoRequestPacket info) {
		String circleCount = info.getBatteryLife();
		BatteryDTO batteryDTO = new BatteryDTO();
		batteryDTO.setSnCode(info.getDeviceId());
		batteryDTO.setState(info.getDeviceStatus() + BasicConstant.EMPTY);
		batteryDTO.setCircleCount(Integer.valueOf(circleCount));
		batteryDTO.setPosition(info.getBatteryPosition().split("-0")[0]);
		batteryDTO.setBatteryLevel(info.getBatteryPower());
		batteryDTO.setFullCapacity(info.getFullCapacity());
		// 更新电池信息后返回电池的设备类型
		R<BatteryVO> r = batteryClient.updateBySn(batteryDTO);
		if(ObjectUtils.isEmpty(r.getData())) {
			return;
		}
		BatteryVO vo = r.getData();
		String key = BasicConstant.WARN_PREFIX + MainBizEnum.DEVICE_TYPE_2.getCode() + BasicConstant.HG + vo.getBatteryModel();
		String value = allcoreRedis.get(key);
		if(StringUtils.isEmpty(value)) {
			// 未设置设备预警
			log.info("key:{} is null", key);
			return;
		}

		JSONObject json = JSON.parseObject(value);

		if(vo.getCircleCount() > json.getInteger(BasicConstant.CIR)) {
			// 循环告警
			WarnDeviceDTO dto = new WarnDeviceDTO();
			dto.setWarnDeviceGuid(IdUtil.simpleUUID());
			dto.setStoreRoomGuid(vo.getStoreRoomGuid());
			dto.setDeviceType(MainBizEnum.DEVICE_TYPE_2.getCode());
			dto.setDeviceModel(vo.getBatteryModel());
			dto.setDeviceNo(vo.getBatteryCode());
			dto.setPosition(vo.getPosition());
			dto.setMaintanceCount(vo.getMaintainCount());
			dto.setCirCount(vo.getCircleCount());
			dto.setWarnDeviceType(MainBizEnum.WARN_STATE_2.getCode());
			warnClient.add(dto);
		}
	}

    /**
     * @param @param  packet
     * @param @return 参数
     * @return TemHumRequestPacket    返回类型
     * @throws
     * @Title: decode
     * @Description: 解析温湿度信息
     */
    public static BatteryInfoRequestPacket decode(Packet packet) {
        BatteryInfoRequestPacket batteryInfoRequestPacket = (BatteryInfoRequestPacket) packet;
        byte[] data = packet.getData();
        if (ArrayUtil.isEmpty(data)) {
            return batteryInfoRequestPacket;
        }
        int idInt = ByteUtil.bytesToShort(Arrays.copyOfRange(data, 0, 1));
        byte[] deviceId = Arrays.copyOfRange(data, 1, idInt+1);//设备Id
        byte[] deviceStatus = Arrays.copyOfRange(data, idInt+1, idInt+2);//设备状态
        byte[] voltage = Arrays.copyOfRange(data, idInt+2, idInt+4);//电压
        byte[] batteryLife = Arrays.copyOfRange(data, idInt+4, idInt+6);//寿命
        byte[] batteryPower = Arrays.copyOfRange(data, idInt+6, idInt+7);//电量
        byte[] batteryTemp = Arrays.copyOfRange(data, idInt+7, idInt+9);//电池温度
        byte[] batteryElectric = Arrays.copyOfRange(data, idInt+9, idInt+11);//电芯容量
        String clientId = OrderUtil.getClientId(packet.getDevicePosition());//位置

        String batteryCode = OrderUtil.byteArr2Char(deviceId);
        short temp = ByteUtil.bytesToShort(batteryTemp);
        batteryInfoRequestPacket.setDeviceId(batteryCode);
        batteryInfoRequestPacket.setDeviceStatus(ByteUtil.byteOneInt(deviceStatus[0]));
		String voltageInt = "";
		for (byte b:
		voltage) {
			int expected = b & 0xff;
			voltageInt += expected;
		}
        batteryInfoRequestPacket.setVoltage(voltageInt);
        batteryInfoRequestPacket.setBatteryLife(String.valueOf(ByteUtil.getShort(batteryLife, false)));
        batteryInfoRequestPacket.setBatteryPower((int) ByteUtil.byteOneInt(batteryPower[0]));
        batteryInfoRequestPacket.setBatteryPosition(clientId);
        batteryInfoRequestPacket.setTemp(String.valueOf(temp));
		batteryInfoRequestPacket.setFullCapacity(String.valueOf(ByteUtil.bytesToShort(batteryElectric)));
		batteryInfoRequestPacket.setTemp(String.valueOf(ByteUtil.bytesToShort(batteryTemp)));
        return batteryInfoRequestPacket;
    }

    @Override
    public void send(Channel ch, Packet packet) {
		ByteBuf encode = OrderUtil.encode(ch.alloc(), packet);
		ch.writeAndFlush(encode);
    }
}


