<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allcore.netty.mapper.WarningMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.allcore.netty.entity.WarningEntity" id="warningMap">
        <result property="id" column="id"/>
        <result property="warnGuid" column="warn_guid"/>
        <result property="warnStatus" column="warn_status"/>
        <result property="warnType" column="warn_type"/>
        <result property="deptCode" column="dept_code"/>
        <result property="receiveUser" column="receive_user"/>
        <result property="warnDeviceGuid" column="warn_device_guid"/>
        <result property="createDept" column="create_dept"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
