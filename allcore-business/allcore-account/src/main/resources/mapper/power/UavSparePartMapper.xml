<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.account.power.mapper.UavSparePartMapper">

    <insert id="insertDetailBatch" parameterType="java.util.List">
        INSERT INTO account_uav_spare_part
        (
        id,
        spare_part_guid,
        spare_part_name,
        uav_brand,
        model_guid,
        spare_part_no,
        spare_part_brand,
        spare_part_type,
        spare_part_model,
        manufacturer,
        remarks,
        dept_code,
        create_time,
        create_dept,
        create_user,
        update_user,
        update_time,
        is_deleted
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.sparePartGuid},
            #{item.sparePartName},
            #{item.uavBrand},
            #{item.modelGuid},
            #{item.sparePartNo},
            #{item.sparePartBrand},
            #{item.sparePartType},
            #{item.sparePartModel},
            #{item.manufacturer},
            #{item.remarks},
            #{item.deptCode},
            #{item.createTime},
            #{item.createDept},
            #{item.createUser},
            #{item.updateUser},
            #{item.updateTime},
            #{item.isDeleted}
            )
        </foreach>
    </insert>


    <select id="pageList" resultType="com.allcore.account.power.vo.UavSparePartVO">
        SELECT
        bu.id,
        bu.spare_part_guid,
        bu.spare_part_name,
        bu.uav_brand,
        bu.model_guid,
        bu.spare_part_no,
        bu.spare_part_brand,
        bu.spare_part_type,
        bu.spare_part_model,
        bu.manufacturer,
        bu.remarks,
        bu.dept_code,
        bu.create_time,
        bu.create_dept,
        bu.create_user,
        bu.update_user,
        bu.update_time,
        bu.is_deleted,

        bui.model_name,
        IFNULL( bbq.stock_count, 0 ) AS stock_count
        FROM
        `account_uav_spare_part` bu
        LEFT JOIN ( SELECT spare_part_guid, sum( bus.stock_count ) stock_count FROM account_uav_spare_part_stock bus GROUP BY spare_part_guid ) bbq ON bu.spare_part_guid = bbq.spare_part_guid
        LEFT JOIN account_uav_model_info bui ON bui.model_guid = bu.model_guid
        LEFT JOIN sys_dept bd ON bd.dept_code = bu.dept_code
        <where>
            and bu.is_deleted = 0
            and bd.`status` = 'yes'
            <if test="dto.deptCode != null and dto.deptCode !=''">
                and bu.dept_code like concat(#{dto.deptCode},'%')
            </if>
            <if test="dto.modelGuid != null and dto.modelGuid !=''">
                and bu.model_guid = #{dto.modelGuid}
            </if>
            <if test="dto.uavBrand != null and dto.uavBrand !=''">
                and bu.uav_brand = #{dto.uavBrand}
            </if>
            <if test="dto.sparePartBrand != null and dto.sparePartBrand !=''">
                and bu.spare_part_brand like concat('%',#{dto.sparePartBrand},'%')
            </if>
            <if test="dto.sparePartName != null and dto.sparePartName !=''">
                and bu.spare_part_name like concat('%',#{dto.sparePartName},'%')
            </if>
        </where>
        order by bu.create_time desc
    </select>

    <select id="getInfoByGuid" resultType="com.allcore.account.power.vo.UavSparePartVO">
        SELECT
        bu.id,
        bu.spare_part_guid,
        bu.spare_part_name,
        bu.uav_brand,
        bu.model_guid,
        bu.spare_part_no,
        bu.spare_part_brand,
        bu.spare_part_type,
        bu.spare_part_model,
        bu.manufacturer,
        bu.remarks,
        bu.dept_code,
        bu.create_time,
        bu.create_dept,
        bu.create_user,
        bu.update_user,
        bu.update_time,
        bu.is_deleted,
        bbq.stock_count
        FROM
        `account_uav_spare_part` bu LEFT JOIN (SELECT spare_part_guid,sum( bus.stock_count ) stock_count FROM account_uav_spare_part_stock  bus GROUP BY spare_part_guid
        ) bbq ON bu.spare_part_guid = bbq.spare_part_guid
        <where>
            and bu.is_deleted = 0
            and bu.spare_part_guid = #{sparePartGuid}
        </where>
    </select>

    <select id="getInfoById" resultType="com.allcore.account.power.vo.UavSparePartVO">
        SELECT
        bu.id,
        bu.spare_part_guid,
        bu.spare_part_name,
        bu.uav_brand,
        bu.model_guid,
        bu.spare_part_no,
        bu.spare_part_brand,
        bu.spare_part_type,
        bu.spare_part_model,
        bu.manufacturer,
        bu.remarks,
        bu.dept_code,
        bu.create_time,
        bu.create_dept,
        bu.create_user,
        bu.update_user,
        bu.update_time,
        bu.is_deleted,
        bbq.stock_count
        FROM
        `account_uav_spare_part` bu LEFT JOIN (SELECT spare_part_guid,sum( bus.stock_count ) stock_count FROM account_uav_spare_part_stock  bus GROUP BY spare_part_guid
        ) bbq ON bu.spare_part_guid = bbq.spare_part_guid
        <where>
            and bu.is_deleted = 0
            and bu.id = #{id}
        </where>
    </select>
</mapper>
