package com.allcore.account.power.service;

import com.allcore.account.power.dto.ExportTowerListDto;
import com.allcore.account.power.dto.UpdateWaitMaintainDto;
import com.allcore.account.power.dto.WaitMaintainDto;
import com.allcore.account.power.entity.WaitMaintainEntity;
import com.allcore.account.power.vo.AccreditListVo;
import com.allcore.account.power.vo.OutWaitMaintainLineVo;
import com.allcore.account.power.vo.WaitMaintainLineVO;
import com.allcore.account.power.vo.WaitMaintainTowerVO;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-11 09:39:16
 */
public interface WaitMaintainService extends ZxhcService<WaitMaintainEntity> {

	/**
	 * 代维列表分页
	 * @param dto
	 * @param page
	 * @return
	 */
	IPage<WaitMaintainLineVO> queryPage(WaitMaintainDto dto, IPage<WaitMaintainLineVO> page);

	/**
	 * 代维杆塔列表
	 * @param lineGuid
	 * @param page
	 * @return
	 */
	IPage<WaitMaintainTowerVO> queryTowerPage(String lineGuid, IPage<WaitMaintainTowerVO> page);

	/**
	 * 代维线路列表导出excel
	 * @param dto
	 * @return
	 */
	List<WaitMaintainLineVO> exportLineList(WaitMaintainDto dto);

	/**
	 * 代维杆塔列表导出excel
	 * @param dto
	 * @return
	 */
	List<WaitMaintainTowerVO> exportTowerList(ExportTowerListDto dto);

	/**
	 * 授权代维单位
	 * @param dto
	 */
	void updateWaitMaintain(UpdateWaitMaintainDto dto);

	/**
	 * 取消授权单位
	 * @param lineGuid
	 * @param waitDeptGuid
	 */
	void delWaitMaintainCompany(String lineGuid, String waitDeptGuid);

	/**
	 * 授权右侧列表
	 * @param waitDeptGuid
	 * @param deptGuid
	 * @param deptCode
	 * @param page
	 * @return
	 */
	IPage<AccreditListVo> getAccreditList(String waitDeptGuid, String deptGuid, String deptCode, String lineType, IPage<AccreditListVo> page);

	/**
	 * 代维左侧列表
	 * @param deptGuid
	 * @param deptCode
	 * @param lineName
	 * @param lineVoltageLevel
	 * @param page
	 * @return
	 */
	IPage<AccreditListVo> getBelongsList(String deptGuid, String deptCode,String lineType, String lineName, String lineVoltageLevel, IPage<AccreditListVo> page);

	R getDeptByCategory(Integer category);

	List<OutWaitMaintainLineVo> getWaitMaintainLine(String deptGuid);

	List<WaitMaintainEntity> getWaitInfoByLineGuid(String lineGuid);
}

