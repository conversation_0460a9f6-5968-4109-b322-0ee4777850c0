package com.allcore.account.power.service.impl;


import cn.hutool.core.io.resource.ClassPathResource;
import com.allcore.account.power.dto.*;
import com.allcore.account.power.entity.OutUnitEntity;
import com.allcore.account.power.entity.UavModelInfoEntity;
import com.allcore.account.power.entity.UavPlaneInfoEntity;
import com.allcore.account.power.entity.UavPlaneInfoFileEntity;
import com.allcore.account.power.enums.DictBizCodeEnum;
import com.allcore.account.power.excel.UavPlaneInfoExcel;
import com.allcore.account.power.excel.UavPlaneInfoWExcel;
import com.allcore.account.power.mapper.OutUnitMapper;
import com.allcore.account.power.mapper.UavModelInfoMapper;
import com.allcore.account.power.mapper.UavPlaneInfoFileMapper;
import com.allcore.account.power.mapper.UavPlaneInfoMapper;
import com.allcore.account.power.service.UavPlaneInfoService;
import com.allcore.account.power.vo.*;
import com.allcore.account.util.PoiUtils;
import com.allcore.common.base.FgPage;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.cache.ThreeDeptInfo;
import com.allcore.common.constant.MessageConstant;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.dict.entity.DictBiz;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.allcore.system.vo.DeptWithParentVO;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * @author: ldh
 * @date: 2022/9/13 19:41
 * @description: 无人机相关
 */
@Slf4j
@Service
public class UavPlaneInfoServiceImpl extends ZxhcServiceImpl<UavPlaneInfoMapper, UavPlaneInfoEntity> implements UavPlaneInfoService {

	@Resource
	private UavPlaneInfoMapper uavPlaneInfoMapper;

	@Resource
	private UavPlaneInfoFileMapper uavPlaneInfoFileMapper;

	@Resource
	private UavModelInfoMapper uavModelInfoMapper;

	@Resource
	private IOssClient ossClient;

	@Resource
	private OutUnitMapper outUnitMapper;

//	@Resource
//	private ZtClient ztClient;

	@Resource
	private ISysClient sysClient;

	@Resource
	private ApplicationContext applicationContext;

	String filePath = "allcore-business" + File.separator + "allcore-account" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "doc" + File.separator;


	@Override
	public R planePageList(UavPlaneInfoPageDTO dto, IPage<UavPlaneInfoPageVO> page) {
		List<UavPlaneInfoPageVO> list = uavPlaneInfoMapper.pageList(dto, page);
		for (UavPlaneInfoPageVO uavPlaneInfoPageVO : list) {
			ThreeDeptInfo info = CommonUtil.getThreeDeptNameByDeptId(uavPlaneInfoPageVO.getUavDeptId());
			uavPlaneInfoPageVO.setOperationUnit(info.getFirstName());
			uavPlaneInfoPageVO.setOperationCentre(info.getSecondName());
			uavPlaneInfoPageVO.setOperationTeam(info.getThirdName());
			OutUnitEntity outUnitEntity = outUnitMapper.selectOne(new QueryWrapper<OutUnitEntity>().lambda().eq(OutUnitEntity::getOutUnitGuid, uavPlaneInfoPageVO.getOutUnit()));
			uavPlaneInfoPageVO.setOutUnitName(outUnitEntity != null ? outUnitEntity.getUnitName() : null);
			uavPlaneInfoPageVO.setUavBrandName(DictBizCache.getValue(DictBizCodeEnum.UAV_BRAND.getCode(), uavPlaneInfoPageVO.getUavBrand()));
			uavPlaneInfoPageVO.setUavTypeName(DictBizCache.getValue(DictBizCodeEnum.UAV_TYPE.getCode(), uavPlaneInfoPageVO.getUavType()));
			if (StringUtils.isBlank(uavPlaneInfoPageVO.getState())) {
				uavPlaneInfoPageVO.setStateName(DictBizCache.getValue(DictBizCodeEnum.UAV_STATE.getCode(), "kx"));
			} else {
				uavPlaneInfoPageVO.setStateName(DictBizCache.getValue(DictBizCodeEnum.UAV_STATE.getCode(), uavPlaneInfoPageVO.getState()));
			}
			uavPlaneInfoPageVO.setAssetAttributeName(DictBizCache.getValue(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode(), uavPlaneInfoPageVO.getAssetAttribute()));
			String flyTime = uavPlaneInfoPageVO.getFlyTime();
			if (StringUtils.isNotBlank(flyTime)) {
				String s = new BigDecimal(flyTime).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).toString();
				uavPlaneInfoPageVO.setFlyTime(s);
			}
		}
		page.setRecords(list);
		return R.data(page);
	}

	@Override
	public R getPlanePageList(QjUavPlaneDto dto, IPage<QjUavPlaneVO> page) {
		if (StringUtils.isNotBlank(dto.getPlaneReceivingUnit())) {
			Dept dept = SysCache.getDept(dto.getPlaneReceivingUnit());
			dto.setDeptCode(dept.getDeptCode());
		}
		List<QjUavPlaneVO> list = uavPlaneInfoMapper.getPlanePageList(dto, page);
		for (QjUavPlaneVO qjUavPlaneVO : list) {
			ThreeDeptInfo info = CommonUtil.getThreeDeptNameByDeptId(qjUavPlaneVO.getUavDeptId());
			qjUavPlaneVO.setUnitGUID(info.getFirstGuid());
			qjUavPlaneVO.setReceivingUnitName(info.getFirstName());
			qjUavPlaneVO.setWorkAreaGUID(info.getSecondGuid());
			qjUavPlaneVO.setWorkAreaUnitName(info.getSecondName());
			qjUavPlaneVO.setTeamGUID(info.getThirdGuid());
			qjUavPlaneVO.setTeamUnitName(info.getThirdName());
			qjUavPlaneVO.setUnitName(info.getFirstName() + "-" + info.getSecondName() + "-" + info.getThirdName());
			String modelBrand = qjUavPlaneVO.getModelBrand();
			String planeUAVMake = qjUavPlaneVO.getPlaneUAVMake();
			String planeAssetAttributes = qjUavPlaneVO.getPlaneAssetAttributes();
			String modelType = qjUavPlaneVO.getModelType();
			String wrjztCode = qjUavPlaneVO.getPlaneReserve1();
			String modelBrandName = StringUtils.isNotBlank(modelBrand) ? DictBizCache.getValue(DictBizCodeEnum.UAV_BRAND.getCode(), modelBrand) : "";
			String planeUAVMakeName = StringUtils.isNotBlank(planeUAVMake) ? DictBizCache.getValue(DictBizCodeEnum.UAV_FACTORY.getCode(), planeUAVMake) : "";
			String planeAssetAttributesName = StringUtils.isNotBlank(planeAssetAttributes) ? DictBizCache.getValue(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode(), planeAssetAttributes) : "";
			String sysCodeName = StringUtils.isNotBlank(modelType) ? DictBizCache.getValue(DictBizCodeEnum.UAV_TYPE.getCode(), modelType) : "";
			String wrjzt = StringUtils.isNotBlank(modelType) ? DictBizCache.getValue(DictBizCodeEnum.UAV_STATE.getCode(), wrjztCode) : "";
			qjUavPlaneVO.setWrjzt(wrjzt);
			qjUavPlaneVO.setModelBrand(modelBrandName);
			qjUavPlaneVO.setPlaneUAVMake(planeUAVMakeName);
			qjUavPlaneVO.setPlaneAssetAttributesName(planeAssetAttributesName);
			qjUavPlaneVO.setSysCodeName(sysCodeName);
		}
		FgPage<QjUavPlaneVO> fgPage = new FgPage<>();
		fgPage.setCurrent(page.getCurrent());
		fgPage.setPages(page.getPages());
		fgPage.setSize(page.getSize());
		fgPage.setTotal(page.getTotal());
		fgPage.setRecords(list);
		return R.data(fgPage);
	}

	@Override
	public R updateUavPlaneInfo(UavPlaneInfoUpdateDTO dto) {
		// 查询无人机sncode是否变更
		QueryWrapper<UavPlaneInfoEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(UavPlaneInfoEntity::getPlaneGuid, dto.getPlaneGuid());
		UavPlaneInfoEntity entity = uavPlaneInfoMapper.selectOne(wrapper);
		if (entity == null) {
			return R.fail("无人机guid有误，未查询到无人机信息");
		}
		if (!entity.getSnCode().equals(dto.getSnCode())) {
			QueryWrapper<UavPlaneInfoEntity> snQuery = new QueryWrapper<>();
			snQuery.lambda().eq(UavPlaneInfoEntity::getSnCode, dto.getSnCode());
			Long count = uavPlaneInfoMapper.selectCount(snQuery);
			if (count > 0) {
				return R.fail(MessageConstant.SN_ERR);
			}
		}
		UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
		uavPlaneInfoEntity.setPlaneGuid(dto.getPlaneGuid());
		uavPlaneInfoEntity.setModelGuid(dto.getModelGuid());
		uavPlaneInfoEntity.setManufacturer(dto.getManufacturer());
		uavPlaneInfoEntity.setPlaneName(dto.getPlaneName());
		uavPlaneInfoEntity.setRealCertificationNumber(dto.getRealCertificationNumber());
		uavPlaneInfoEntity.setSnCode(dto.getSnCode());
		uavPlaneInfoEntity.setRegistrationTime(DateUtil.parse(dto.getRegistrationTime(), DateUtil.PATTERN_DATE));
		uavPlaneInfoEntity.setDeviceSource(dto.getDeviceSource());
		uavPlaneInfoEntity.setAssetAttribute(dto.getAssetAttribute());
		uavPlaneInfoEntity.setCustodian(dto.getCustodian());
		uavPlaneInfoEntity.setUavDeptId(dto.getUavDeptId());
		uavPlaneInfoEntity.setRemarks(dto.getRemarks());
		uavPlaneInfoEntity.setUavType(dto.getUavType());
		uavPlaneInfoEntity.setUavBrand(dto.getUavBrand());
		uavPlaneInfoEntity.setIsOut(dto.getIsOut());
		uavPlaneInfoEntity.setOutUnit(dto.getOutUnit());
		uavPlaneInfoEntity.setUpdateUser(AuthUtil.getUserId());
		uavPlaneInfoEntity.setUpdateTime(new Date());
		uavPlaneInfoEntity.setId(entity.getId());
		int update = uavPlaneInfoMapper.updateById(uavPlaneInfoEntity);
		int delete = uavPlaneInfoFileMapper.delete(new QueryWrapper<UavPlaneInfoFileEntity>().lambda().eq(UavPlaneInfoFileEntity::getPlaneGuid, dto.getPlaneGuid()));
		UavPlaneInfoFileEntity qrFile = new UavPlaneInfoFileEntity();
		qrFile.setFileGuid(dto.getQrCode());
		qrFile.setFileType("qr_code");
		qrFile.setPlaneGuid(uavPlaneInfoEntity.getPlaneGuid());
		int qrI = uavPlaneInfoFileMapper.insert(qrFile);
		List<String> planePicCodes = dto.getPlanePicCodes();
		for (String planePicCode : planePicCodes) {
			UavPlaneInfoFileEntity planeFile = new UavPlaneInfoFileEntity();
			planeFile.setFileGuid(planePicCode);
			planeFile.setFileType("plane_pic");
			planeFile.setPlaneGuid(uavPlaneInfoEntity.getPlaneGuid());
			uavPlaneInfoFileMapper.insert(planeFile);
		}
		if (update > 0) {
			return R.success("更新无人机信息成功");
		} else {
			return R.fail("更新无人机信息失败");
		}

	}

	@Override
	public R excelAdd(MultipartFile file, Integer isOut) {
		String deptCode = AuthUtil.getDeptCode();
		R<List<Dept>> deptLikeByDeptCode = sysClient.getDeptLikeByDeptCode(deptCode);
		List<Dept> deptChild = new ArrayList<>();
		if (deptLikeByDeptCode.isSuccess()) {
			deptChild = deptLikeByDeptCode.getData();
		} else {
			return R.fail("当前登录单位查询失败");
		}
		List<UavPlaneInfoEntity> infoEntities = new ArrayList<>();
		String tenantId = AuthUtil.getTenantId();
		if (isOut == 1) {
			List<UavPlaneInfoWExcel> wExcels = PoiUtils.readExcel(file, 0, UavPlaneInfoWExcel.class);
			Integer line = 1;
			for (UavPlaneInfoWExcel wExcel : wExcels) {
				UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
				line = line + 1;
				R r1 = checkUavInfoW(uavPlaneInfoEntity, line, wExcel);
				if (r1.isSuccess()) {
					uavPlaneInfoEntity = (UavPlaneInfoEntity) r1.getData();
				} else {
					return r1;
				}

				String outUnit = wExcel.getOutUnit();
				if (StringUtils.isBlank(outUnit)) {
					return R.fail("列表第" + line + "行，检测外包单位为空，请检查表格录入数据，修改后重新导入！");
				}
				List<OutUnitEntity> units = outUnitMapper.selectList(new QueryWrapper<OutUnitEntity>().lambda().eq(OutUnitEntity::getUnitName, outUnit));
				if (units.size() > 0) {
					uavPlaneInfoEntity.setOutUnit(units.get(0).getOutUnitGuid());
				} else {
					return R.fail("列表第" + line + "行，检测外包单位为不存在，请检查表格录入数据，修改后重新导入！");
				}
				String teamName = wExcel.getOperationTeam();
				String centerName = wExcel.getOperationCentre();
				String unitName = wExcel.getOperationUnit();
				R r = checkUnitInfo(line, teamName, centerName, unitName, tenantId);
				if (!r.isSuccess()) {
					return r;
				}
				String deptExcelId = r.getData().toString();
				final String deptExId = deptExcelId;
				if (deptChild.stream().filter(w -> String.valueOf(w.getId()).equals(deptExId)).findAny().isPresent()) {
					uavPlaneInfoEntity.setUavDeptId(deptExId);
					Dept dept = SysCache.getDept(deptExId);
					uavPlaneInfoEntity.setDeptCode(dept != null ? dept.getDeptCode() : null);
					uavPlaneInfoEntity.setSnCode(wExcel.getSnCode());
					uavPlaneInfoEntity.setPlaneName(wExcel.getPlanName());
					uavPlaneInfoEntity.setRemarks(wExcel.getRemark());
					uavPlaneInfoEntity.setIsDeleted(0);
					uavPlaneInfoEntity.setIsOut(1);
					uavPlaneInfoEntity.setPlaneGuid(CommonUtil.generateUuid());
					boolean exists = uavPlaneInfoMapper.exists(new QueryWrapper<UavPlaneInfoEntity>().lambda().eq(UavPlaneInfoEntity::getSnCode, wExcel.getSnCode()));
					if (!exists) {
						infoEntities.add(uavPlaneInfoEntity);
					}
				} else {
					return R.fail("列表第" + line + "行，检测最小运维单位信息，不在当前登录人单位管理范围内无法新增无人机数据！");
				}
			}
		}
		if (isOut == 0) {
			List<UavPlaneInfoExcel> excels = PoiUtils.readExcel(file, 0, UavPlaneInfoExcel.class);
			Integer line = 1;
			for (UavPlaneInfoExcel excel : excels) {
				UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
				line = line + 1;
				R r1 = checkUavInfo(uavPlaneInfoEntity, line, excel);
				if (r1.isSuccess()) {
					uavPlaneInfoEntity = (UavPlaneInfoEntity) r1.getData();
				} else {
					return r1;
				}
				String teamName = excel.getOperationTeam();
				String centerName = excel.getOperationCentre();
				String unitName = excel.getOperationUnit();
				R r = checkUnitInfo(line, teamName, centerName, unitName, tenantId);
				if (!r.isSuccess()) {
					return r;
				}
				String deptExcelId = r.getData().toString();
				final String deptExId = deptExcelId;
				if (deptChild.stream().filter(w -> String.valueOf(w.getId()).equals(deptExId)).findAny().isPresent()) {
					uavPlaneInfoEntity.setUavDeptId(deptExId);
					Dept dept = SysCache.getDept(deptExId);
					uavPlaneInfoEntity.setDeptCode(dept != null ? dept.getDeptCode() : null);
					uavPlaneInfoEntity.setSnCode(excel.getSnCode());
					uavPlaneInfoEntity.setPlaneName(excel.getPlanName());
					uavPlaneInfoEntity.setRemarks(excel.getRemark());
					uavPlaneInfoEntity.setIsDeleted(0);
					uavPlaneInfoEntity.setIsOut(0);
					uavPlaneInfoEntity.setPlaneGuid(CommonUtil.generateUuid());
					boolean exists = uavPlaneInfoMapper.exists(new QueryWrapper<UavPlaneInfoEntity>().lambda().eq(UavPlaneInfoEntity::getSnCode, excel.getSnCode()));
					if (!exists) {
						infoEntities.add(uavPlaneInfoEntity);
					}
				} else {
					return R.fail("列表第" + line + "行，检测最小运维单位信息，不在当前登录人单位管理范围内无法新增无人机数据！");
				}
			}
		}
		if (infoEntities.size() > 0) {
			int b = uavPlaneInfoMapper.insertDetailBatch(infoEntities);
			return R.success("上传无人机信息" + b + "条");
		} else {
			return R.fail("未导入无人机数据，检查无人机sn码是否已存在");
		}
	}

	private R checkUavInfoW(UavPlaneInfoEntity uavPlaneInfoEntity, Integer line, UavPlaneInfoWExcel excel) {
		String manufacturer = excel.getManufacturer();
		if (StringUtils.isBlank(manufacturer)) {
			return R.fail("列表第" + line + "行，检测制造厂商为空，请检查表格录入数据，修改后重新导入！");
		}
		String manufacturerCode = null;
		List<DictBiz> listManufacturer = DictBizCache.getList(DictBizCodeEnum.UAV_FACTORY.getCode());
		for (DictBiz dictBiz : listManufacturer) {
			if (dictBiz.getDictValue().equals(manufacturer)) {
				manufacturerCode = dictBiz.getDictKey();
			}
		}
		uavPlaneInfoEntity.setManufacturer(manufacturerCode);
		if (StringUtils.isBlank(manufacturerCode)) {
			return R.fail("列表第" + line + "行，检测未查询到制造厂商，请检查表格录入数据，修改后重新导入！");
		}
		String uavModel = excel.getUavModel();
		if (StringUtils.isBlank(uavModel)) {
			return R.fail("列表第" + line + "行，检测无人机型号为空，请检查表格录入数据，修改后重新导入！");
		}
		String uavType = excel.getUavType();
		if (StringUtils.isBlank(uavType)) {
			return R.fail("列表第" + line + "行，检测无人机类型为空，请检查表格录入数据，修改后重新导入！");
		}
		String uavTypeCode = null;
		List<DictBiz> list = DictBizCache.getList(DictBizCodeEnum.UAV_TYPE.getCode());
		for (DictBiz dictBiz : list) {
			if (dictBiz.getDictValue().equals(uavType)) {
				uavTypeCode = dictBiz.getDictKey();
			}
		}
		if (StringUtils.isBlank(uavTypeCode)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机类型，请检查表格录入数据，修改后重新导入！");
		}

		String deviceSource = excel.getDeviceSource();
		if (StringUtils.isBlank(deviceSource)) {
			return R.fail("列表第" + line + "行，检测设备来源为空，请检查表格录入数据，修改后重新导入！");
		}
		String deviceSourceCode = null;
		List<DictBiz> listDevice = DictBizCache.getList(DictBizCodeEnum.DEVICE_SOURCE.getCode());
		for (DictBiz dictBiz : listDevice) {
			if (dictBiz.getDictValue().equals(deviceSource)) {
				deviceSourceCode = dictBiz.getDictKey();
			}
		}

		if (StringUtils.isBlank(deviceSourceCode)) {
			return R.fail("列表第" + line + "行，检测未查询到设备来源，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setDeviceSource(deviceSourceCode);
		String custodian = excel.getCustodian();
		if (StringUtils.isBlank(custodian)) {
			return R.fail("列表第" + line + "行，检测保管人为空，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setCustodian(custodian);
		String uavBrand = excel.getUavBrand();
		if (StringUtils.isBlank(uavBrand)) {
			return R.fail("列表第" + line + "行，检测无人机品牌为空，请检查表格录入数据，修改后重新导入！");
		}

		String uavBrandCode = null;
		List<DictBiz> listBrand = DictBizCache.getList(DictBizCodeEnum.UAV_BRAND.getCode());
		for (DictBiz dictBiz : listBrand) {
			if (dictBiz.getDictValue().equals(uavBrand)) {
				uavBrandCode = dictBiz.getDictKey();
			}
		}

		if (StringUtils.isBlank(uavBrandCode)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机品牌，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setUavBrand(uavBrandCode);
		String assetAttribute = excel.getAssetAttribute();
		if (StringUtils.isBlank(assetAttribute)) {
			return R.fail("列表第" + line + "行，检测资产属性为空，请检查表格录入数据，修改后重新导入！");
		}
		String assetAttributeCode = null;
		List<DictBiz> listAsset = DictBizCache.getList(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode());
		for (DictBiz dictBiz : listAsset) {
			if (dictBiz.getDictValue().equals(assetAttribute)) {
				assetAttributeCode = dictBiz.getDictKey();
			}
		}
		if (StringUtils.isBlank(assetAttributeCode)) {
			return R.fail("列表第" + line + "行，检测未查询到资产属性，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setAssetAttribute(assetAttributeCode);
		String registrationTime = excel.getRegistrationTime();
		if (StringUtils.isBlank(registrationTime)) {
			return R.fail("列表第" + line + "行，检测注册时间为空，请检查表格录入数据，修改后重新导入！");
		}
		Date parse = DateUtil.parse(registrationTime, DateUtil.PATTERN_DATE);
		uavPlaneInfoEntity.setRegistrationTime(parse);
		String realCertificationNumber = excel.getRealCertificationNumber();
		if (StringUtils.isBlank(realCertificationNumber)) {
			return R.fail("列表第" + line + "行，检测实名认证编号为空，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setRealCertificationNumber(realCertificationNumber);
		String modelGuid = null;
		QueryWrapper<UavModelInfoEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(UavModelInfoEntity::getModelName, uavModel).eq(UavModelInfoEntity::getUavBrand, uavBrandCode).eq(UavModelInfoEntity::getBigUavType, uavTypeCode);
		List<UavModelInfoEntity> infoEntityList = uavModelInfoMapper.selectList(wrapper);
		if (infoEntityList.size() > 0) {
			modelGuid = infoEntityList.get(0).getModelGuid();
		}
		if (StringUtils.isBlank(modelGuid)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机型号库信息，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setModelGuid(modelGuid);
		return R.data(uavPlaneInfoEntity);
	}

	private R checkUavInfo(UavPlaneInfoEntity uavPlaneInfoEntity, Integer line, UavPlaneInfoExcel excel) {
		String manufacturer = excel.getManufacturer();
		if (StringUtils.isBlank(manufacturer)) {
			return R.fail("列表第" + line + "行，检测制造厂商为空，请检查表格录入数据，修改后重新导入！");
		}
		String manufacturerCode = null;
		List<DictBiz> listManufacturer = DictBizCache.getList(DictBizCodeEnum.UAV_FACTORY.getCode());
		for (DictBiz dictBiz : listManufacturer) {
			if (dictBiz.getDictValue().equals(manufacturer)) {
				manufacturerCode = dictBiz.getDictKey();
			}
		}
		uavPlaneInfoEntity.setManufacturer(manufacturerCode);
		if (StringUtils.isBlank(manufacturerCode)) {
			return R.fail("列表第" + line + "行，检测未查询到制造厂商，请检查表格录入数据，修改后重新导入！");
		}
		String uavModel = excel.getUavModel();
		if (StringUtils.isBlank(uavModel)) {
			return R.fail("列表第" + line + "行，检测无人机型号为空，请检查表格录入数据，修改后重新导入！");
		}
		String uavType = excel.getUavType();
		if (StringUtils.isBlank(uavType)) {
			return R.fail("列表第" + line + "行，检测无人机类型为空，请检查表格录入数据，修改后重新导入！");
		}
		String uavTypeCode = null;
		List<DictBiz> list = DictBizCache.getList(DictBizCodeEnum.UAV_TYPE.getCode());
		for (DictBiz dictBiz : list) {
			if (dictBiz.getDictValue().equals(uavType)) {
				uavTypeCode = dictBiz.getDictKey();
			}
		}
		if (StringUtils.isBlank(uavTypeCode)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机类型，请检查表格录入数据，修改后重新导入！");
		}

		String deviceSource = excel.getDeviceSource();
		if (StringUtils.isBlank(deviceSource)) {
			return R.fail("列表第" + line + "行，检测设备来源为空，请检查表格录入数据，修改后重新导入！");
		}
		String deviceSourceCode = null;
		List<DictBiz> listDevice = DictBizCache.getList(DictBizCodeEnum.DEVICE_SOURCE.getCode());
		for (DictBiz dictBiz : listDevice) {
			if (dictBiz.getDictValue().equals(deviceSource)) {
				deviceSourceCode = dictBiz.getDictKey();
			}
		}

		if (StringUtils.isBlank(deviceSourceCode)) {
			return R.fail("列表第" + line + "行，检测未查询到设备来源，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setDeviceSource(deviceSourceCode);
		String custodian = excel.getCustodian();
		if (StringUtils.isBlank(custodian)) {
			return R.fail("列表第" + line + "行，检测保管人为空，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setCustodian(custodian);
		String uavBrand = excel.getUavBrand();
		if (StringUtils.isBlank(uavBrand)) {
			return R.fail("列表第" + line + "行，检测无人机品牌为空，请检查表格录入数据，修改后重新导入！");
		}

		String uavBrandCode = null;
		List<DictBiz> listBrand = DictBizCache.getList(DictBizCodeEnum.UAV_BRAND.getCode());
		for (DictBiz dictBiz : listBrand) {
			if (dictBiz.getDictValue().equals(uavBrand)) {
				uavBrandCode = dictBiz.getDictKey();
			}
		}

		if (StringUtils.isBlank(uavBrandCode)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机品牌，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setUavBrand(uavBrandCode);
		String assetAttribute = excel.getAssetAttribute();
		if (StringUtils.isBlank(assetAttribute)) {
			return R.fail("列表第" + line + "行，检测资产属性为空，请检查表格录入数据，修改后重新导入！");
		}
		String assetAttributeCode = null;
		List<DictBiz> listAsset = DictBizCache.getList(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode());
		for (DictBiz dictBiz : listAsset) {
			if (dictBiz.getDictValue().equals(assetAttribute)) {
				assetAttributeCode = dictBiz.getDictKey();
			}
		}
		if (StringUtils.isBlank(assetAttributeCode)) {
			return R.fail("列表第" + line + "行，检测未查询到资产属性，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setAssetAttribute(assetAttributeCode);
		String registrationTime = excel.getRegistrationTime();
		if (StringUtils.isBlank(registrationTime)) {
			return R.fail("列表第" + line + "行，检测注册时间为空，请检查表格录入数据，修改后重新导入！");
		}
		Date parse = DateUtil.parse(registrationTime, DateUtil.PATTERN_DATE);
		uavPlaneInfoEntity.setRegistrationTime(parse);
		String realCertificationNumber = excel.getRealCertificationNumber();
		if (StringUtils.isBlank(realCertificationNumber)) {
			return R.fail("列表第" + line + "行，检测实名认证编号为空，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setRealCertificationNumber(realCertificationNumber);
		String modelGuid = null;
		QueryWrapper<UavModelInfoEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(UavModelInfoEntity::getModelName, uavModel).eq(UavModelInfoEntity::getUavBrand, uavBrandCode).eq(UavModelInfoEntity::getBigUavType, uavTypeCode);
		List<UavModelInfoEntity> infoEntityList = uavModelInfoMapper.selectList(wrapper);
		if (infoEntityList.size() > 0) {
			modelGuid = infoEntityList.get(0).getModelGuid();
		}
		if (StringUtils.isBlank(modelGuid)) {
			return R.fail("列表第" + line + "行，检测未查询到无人机型号库信息，请检查表格录入数据，修改后重新导入！");
		}
		uavPlaneInfoEntity.setModelGuid(modelGuid);
		return R.data(uavPlaneInfoEntity);
	}

	private R checkUnitInfo(Integer line, String teamName, String centerName, String unitName, String tenantId) {
		// 运维单位判断
		if (StringUtils.isBlank(unitName)) {
			return R.fail("列表第" + line + "行，检测运维单位为空，请检查表格录入数据，修改后重新导入！");
		}
		// 单位中心班组判断
		if (StringUtils.isNotBlank(teamName) && StringUtils.isBlank(centerName)) {
			return R.fail("列表第" + line + "行，检测运维中心为空，运维班组不为空,请检查表格录入数据，修改后重新导入！");
		}
		String deptExcelId = null;
		if (StringUtils.isNotBlank(teamName)) {
			R<List<DeptWithParentVO>> deptWithParent = sysClient.getDeptWithParent(tenantId, teamName);
			if (deptWithParent.isSuccess()) {
				R<String> excelUavDeptId = getExcelUavDeptId(deptWithParent.getData(), teamName, centerName, unitName, tenantId);
				if (!excelUavDeptId.isSuccess()) {
					return R.fail("列表第" + line + "行，" + excelUavDeptId.getMsg() + "，请检查表格录入数据，修改后重新导入！");
				} else {
					deptExcelId = excelUavDeptId.getData();
				}
			} else {
				return R.fail("列表第" + line + "行，检测运维班组不存在，请检查表格录入数据，修改后重新导入！");
			}
		}
		if (StringUtils.isNotBlank(centerName) && StringUtils.isBlank(teamName)) {
			R<List<DeptWithParentVO>> deptWithParent = sysClient.getDeptWithParent(tenantId, centerName);
			if (deptWithParent.isSuccess()) {
				R<String> excelUavDeptId = getExcelUavDeptId(deptWithParent.getData(), teamName, centerName, unitName, tenantId);
				if (!excelUavDeptId.isSuccess()) {
					return R.fail("列表第" + line + "行，" + excelUavDeptId.getMsg() + "，请检查表格录入数据，修改后重新导入！");
				} else {
					deptExcelId = excelUavDeptId.getData();
				}
			} else {
				return R.fail("列表第" + line + "行，检测运维中心不存在，请检查表格录入数据，修改后重新导入！");
			}
		}
		if (StringUtils.isBlank(centerName) && StringUtils.isBlank(teamName) && StringUtils.isNotBlank(unitName)) {
			R<List<DeptWithParentVO>> deptWithParent = sysClient.getDeptWithParent(tenantId, unitName);
			if (deptWithParent.isSuccess()) {
				List<DeptWithParentVO> data = deptWithParent.getData();
				if (data.size() <= 0) {
					return R.fail("列表第" + line + "行，检测单位不存在，请检查表格录入数据，修改后重新导入！");
				}
				R<String> excelUavDeptId = getExcelUavDeptId(data, teamName, centerName, unitName, tenantId);
				deptExcelId = excelUavDeptId.getData();
			} else {
				return R.fail("列表第" + line + "行，检测运维单位不存在，请检查表格录入数据，修改后重新导入！");
			}
		}
		return R.data(deptExcelId);
	}

	private R<String> getExcelUavDeptId(List<DeptWithParentVO> deptVos, String team, String centre, String unit, String tenantId) {
		List<DeptWithParentVO> depts = new ArrayList<>();
		if (StringUtils.isNotBlank(team)) {
			for (DeptWithParentVO datum : deptVos) {
				if (datum.getParentDeptName().equals(centre)) {
					depts.add(datum);
				}
			}
			if (depts.size() <= 0) {
				return R.fail("检测运维中心不存在");
			}
			if (depts.size() == 1) {
				DeptWithParentVO deptWithParentVO = depts.get(0);
				R<List<DeptWithParentVO>> deptWithParent = sysClient.getDeptWithParent(tenantId, centre);
				if (deptWithParent.isSuccess()) {
					for (DeptWithParentVO datum : deptWithParent.getData()) {
						if (datum.getParentDeptName().equals(unit)) {
							return R.data(deptWithParentVO.getId());
						}
					}
					return R.fail("检测运维单位不存在");
				}
			}
			if (depts.size() > 1) {
				// 二次过滤
				DeptWithParentVO deptVo = new DeptWithParentVO();
				R<List<DeptWithParentVO>> deptWithParent = sysClient.getDeptWithParent(tenantId, centre);
				if (deptWithParent.isSuccess()) {
					for (DeptWithParentVO datum : deptWithParent.getData()) {
						if (datum.getParentDeptName().equals(unit)) {
							deptVo = datum;
						}
					}
					if (StringUtils.isEmpty(deptVo.getDeptName())) {
						return R.fail("检测运维单位不存在");
					}
				}
				for (DeptWithParentVO datum : depts) {
					if (datum.getParentId().equals(deptVo.getId())) {
						return R.data(datum.getId());
					}
				}
			}
		}
		if (StringUtils.isBlank(team) && StringUtils.isNotBlank(centre)) {
			for (DeptWithParentVO datum : deptVos) {
				if (datum.getParentDeptName().equals(unit)) {
					depts.add(datum);
				}
			}
			if (depts.size() <= 0) {
				return R.fail("检测运维单位不存在");
			} else {
				return R.data(depts.get(0).getId());
			}
		}
		if (StringUtils.isBlank(team) && StringUtils.isBlank(centre) && StringUtils.isNotBlank(unit)) {
			return R.data(deptVos.get(0).getId());
		}
		return R.fail("检测运维中心或单位不存在");
	}

	@Override
	public R getPlaneDetail(String planeGuid) {
		QueryWrapper<UavPlaneInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(UavPlaneInfoEntity::getPlaneGuid, planeGuid);
		UavPlaneInfoEntity uavPlaneInfoEntity = uavPlaneInfoMapper.selectOne(queryWrapper);
		if (uavPlaneInfoEntity == null) {
			return R.fail("无人机信息查询失败");
		}
		UavPlaneInfoVO uavPlaneInfoVO = new UavPlaneInfoVO();
		BeanUtils.copyProperties(uavPlaneInfoEntity, uavPlaneInfoVO);
		uavPlaneInfoVO.setDeptName(SysCache.getDeptNameByDeptCode(uavPlaneInfoEntity.getDeptCode()));
		if (uavPlaneInfoEntity.getRegistrationTime() != null) {
			uavPlaneInfoVO.setRegistrationTime(DateUtil.format(uavPlaneInfoEntity.getRegistrationTime(), DateUtil.PATTERN_DATETIME));
		}
		uavPlaneInfoVO.setRemarks(uavPlaneInfoEntity.getRemarks());
		uavPlaneInfoVO.setAssetAttributeName(DictBizCache.getValue(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode(), uavPlaneInfoEntity.getAssetAttribute()));
		uavPlaneInfoVO.setDeviceSourceName(DictBizCache.getValue(DictBizCodeEnum.DEVICE_SOURCE.getCode(), uavPlaneInfoEntity.getDeviceSource()));
		// 查询关联图片
		QueryWrapper<UavPlaneInfoFileEntity> fileQueryWrapper = new QueryWrapper<>();
		fileQueryWrapper.lambda().eq(UavPlaneInfoFileEntity::getPlaneGuid, uavPlaneInfoEntity.getPlaneGuid());
		List<UavPlaneInfoFileEntity> uavPlaneInfoFiles = uavPlaneInfoFileMapper.selectList(fileQueryWrapper);
		List<UavPicVO> picUrl = new ArrayList<>();
		List<UavPicVO> qrUrl = new ArrayList<>();
		for (UavPlaneInfoFileEntity uavPlaneInfoFile : uavPlaneInfoFiles) {
			AllcoreFileVO fileDetail = ossClient.getFileDetail(uavPlaneInfoFile.getFileGuid()).getData();
			if (fileDetail != null) {
				UavPicVO uavPicVO = new UavPicVO();
				if ("qr_code".equals(uavPlaneInfoFile.getFileType())) {
					uavPicVO.setUrl(fileDetail.getStaticPath());
					uavPicVO.setGuid(uavPlaneInfoFile.getFileGuid());
					qrUrl.add(uavPicVO);
				}
				if ("plane_pic".equals(uavPlaneInfoFile.getFileType())) {
					uavPicVO.setUrl(fileDetail.getStaticPath());
					uavPicVO.setGuid(uavPlaneInfoFile.getFileGuid());
					picUrl.add(uavPicVO);
				}
			}
		}
		uavPlaneInfoVO.setQrCodeUrl(qrUrl);
		uavPlaneInfoVO.setPlanePicCodesUrls(picUrl);
		QueryWrapper<UavModelInfoEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(UavModelInfoEntity::getModelGuid, uavPlaneInfoEntity.getModelGuid());
		UavModelInfoEntity one = uavModelInfoMapper.selectOne(wrapper);
		if (one != null) {
			uavPlaneInfoVO.setUavType(one.getBigUavType());
			uavPlaneInfoVO.setUavBrand(one.getUavBrand());
			uavPlaneInfoVO.setModelName(one.getModelName());
		}
		OutUnitEntity outUnitEntity = outUnitMapper.selectOne(new QueryWrapper<OutUnitEntity>().lambda().eq(OutUnitEntity::getOutUnitGuid, uavPlaneInfoVO.getOutUnit()));
		uavPlaneInfoVO.setOutUnitName(outUnitEntity != null ? outUnitEntity.getUnitName() : null);

//		if (ZtSwitch.ToZhongTai) {
//			// 通过上下文获取该类的实例，再调用该实例方法
//			UavPlaneInfoService service = applicationContext.getBean(UavPlaneInfoService.class);
//			//查看无人机详情 触发 重庆中台21-3、重庆中台21-4
//			service.sendZtSmartDeviceQuery(planeGuid);
//		}

		return R.data(uavPlaneInfoVO);
	}

//	@Override
//	@Async
//	public void sendZtSmartDeviceQuery(String planeGuid) {
//
//		try {
//			log.info("开始调用智能装备台帐查看、查询接口--------------------------");
//			ztClient.SmartDeviceQuery(planeGuid);
//			ztClient.findSmartDevice(planeGuid);
//			log.info("结束调用智能装备台帐查看、查询接口--------------------------");
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//	}

	@Override
	public List<UavPlaneInfoEntity> getAllPlaneInfoByDept() {
		QueryWrapper<UavPlaneInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().like(UavPlaneInfoEntity::getDeptCode, AuthUtil.getDeptCode());
		queryWrapper.lambda().isNotNull(UavPlaneInfoEntity::getSnCode);
		return uavPlaneInfoMapper.selectList(queryWrapper);
	}

	@Override
	public R deletePlanInfo(String planGuid) {
		QueryWrapper<UavPlaneInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(UavPlaneInfoEntity::getPlaneGuid, planGuid);
		List<UavPlaneInfoEntity> entities = uavPlaneInfoMapper.selectList(queryWrapper);
		if (entities.size() > 0) {
			UpdateWrapper<UavPlaneInfoEntity> updateWrapper = new UpdateWrapper<>();
			updateWrapper.lambda().eq(UavPlaneInfoEntity::getPlaneGuid, planGuid).set(UavPlaneInfoEntity::getIsDeleted, 1).set(UavPlaneInfoEntity::getSnCode, entities.get(0).getSnCode() + "-" + CommonUtil.generateUuid());
			int update = uavPlaneInfoMapper.update(null, updateWrapper);
			if (update > 0) {
				return R.success("删除无人机信息成功");
			} else {
				return R.fail("删除无人机信息失败");
			}
		}
		return R.fail("无人机不存在");
	}

	@Override
	public R saveUavInfo(UavPlaneInfoDTO dto) {
		QueryWrapper<UavPlaneInfoEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(UavPlaneInfoEntity::getSnCode, dto.getSnCode()).eq(UavPlaneInfoEntity::getIsDeleted, 0);
		List<UavPlaneInfoEntity> list = uavPlaneInfoMapper.selectList(wrapper);
		if (list.size() > 0) {
			return R.fail(MessageConstant.SN_ERR);
		}
		UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
		BeanUtils.copyProperties(dto, uavPlaneInfoEntity);
		uavPlaneInfoEntity.setRegistrationTime(DateUtil.parse(dto.getRegistrationTime(), DateUtil.PATTERN_DATETIME));
		uavPlaneInfoEntity.setPlaneGuid(CommonUtil.generateUuid());
		uavPlaneInfoEntity.setState("kx");
		Dept deptByDeptCode = SysCache.getDeptByDeptCode(dto.getDeptCode());
		uavPlaneInfoEntity.setUavDeptId(deptByDeptCode != null ? deptByDeptCode.getId() : null);
		int insert = uavPlaneInfoMapper.insert(uavPlaneInfoEntity);
		UavPlaneInfoFileEntity qrFile = new UavPlaneInfoFileEntity();
		qrFile.setFileGuid(dto.getQrCode());
		qrFile.setFileType("qr_code");
		qrFile.setPlaneGuid(uavPlaneInfoEntity.getPlaneGuid());
		int qrI = uavPlaneInfoFileMapper.insert(qrFile);
		List<String> planePicCodes = dto.getPlanePicCodes();
		for (String planePicCode : planePicCodes) {
			UavPlaneInfoFileEntity planeFile = new UavPlaneInfoFileEntity();
			planeFile.setFileGuid(planePicCode);
			planeFile.setFileType("plane_pic");
			planeFile.setPlaneGuid(uavPlaneInfoEntity.getPlaneGuid());
			uavPlaneInfoFileMapper.insert(planeFile);
		}

//		/*调用中台*/
//		UavPlaneInfoService service = applicationContext.getBean(UavPlaneInfoService.class);
//		/*调用中台*/
//		service.sendZtAddSmartDevice(uavPlaneInfoEntity);

		if (insert > 0) {
			return R.success("保存无人机信息成功");
		} else {
			return R.fail("保存无人机信息失败");
		}
	}

//	/**
//	 * （中台接口智能装备台帐新增）
//	 * <AUTHOR>
//	 * @date 2022/09/27 11:23
//	 * @param entity
//	 * @return void
//	 */
//	@Async
//	@Override
//	public void sendZtAddSmartDevice(UavPlaneInfoEntity entity) {
//
//		UavModelInfoEntity model = uavModelInfoService.getOne(
//			new QueryWrapper<UavModelInfoEntity>().lambda()
//				.eq(UavModelInfoEntity::getModelGuid, entity.getModelGuid())
//		);
//
//		TbUavPlaneinfoDTO ztDto = new TbUavPlaneinfoDTO();
//		ztDto.setPlaneguid(entity.getPlaneGuid());
//		ztDto.setPlaneuavmake(entity.getManufacturer());
//		ztDto.setPlaneuavmodel(model.getModelName());
//		ztDto.setPlaneequipmentno(entity.getSnCode());
//		ztDto.setPlaneregistertime(DateUtil.format(entity.getRegistrationTime(),DateUtil.PATTERN_DATETIME));
//		ztDto.setPlaneassetno(entity.getRealCertificationNumber());
//		ztDto.setPlanepurchasetime(DateUtil.format(entity.getRegistrationTime(),DateUtil.PATTERN_DATETIME));
//		DeptVO deptVO = sysClient.getDeptInfoByDeptCode(entity.getDeptCode());
//
//		ThreeDeptInfo deptInfo = CommonUtil.getThreeDeptNameByDeptId(deptVO.getId());
//		ztDto.setPlanereceivingunit(deptInfo.getFirstGuid());
//		ztDto.setPlaneequipmentsource(DictBizCache.getValue(DictBizCodeEnum.DEVICE_SOURCE.getCode(), entity.getDeviceSource()));
//		ztDto.setPlanefactorytime(DateUtil.format(entity.getRegistrationTime(),DateUtil.PATTERN_DATETIME));
//		ztDto.setTeamguid(deptInfo.getThirdGuid());
//
//		if (ChongQingZhongTaiSwitch.ToZhongTai) {
//			try {
//				log.info("开始调用中台接口智能装备台帐新增开始------------------------------");
//				ztClient.addSmartDevice(ztDto);
//				log.info("开始调用中台接口智能装备台帐新增结束------------------------------");
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//	}

	@Override
	public R getPlaneInfoBySnCode(String snCode) {
		List<UavPlaneInfoEntity> planeInfoVO = uavPlaneInfoMapper.selectList(new QueryWrapper<UavPlaneInfoEntity>().lambda().eq(UavPlaneInfoEntity::getSnCode, snCode).eq(UavPlaneInfoEntity::getIsDeleted, 0));
		UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
		if (planeInfoVO.size() > 0) {
			uavPlaneInfoEntity = planeInfoVO.get(0);
		}
		return R.data(uavPlaneInfoEntity);
	}

	@Override
	public R<UavPlaneInfoEntity> saveAppUavInfo(AppUavPlaneInfoDTO uavPlaneInfoDTO) {
		String userGuid = uavPlaneInfoDTO.getUserGuid();
		User user = UserCache.getUser(userGuid);
		UavPlaneInfoEntity uavPlaneInfoEntity = new UavPlaneInfoEntity();
		uavPlaneInfoEntity.setIsOut(uavPlaneInfoDTO.getIsOut());
		if (user != null) {
			uavPlaneInfoEntity.setUavDeptId(user.getDeptId());
			Dept dept = SysCache.getDept(user.getDeptId());
			uavPlaneInfoEntity.setDeptCode(dept != null ? dept.getDeptCode() : null);
		}
		String modelName = uavPlaneInfoDTO.getModelName();
		if (StringUtils.isNotBlank(modelName)) {
			List<UavModelInfoEntity> uavModelInfoEntities = uavModelInfoMapper.selectList(new QueryWrapper<UavModelInfoEntity>().lambda().eq(UavModelInfoEntity::getModelName, modelName).eq(UavModelInfoEntity::getIsDeleted, 0));
			if (uavModelInfoEntities.size() > 0) {
				uavPlaneInfoEntity.setModelGuid(uavModelInfoEntities.get(0).getModelGuid());
			}
		}
		String snCode = uavPlaneInfoDTO.getSnCode();
		List<UavPlaneInfoEntity> list = uavPlaneInfoMapper.selectList(new QueryWrapper<UavPlaneInfoEntity>().lambda().eq(UavPlaneInfoEntity::getSnCode, snCode).eq(UavPlaneInfoEntity::getIsDeleted, 0));
		if (list.size() > 0) {
			UavPlaneInfoEntity planeInfoEntity = list.get(0);
			if (StringUtils.isBlank(planeInfoEntity.getPlaneName())) {
				int update = uavPlaneInfoMapper.update(null, new UpdateWrapper<UavPlaneInfoEntity>().lambda().set(UavPlaneInfoEntity::getPlaneName, "无人机自动注册_" + snCode).eq(UavPlaneInfoEntity::getPlaneGuid, planeInfoEntity.getPlaneGuid()));
			}
			return R.fail("无人机snCode已存在,无法新增");
		}
		uavPlaneInfoEntity.setSnCode(snCode);
		uavPlaneInfoEntity.setPlaneGuid(CommonUtil.getGuid());
		uavPlaneInfoEntity.setState("kx");
		uavPlaneInfoEntity.setPlaneName("无人机自动注册_" + snCode);
		int insert = uavPlaneInfoMapper.insert(uavPlaneInfoEntity);
		if (insert > 0) {
			UavPlaneInfoEntity newUavPlane = uavPlaneInfoMapper.selectOne(new QueryWrapper<UavPlaneInfoEntity>().lambda().eq(UavPlaneInfoEntity::getSnCode, snCode).eq(UavPlaneInfoEntity::getIsDeleted, 0));
			return R.data(newUavPlane);
		}
		return R.fail("新增无人机信息失败");
	}


	/**
	 * 下载备案信息
	 *
	 * @param planeGuids 飞机guid
	 * @param response   响应
	 */
	@Override
	public void getUavPoliceInfo(List<String> planeGuids, HttpServletResponse response) {
		List<UavWordInfoVO> uavPoliceInfo = uavPlaneInfoMapper.getUavPoliceInfo(planeGuids);
		if (uavPoliceInfo.size() < 1) {
			return;
		}
		try {
			HashMap<String, Object> map = new HashMap<>();
			Map<String, InputStream> mapDocs = new HashMap<>();
			int i = 0;
			for (UavWordInfoVO uavWordInfoVO : uavPoliceInfo) {
				XWPFTemplate template = null;
				ByteArrayOutputStream out = null;
				String uavType = DictBizCache.getValue("uav_type", uavWordInfoVO.getBigUavType());
				map.put("uav_type", uavType);
				String uavBrandName = DictBizCache.getValue("uav_brand", uavWordInfoVO.getUavBrand());
				String manufacturer = DictBizCache.getValue(DictBizCodeEnum.UAV_FACTORY.getCode(), uavWordInfoVO.getManufacturer());
				map.put("uav_brand_name", uavBrandName);
				String deptName = SysCache.getDeptName(uavWordInfoVO.getUavDeptId());
				map.put("dept_name", deptName);
				map.put("sn_code", uavWordInfoVO.getSnCode());
				map.put("model_name", uavWordInfoVO.getModelName());
				map.put("manufacturer", manufacturer);
				map.put("max_speed", uavWordInfoVO.getMaxSpeed());
				map.put("max_flight_altitude", uavWordInfoVO.getMaxFlightAltitude());
				map.put("max_takeoff_weight", uavWordInfoVO.getMaxTakeoffWeight());
				map.put("empty_weight", uavWordInfoVO.getEmptyWeight());
				// 机身图片数据
				List<Map<String, Object>> jsPic = new ArrayList<>();
				QueryWrapper<UavPlaneInfoFileEntity> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().eq(StringUtils.isNotBlank(uavWordInfoVO.getPlaneGuid()), UavPlaneInfoFileEntity::getPlaneGuid, uavWordInfoVO.getPlaneGuid());
				List<UavPlaneInfoFileEntity> planeInfoFiles = uavPlaneInfoFileMapper.selectList(queryWrapper);
				for (UavPlaneInfoFileEntity planeInfoFile : planeInfoFiles) {
					AllcoreFileVO data = ossClient.getFileDetail(planeInfoFile.getFileGuid()).getData();
					if (data != null) {
						if ("plane_pic".equals(planeInfoFile.getFileType())) {
							Map<String, Object> map1 = new HashMap<>();
							map1.put("jsImg", Pictures.ofUrl(data.getStaticPath(), PictureType.JPEG).size(200, 100).center().create());
							jsPic.add(map1);
						}
						if ("qr_code".equals(planeInfoFile.getFileType())) {
							map.put("qrImg", Pictures.ofUrl(data.getStaticPath(), PictureType.JPEG).size(200, 100).center().create());
						}
					}
				}
				map.put("jsImgs", jsPic);
				Configure config = Configure.builder().bind("jsImgs", new LoopRowTableRenderPolicy()).build();
				String path = new ClassPathResource(filePath + "gabamxmb.docx").getPath();
				File file = new File(path);
				System.out.println("路径输出" + path);
				template = XWPFTemplate.compile(file, config).render(map);
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				template.write(baos);
				InputStream inputStream = new ByteArrayInputStream(baos.toByteArray());
				template.close();
				mapDocs.put("无人机公安备案(" + uavWordInfoVO.getPlaneName() + "-" + i + ").docx", inputStream);
			}
			PoiUtils.downInputStreamZip(mapDocs, response);
			response.flushBuffer();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public R findListByUavTypeAndModelGuid(String deptCode, String uavType, String modelGuid, Integer isOut) {
		List<UavPlaneNameVO> list = uavPlaneInfoMapper.findListByUavTypeAndModelGuid(deptCode, uavType, modelGuid, isOut);
		return R.data(list);
	}

	@Override
	public R getPlaneListByGuids(List<String> planeGuids) {
		List<UavPlaneInfoVO> list = uavPlaneInfoMapper.getPlaneListByGuids(planeGuids);
		for (UavPlaneInfoVO uavPlaneInfoVO : list) {
			uavPlaneInfoVO.setDeptName(SysCache.getDeptNameByDeptCode(uavPlaneInfoVO.getDeptCode()));
			uavPlaneInfoVO.setUavBrandName(DictBizCache.getValue(DictBizCodeEnum.UAV_BRAND.getCode(), uavPlaneInfoVO.getUavBrand()));
			String uavType = uavPlaneInfoVO.getUavType();
			uavPlaneInfoVO.setUavTypeName(uavType != null ? DictBizCache.getValue(DictBizCodeEnum.UAV_TYPE.getCode(), uavType) : "-");
			uavPlaneInfoVO.setDeviceSourceName(DictBizCache.getValue(DictBizCodeEnum.DEVICE_SOURCE.getCode(), uavPlaneInfoVO.getDeviceSource()));
			uavPlaneInfoVO.setAssetAttributeName(DictBizCache.getValue(DictBizCodeEnum.UAV_ASSET_ATTRIBUTES.getCode(), uavPlaneInfoVO.getAssetAttribute()));
			uavPlaneInfoVO.setManufacturerName(DictBizCache.getValue(DictBizCodeEnum.UAV_FACTORY.getCode(), uavPlaneInfoVO.getManufacturer()));
		}
		return R.data(list);
	}


	/**
	 * 重命名无人机名称
	 *
	 * @param sn   无人机SN码
	 * @param name 无人机名称
	 */
	@Override
	public UavPlaneInfoEntity updateUavNameBySn(String sn, String name) {
		QueryWrapper<UavPlaneInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(UavPlaneInfoEntity::getSnCode, sn);
		UavPlaneInfoEntity uavPlaneInfoEntity = uavPlaneInfoMapper.selectOne(queryWrapper);
		if (uavPlaneInfoEntity == null) {
			throw new ServiceException("未找到无人机，修改名称失败");
		}
		String id = uavPlaneInfoEntity.getId();
		uavPlaneInfoEntity = new UavPlaneInfoEntity();
		uavPlaneInfoEntity.setId(id);
		uavPlaneInfoEntity.setPlaneName(name);
		uavPlaneInfoMapper.updateById(uavPlaneInfoEntity);
		return uavPlaneInfoEntity;
	}
}
