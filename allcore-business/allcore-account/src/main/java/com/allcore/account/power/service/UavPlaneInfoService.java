package com.allcore.account.power.service;


import com.allcore.account.power.dto.*;
import com.allcore.account.power.entity.UavPlaneInfoEntity;
import com.allcore.account.power.vo.QjUavPlaneVO;
import com.allcore.account.power.vo.UavPlaneInfoPageVO;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 无人机表
 *
 * <AUTHOR>
 * @email
 * @date 2022-04-18 09:39:34
 */
public interface UavPlaneInfoService extends ZxhcService<UavPlaneInfoEntity> {

	/**
	 * 无人机设备分页列表
	 *
	 * @param page
	 * @param dto
	 * @return
	 */
	R planePageList(UavPlaneInfoPageDTO dto, IPage<UavPlaneInfoPageVO> page);

	/**
	 * 全景无人机设备分页列表
	 *
	 * @param page
	 * @param dto
	 * @return
	 */
	R getPlanePageList(QjUavPlaneDto dto, IPage<QjUavPlaneVO> page);

	/**
	 * 更新无人机平面信息
	 *
	 * @param entity 实体
	 * @return {@link R}
	 */
	R updateUavPlaneInfo(UavPlaneInfoUpdateDTO entity);

	/**
	 * excel添加
	 *
	 * @param file  文件
	 * @param isOut 是不
	 * @return {@link R}
	 */
	R excelAdd(MultipartFile file, Integer isOut);

	/**
	 * 让飞机细节
	 *
	 * @param planeGuid 飞机guid
	 * @return {@link R}
	 */
	R getPlaneDetail(String planeGuid);

	/**
	 * 删除计划信息
	 *
	 * @param planGuid 计划guid
	 * @return {@link R}
	 */
	R deletePlanInfo(String planGuid);

	/**
	 * 保存无人机信息
	 *
	 * @param dto dto
	 * @return {@link R}
	 */
	R saveUavInfo(UavPlaneInfoDTO dto);

	/**
	 * 得到无人机警察信息
	 *
	 * @param planeGuid 飞机guid
	 * @param response  响应
	 */
	void getUavPoliceInfo(List<String> planeGuid, HttpServletResponse response);

	/**
	 * 找到列表通过无人机类型和模型guid
	 *
	 * @param deptCode  部门代码
	 * @param uavType   无人机类型
	 * @param modelGuid 模型guid
	 * @return {@link R}
	 */
	R findListByUavTypeAndModelGuid(String deptCode, String uavType, String modelGuid, Integer isOut);

	/**
	 * 被guid飞机列表
	 *
	 * @param planeGuids 飞机guid
	 * @return {@link R}
	 */
	R getPlaneListByGuids(List<String> planeGuids);


//	/**
//	 * （智能装备台帐新增）
//	 * <AUTHOR>
//	 * @date 2022/09/27 15:49
//	 * @param uavPlaneInfoEntity
//	 * @return void
//	 */
//    void sendZtAddSmartDevice(UavPlaneInfoEntity uavPlaneInfoEntity);

	/**
	 * 被sn飞机信息代码
	 *
	 * @param snCode sn代码
	 * @return {@link R}
	 */
	R getPlaneInfoBySnCode(String snCode);

	/**
	 * 保存应用无人机信息
	 *
	 * @param uavPlaneInfoDTO 无人机dto平面信息
	 * @return {@link R}
	 */
	R<UavPlaneInfoEntity> saveAppUavInfo(AppUavPlaneInfoDTO uavPlaneInfoDTO);

	/**
	 * （查看无人机详情 触发 重庆中台21-3、重庆中台21-4）
	 *
	 * @param planeGuid
	 * @return void
	 * <AUTHOR>
	 * @date 2022/10/10 13:27
	 */
//	void sendZtSmartDeviceQuery(String planeGuid);

	List<UavPlaneInfoEntity> getAllPlaneInfoByDept();

	/**
	 * 重命名无人机名称
	 *
	 * @param sn   无人机SN码
	 * @param name 无人机名称
	 */
	UavPlaneInfoEntity updateUavNameBySn(String sn, String name);
}

