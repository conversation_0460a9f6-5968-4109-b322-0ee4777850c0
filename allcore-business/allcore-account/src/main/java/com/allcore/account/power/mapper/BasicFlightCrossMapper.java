package com.allcore.account.power.mapper;

import com.allcore.account.power.entity.FlightCrossEntity;
import com.allcore.account.power.vo.BasicFlightCrossVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 飞行交跨表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
public interface BasicFlightCrossMapper extends BaseMapper<FlightCrossEntity> {

	List<BasicFlightCrossVO> selectPageListByVoltageLevelLineGuid(IPage page, @Param("deptCode") String deptCode, @Param("voltageLevel") String voltageLevel, @Param("lineGuid") String lineGuid);

	List<BasicFlightCrossVO> queryFlightCrossInfoByLineGuids(@Param("list") List<String> lineGuids);

}
