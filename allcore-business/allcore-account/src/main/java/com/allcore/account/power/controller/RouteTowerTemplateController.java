package com.allcore.account.power.controller;

import com.allcore.account.power.entity.RouteTowerTemplateEntity;
import com.allcore.account.power.mapper.LineMapper;
import com.allcore.account.power.service.LineService;
import com.allcore.account.power.service.RouteTowerTemplateService;
import com.allcore.account.power.service.TowerService;
import com.allcore.account.power.vo.RouteLearnTemplateVo;
import com.allcore.account.power.vo.RouteTowerTemplateVo;
import com.allcore.account.power.vo.TowerTemplateVO;
import com.allcore.common.utils.AllCoreAuthUtil;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;


/**
 * 航线模板(RouteTowerTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-29 17:43:37
 */
@RestController
@RequestMapping("/power/towerTemplate")
@Slf4j
@Api(value = "杆塔关联表", tags = "杆塔关联表接口")
public class RouteTowerTemplateController {
    /**
     * 服务对象
     */
    @Resource
    private RouteTowerTemplateService routeTowerTemplateService;
    @Resource
	private LineMapper lineMapper;
	@Resource
	private LineService lineService;
	@Resource
	private TowerService towerService;


	/**
	 * 分页 平台模板基础信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入routeLearnTemplate")
	public R<IPage<TowerTemplateVO>> list(@RequestParam(value = "unitCode", required = false) String unitCode,
										  @RequestParam(value = "voltageLevel", required = false) String voltageLevel,
										  @RequestParam(value = "lineName",required = false) String lineName,
										  @RequestParam(value = "towerType",required = false) String towerType,
										  @RequestParam(value = "professionalType",required = false) String professionalType,
										  @RequestParam(value = "loopNumber",required = false) String loopNumber,
										  @RequestParam(value = "templateName",required = false) String templateName,

										  Query query) {
		IPage<TowerTemplateVO> infos = lineService.queryLineTowerPage(unitCode, voltageLevel, lineName,
			towerType, loopNumber, professionalType,templateName, Condition.getPage(query));
		return R.data(infos);
	}


	/**
	 * 批量插入数据
	 * @param routeLearnTemplate
	 * @return
	 */
	@PostMapping("/saveBatch")
	public R update(@RequestBody RouteLearnTemplateVo routeLearnTemplate) {
		List<RouteTowerTemplateEntity> arrayList = new ArrayList<>();
		AllcoreUser userinfo = AllCoreAuthUtil.getUser();
		List<String> towerGuids = lineMapper.getTowerGuids(routeLearnTemplate.getVoltageLevel(), routeLearnTemplate.getTowerType(), routeLearnTemplate.getLoopNumber());
		if (CollectionUtils.isNotEmpty(towerGuids)){
			//先物理删除
				for (String route : routeLearnTemplate.getTemplateGuid()){
					routeTowerTemplateService.deleteByDouble(towerGuids,route);
				}
				//新增
			for (String info : towerGuids) {
				for (String back : routeLearnTemplate.getTemplateGuid()) {
					RouteTowerTemplateEntity template = new RouteTowerTemplateEntity();
					template.setId(CommonUtil.getGuid());
					template.setTowerTemplateGuid(UUID.randomUUID().toString().replace("-", ""));
					template.setTowerGuid(info);
					template.setTemplateGuid(back);
					template.setCreateUser(userinfo.getUserId());
					template.setCreateDept(userinfo.getDeptId());
					template.setDeptCode(userinfo.getDeptCode());
					template.setCreateTime(new Date());
					template.setIsDeleted(0);
					arrayList.add(template);
				}
			}
			if (arrayList.size() > 0){
				return R.data(this.routeTowerTemplateService.insertAllRelation(arrayList));
			}
			return R.success("绑定失败");
		}else {
			return R.success("未获取到杆塔");
		}

	}

	/**
	 * 插入数据
	 *
	 * @param routeTowerTemplate 实体
	 * @return 更新结果
	 */
	@PostMapping("/save")
	public R save(@RequestBody RouteTowerTemplateVo routeTowerTemplate) {
		List<RouteTowerTemplateEntity> list = new ArrayList<RouteTowerTemplateEntity>();
		if (CollectionUtils.isNotEmpty(routeTowerTemplate.getTemplateGuid()) && StringUtil.isNotBlank(routeTowerTemplate.getTowerTemplateGuid())){
			RouteTowerTemplateEntity towerTemplate = routeTowerTemplateService.getOne(new QueryWrapper<RouteTowerTemplateEntity>().lambda().eq(RouteTowerTemplateEntity::getTowerTemplateGuid, routeTowerTemplate.getTowerTemplateGuid()));
			for (String info : routeTowerTemplate.getTemplateGuid()){
				RouteTowerTemplateEntity template = new RouteTowerTemplateEntity();
				template.setTowerTemplateGuid(towerTemplate.getTowerTemplateGuid());
				template.setTowerGuid(routeTowerTemplate.getTowerGuid());
				template.setTemplateGuid(info);
				UpdateWrapper<RouteTowerTemplateEntity> updateWrapper = new UpdateWrapper<>();
				updateWrapper.lambda().eq(RouteTowerTemplateEntity::getTowerTemplateGuid,template.getTowerTemplateGuid());
				this.routeTowerTemplateService.update(template,updateWrapper);
			}
			return R.success("保存成功");
		}else {
			for (String info : routeTowerTemplate.getTemplateGuid()){
				RouteTowerTemplateEntity template = new RouteTowerTemplateEntity();
				template.setTowerTemplateGuid(UUID.randomUUID().toString().replace("-", ""));
				template.setTowerGuid(routeTowerTemplate.getTowerGuid());
				template.setTemplateGuid(info);
				list.add(template);
			}
			return R.data(this.routeTowerTemplateService.saveBatch(list));
		}

	}


	/**
	 * 重置学习模板
	 * @param routeLearnTemplate
	 * @return
	 */
	@PostMapping("/resetTemplate")
	public R resetTemplate(@RequestBody RouteLearnTemplateVo routeLearnTemplate) {
		List<String> towerGuids = lineMapper.getTowerGuidsHaveTemplate(routeLearnTemplate.getVoltageLevel(), routeLearnTemplate.getTowerType(), routeLearnTemplate.getLoopNumber());
		if (CollectionUtils.isNotEmpty(towerGuids)){
			return R.data(routeTowerTemplateService.deleteList(towerGuids));
		}else {
			return R.success("未获取到杆塔");
		}

	}
}

