-- <PERSON><PERSON><PERSON> app离线信息表
-- DROP TABLE IF EXISTS `sys_app_offline`;
CREATE TABLE `sys_app_offline` (
                                   `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '无人机设备sn',
                                   `remote_guid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '遥控器唯一识别码',
                                   `worker_guid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '飞手GUID',
                                   `user_guid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户GUID',
                                   `operate_person` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
                                   `create_time` datetime DEFAULT NULL COMMENT '离线日期',
                                   `end_time` datetime DEFAULT NULL COMMENT '申请离线截止日期',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改日期',
                                   `is_deleted` int DEFAULT '0' COMMENT '是否删除',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='app离线信息表';

-- zhangsai 上传日志文件记录表
-- DROP TABLE IF EXISTS `sys_app_upload_log`;
CREATE TABLE `sys_app_upload_log` (
                                 `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                 `file_guid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件guid',
                                 `file_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名称',
                                 `app_version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用版本号',
                                 `device_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备型号',
                                 `user_guid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上传人guid',
                                 `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上传人账号名',
                                 `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人id',
                                 `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `file_size` bigint DEFAULT NULL COMMENT '附件大小字节单位',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='上传日志文件记录表';


-- 2023-09-11 sys_notice 增加icon_url 图片路径字段
alter table sys_notice add icon_url varchar(255) null comment '图片路径字段' after remark;