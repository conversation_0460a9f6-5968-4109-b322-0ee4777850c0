package com.allcore.external.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.external.config.TetraProperties;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.dto.tetra.TetraPageDTO;
import com.allcore.external.dto.tetra.TetraRequestDTO;
import com.allcore.external.entity.OrbitalRobotInfo;
import com.allcore.external.entity.OribitialRobotConfig;
import com.allcore.external.mapper.OribitalRobotConfigMapper;
import com.allcore.external.service.IOrbitalRobotInfoService;
import com.allcore.external.service.IOrbitialRobotService;
import com.allcore.external.service.OribitalRobotConfigService;
import com.allcore.external.utils.TetraClientUtil;
import com.allcore.external.vo.tetra.RobotDetailVO;
import com.allcore.external.vo.tetra.RobotInfoVO;
import com.allcore.external.wrapper.OribitalRobotConfigWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 09 5月 2025
 */
@Service
@Slf4j
public class OribitialRobotConfigServiceImpl extends ServiceImpl<OribitalRobotConfigMapper, OribitialRobotConfig> implements OribitalRobotConfigService {
    final
    private TetraProperties tetraProperties;
    final
    private AllcoreRedis allcoreRedis;

    @Resource
    private  IOrbitalRobotInfoService orbitalRobotInfoService;


    public OribitialRobotConfigServiceImpl(TetraProperties tetraProperties, AllcoreRedis allcoreRedis) {
        this.tetraProperties = tetraProperties;
        this.allcoreRedis = allcoreRedis;
    }

    @Override
    public IPage<RobotInfoVO> getPage(TetraPageDTO dto, Query query) {
        IPage<OribitialRobotConfig> page = page(Condition.getPage(query), new LambdaQueryWrapper<OribitialRobotConfig>()
                .likeRight(StringUtils.isNotBlank(dto.getDeptCode()), OribitialRobotConfig::getDeptCode, dto.getDeptCode())

        );
        IPage<RobotInfoVO> robotInfoVOIPage = OribitalRobotConfigWrapper.build().pageVO(page);

        if ( !CollectionUtil.isEmpty(robotInfoVOIPage.getRecords())){
            robotInfoVOIPage.getRecords().forEach(e -> {
                String url = "http://"+ e.getHost()+ ExternalConstant.Tetra.ROBOT_LIST;
                try{
                    JSONArray resultJsonArrary = this.fetchList(url, new HashMap<>(),e.getHost(),e.getUsername(),e.getPassword());
                    if( resultJsonArrary != null){
                        List<RobotInfoVO> robotInfoVOList = JSONArray.parseArray(resultJsonArrary.toJSONString(), RobotInfoVO.class);
                        if (CollectionUtil.isNotEmpty(robotInfoVOList)){
                            e.setRobotSn(robotInfoVOList.get(0).getRobotSn());
                            e.setRobotId(robotInfoVOList.get(0).getRobotId());
                            e.setRobotName(robotInfoVOList.get(0).getRobotName());
                            e.setRobotType(robotInfoVOList.get(0).getRobotType());
                            if(dto.getIsDetail()==1){
                                TetraRequestDTO dto1=new TetraRequestDTO();
                                dto1.setRobotId(e.getRobotId());
                                dto1.setHost(e.getHost());
                                RobotDetailVO robotDetail = this.getRobotDetail(dto1, e.getUsername(), e.getPassword());
                                if (robotDetail != null) {
                                    e.setRobotDetail(robotDetail);
                                }
                            }
                        }
                    }
                }catch (Exception ex){
                    log.error("获取轨道机器人列表异常");
                    ex.printStackTrace();
                }
            });
        }

        return robotInfoVOIPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RobotInfoVO> getList(TetraPageDTO dto) {
        List<OribitialRobotConfig> page = this.list(new LambdaQueryWrapper<OribitialRobotConfig>()
                .likeRight(StringUtils.isNotBlank(dto.getDeptCode()), OribitialRobotConfig::getDeptCode, dto.getDeptCode())

        );
        List<RobotInfoVO> robotInfoVOIPage = OribitalRobotConfigWrapper.build().listVO(page);

        List<OrbitalRobotInfo>infoList=new ArrayList<>();
        if ( !CollectionUtil.isEmpty(robotInfoVOIPage)){
            for (RobotInfoVO e : robotInfoVOIPage) {
                String url = "http://" + e.getHost() + ExternalConstant.Tetra.ROBOT_LIST;
                try {
                    JSONArray resultJsonArrary = this.fetchList(url, new HashMap<>(), e.getHost(), e.getUsername(), e.getPassword());
                    if (resultJsonArrary != null) {
                        List<RobotInfoVO> robotInfoVOList = JSONArray.parseArray(resultJsonArrary.toJSONString(), RobotInfoVO.class);
                        if (CollectionUtil.isNotEmpty(robotInfoVOList)) {
                            e.setRobotSn(robotInfoVOList.get(0).getRobotSn());
                            e.setRobotId(robotInfoVOList.get(0).getRobotId());
                            e.setRobotName(robotInfoVOList.get(0).getRobotName());
                            e.setRobotType(robotInfoVOList.get(0).getRobotType());


                            if (dto.getIsDetail() == 1) {
                                TetraRequestDTO dto1 = new TetraRequestDTO();
                                dto1.setRobotId(e.getRobotId());
                                dto1.setHost(e.getHost());
                                RobotDetailVO robotDetail = this.getRobotDetail(dto1, e.getUsername(), e.getPassword());
                                if (robotDetail != null) {
                                    e.setRobotDetail(robotDetail);
                                }
                            }
                        }
                        // 保存轨道机器人信息到库中
                        if(Func.notNull(dto.getIsDetail())&&dto.getIsDetail()==2){
                            OrbitalRobotInfo orbitalRobotInfo = new OrbitalRobotInfo();
                            BeanUtil.copyProperties(e, orbitalRobotInfo);
                            e.setId(null);
                            if(Func.isBlank(e.getDeptCode())){
                                e.setDeptCode(AuthUtil.getDeptCode());
                            }
                            infoList.add(orbitalRobotInfo);
                        }

                    }
                } catch (Exception ex) {
                    if(Func.notNull(dto.getIsDetail())&&dto.getIsDetail()==2){
                        OrbitalRobotInfo orbitalRobotInfo = new OrbitalRobotInfo();
                        BeanUtil.copyProperties(e, orbitalRobotInfo);
                        e.setId(null);
                        e.setRobotType("T5-E03");
                        e.setRobotId("1");
                        e.setRobotSn("TC-001");
                        e.setRobotName("测试机器人");

                        if(Func.isBlank(e.getDeptCode())){
                            e.setDeptCode(AuthUtil.getDeptCode());
                        }
                        infoList.add(orbitalRobotInfo);
                    }
                    log.error("获取轨道机器人列表异常");
                    ex.printStackTrace();
                }
            }
            if(Func.isNotEmpty(infoList)){
                // 先删除再新增
                orbitalRobotInfoService.remove(new QueryWrapper<>());
                orbitalRobotInfoService.saveBatch(infoList);
            }
        }

        return robotInfoVOIPage;
    }

    @Override
    public RobotDetailVO getRobotDetail(TetraRequestDTO dto,String username,String password) {
        String url = "http://" +dto.getHost()+ ExternalConstant.Tetra.ROBOT_DETAIL;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("robotId", dto.getRobotId());
        try {
            JSONObject fetchedObj = this.fetchObj(url, paramMap, dto.getHost(),username,password);
            if (fetchedObj != null) {
                return JSONObject.parseObject(fetchedObj.toJSONString(), RobotDetailVO.class);
            }
        }catch (Exception e){
            log.error("获取轨道机器人详情异常");
            e.printStackTrace();
        }
        //todo 数据存储
        return null;
    }

    /**
     * 第三方返回列表数据处理逻辑，包括401错误码重试机制
     * @param url
     * @param paramMap
     * @return
     */
    private JSONArray fetchList(String url,Map<String, Object> paramMap, String host,String username,String password)  {
        String token = this.getToken2Redis(host,username, password);
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("Authorization", token);

        JSONObject resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);
        if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
            return resultObj.getJSONArray("data");

        } else if (resultObj != null && "401".equalsIgnoreCase(resultObj.getString("code"))) {
            // Token失效，重新获取Token并重试
            allcoreRedis.del(ExternalConstant.Tetra.TOKEN+host);
            String newToken = this.getToken2Redis(host,username, password);
            headersMap.put("Authorization", newToken);
            resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);

            if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
                return resultObj.getJSONArray("data");
            }
        }
        return null;
    }

    private String getToken2Redis(String host,String username,String password) {
        String token = null;
        try {
            //先从redis中获取token，获取不到在调用第三方接口返回token
            String redisToken = allcoreRedis.get(ExternalConstant.TETRA_TOKEN+host);
            if (StringUtils.isNotBlank(redisToken)) {
                return redisToken;
            }
            R<String> tokenR = this.getToken(host, username, password);
            if (tokenR.isSuccess()) {
                token = tokenR.getData();
            }
        } catch (Exception e) {
            log.error("获取token异常:{}", e);
            throw new ServiceException("获取轨道机器人token异常");
        }
        return token;
    }

    public R<String> getToken(String host,String username,String password)  {
        String token = null;
        try {
            String url = "http://"+host + ExternalConstant.Tetra.TOKEN;
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("username", username);
            paramMap.put("password", password);

            token = TetraClientUtil.getToken(url, paramMap);

            if (StringUtils.isNotBlank(token)) {
                allcoreRedis.setEx(ExternalConstant.Tetra.TOKEN+host, token, Duration.ofDays(30));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return R.data(token);
    }

    private JSONObject fetchObj(String url,Map<String, Object> paramMap,String host,String username,String password)  {
        String token = this.getToken2Redis(host,username,password);
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("Authorization", token);

        JSONObject resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);
        log.info("获取数据结果：{}",resultObj);
        if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
            return resultObj.getJSONObject("data");

        } else if (resultObj != null && "401".equalsIgnoreCase(resultObj.getString("code"))) {
            // Token失效，重新获取Token并重试
            allcoreRedis.del(ExternalConstant.TETRA_TOKEN+host);
            String newToken = this.getToken2Redis(host,username,password);
            headersMap.put("Authorization", newToken);
            resultObj = TetraClientUtil.doPostResultJson(url, headersMap, paramMap);

            if (resultObj != null && "200".equalsIgnoreCase(resultObj.getString("code"))) {
                return resultObj.getJSONObject("data");
            }
        }
        return null;
    }



}
