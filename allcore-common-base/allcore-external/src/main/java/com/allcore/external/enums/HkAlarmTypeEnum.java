package com.allcore.external.enums;

/**
 * 海康智能安防事件类型
 * <AUTHOR>
 * @date 2025/5/14 10:31
 **/
public enum HkAlarmTypeEnum {


    TYPE_FIRE_ALARM(192515, "fire"),
    TYPE_PERIMETER_ALARM(131588, "perimeter_warning"),


    ;


    HkAlarmTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 告警code
     */
    private Integer code;
    /**
     * 告警类型字典项
     */
    private String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(Integer code) {
        for (HkAlarmTypeEnum alarmTypeEnum : HkAlarmTypeEnum.values()) {
            if (alarmTypeEnum.getCode().equals(code)) {
                return alarmTypeEnum.getValue();
            }
        }
        return null;
    }
}
