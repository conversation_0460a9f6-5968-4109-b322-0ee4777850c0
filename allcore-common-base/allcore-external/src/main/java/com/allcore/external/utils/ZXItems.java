package com.allcore.external.utils;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON>
 * @version: 1.0
 * @date: 2023/03/13
 **/
@Data
public class ZXItems {
    private List<ZXActions> actions;
    private int actions_count;
    private double altitude;
    private int delay_time;
    private int index;
    private double latitude;
    private double longitude;
    private String sample_time;
    private int sort;
    private double speed;
    private List<ZXTargetPostion> targetPostion;
    private int way_type;
    private double yaw;
}
