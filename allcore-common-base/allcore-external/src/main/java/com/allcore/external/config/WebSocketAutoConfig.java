package com.allcore.external.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.SockJsServiceRegistration;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

//头部加注解EnableWebSocketMessageBroker，允许使用Stomp方式。
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketAutoConfig implements WebSocketMessageBrokerConfigurer {
    @Autowired
    private WebSocketInterceptor authChannelInterceptor;

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        //允许原生的websocket,如果只允许源生的websocket，用这段代码即可
		//registry.addEndpoint("/ws")
		//      .setAllowedOrigins("*");//允许跨域

        //请求地址:ws://ip:port/ws
        SockJsServiceRegistration registration = registry.addEndpoint("/ws")
//                .setAllowedOrigins("*")//允许跨域
                .setAllowedOriginPatterns("*")
                .withSockJS();//允许sockJS
         //下面注解的代码主要用于客户端不支持websocket的情况下，SockJS降级使用xhr-stream或者pjson等等传输方式的时候使用。
        //registration.setClientLibraryUrl("//cdn.jsdelivr.net/npm/sockjs-client@1.5.2/dist/sockjs.min.js");
    }
	/**
     * 注册相关的消息频道
     *
     * @param config
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
    	//设置两个频道，topic用于广播，queue用于点对点发送
        config.enableSimpleBroker("/topic/", "/queue/");
        //设置应用目的地前缀
        config.setApplicationDestinationPrefixes("/app");
        //设置用户目的地前缀
        config.setUserDestinationPrefix("/user");
    }
    /**
     * 加入拦截器主要是为了验证权限的
     *
     * @param registration
     */
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(authChannelInterceptor);
    }
	//这个是为了解决和调度任务的冲突重写的bean
    @Primary
    @Bean
    public TaskScheduler taskScheduler(){
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(10);
        taskScheduler.initialize();
        return taskScheduler;
    }
}

