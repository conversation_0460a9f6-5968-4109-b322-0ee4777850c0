package com.allcore.external.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.allcore.common.constant.Constant;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.entity.PositionDevice;
import com.allcore.external.mapper.PositionDeviceMapper;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.vo.PositionWithPeopleVO;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.feign.MainForAppClient;
import com.allcore.main.code.inspection.vo.InspectionTaskVO;
import com.allcore.system.cache.SysCache;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class PositionRealTimeWebsocketHandler implements WebSocketMessageHandler {
    private final PositionDeviceMapper positionDeviceMapper;
    private final SimpMessagingTemplate simpMessagingTemplate;
    private final MainForAppClient mainForAppClient;
    private final AllcoreRedis allcoreRedis;
    private final IAlarmInfoService alarmInfoService;

    @Override
    public void handleTextMessage(String msg) {
        List<PositionWithPeopleVO> vos = new ArrayList<>();

        JSONObject result = JSONUtil.parseObj(msg);
        Object action = result.get("action");
        // 推送类型只接受推送 "action":realtimeData
        if (action != null && StringUtil.equals(action.toString(), "realtimeData")) {
            log.info("收到RAS服务端定位设备实时数据: {}", msg);
            JSONObject content = (JSONObject) result.get("content");
            JSONObject position = (JSONObject) content.get(Constant.WS_GROUP);
            if (position != null) {
                // 获取各设备坐标数据
                Map<String, Object> positionMap = JSONUtil.toBean(position, Map.class);
                // 填充坐标信息
                vos = convertToList(positionMap);
                // 填充其他信息
                vos.forEach(vo -> {
                    PositionDevice positionDevice = getPositionDevice(vo.getDeviceKey());
                    if (positionDevice != null && StringUtil.isNotBlank(positionDevice.getPeopleId())) {
                        vo.setId(positionDevice.getId());
                        vo.setName(positionDevice.getName());
                        // 1.获取人员信息
                        vo.setPeopleId(positionDevice.getPeopleId());
                        User user = UserCache.getUser(positionDevice.getPeopleId());
                        if (user != null) {
                            vo.setPeopleSex(user.getSex().toString());
                            vo.setPeopleName(user.getRealName());
                        } else {
                            vo.setPeopleSex("未知");
                            vo.setPeopleName("未知");
                        }
                        // 2.单位信息
                        vo.setDeptCode(positionDevice.getDeptCode());
                        vo.setDeptName(SysCache.getDeptNameByDeptCode(positionDevice.getDeptCode()));
                        // 3.获取该设备绑定人员最新任务信息
                        InspectionTaskVO taskVO = getInspectionTaskVO(positionDevice.getPeopleId());
                        if (taskVO != null && StringUtil.isNoneBlank(taskVO.getInspectionTaskNo(), taskVO.getDeviceNames())) {
                            vo.setTaskNum(taskVO.getInspectionTaskNo());
                            vo.setTaskType("巡视任务");
                            vo.setContent("对" + taskVO.getDeviceNames() + "进行检修");
                        }
                        // 4.人员与位置都存在，异步调用检查是否进入围栏并告警
                        alarmInfoService.fenceSaveAlarmInfo(vo);
                    }
                });
                log.info("定位设备实时数据+绑定人员：{}", vos);
                // 将第三方实时消息转发给前端
                simpMessagingTemplate.convertAndSend("/topic/position", vos);
            }
        }
    }

    public List<PositionWithPeopleVO> convertToList(Map<String, Object> positionMap) {
        List<PositionWithPeopleVO> result;
        // 按设备id分组结果
        Map<String, Map<String, Object>> deviceGroups = new HashMap<>();
        // 设置正则表达式
        Pattern pattern = Pattern.compile(Constant.RAS_POSITION_DEVICE + "([0-9A-Za-z]+):position_(x|y|z)");
        for (Map.Entry<String, Object> entry : positionMap.entrySet()) {
            if (StringUtil.isBlank(entry.getKey().toString()) || StringUtil.isBlank(entry.getValue().toString())) {
                continue;
            }
            String key = entry.getKey().trim();
            Matcher matcher = pattern.matcher(key);
            if (matcher.matches()) {
                // 获取设备skey
                String deviceId = matcher.group(1);
                // 分组xyz坐标
                String coordinateType = matcher.group(2);
                // 获取坐标值
                Object value = entry.getValue();
                deviceGroups.putIfAbsent(deviceId, new HashMap<>());
                deviceGroups.get(deviceId).put(coordinateType, value);
            }
        }
        // 分组后的设备坐标数据转化为vo
        log.info("坐标值不为空的设备坐标为{}", deviceGroups);
        result = deviceGroups.entrySet().stream().map(
                group -> {
                    String deviceId = group.getKey();
                    Map<String, Object> coordinates = group.getValue();
                    PositionWithPeopleVO vo = new PositionWithPeopleVO();
                    vo.setDeviceKey(deviceId);
                    // 填充坐标值，处理可能的类型转换
                    if (coordinates.containsKey("x")) {
                        vo.setPositionX(convertToDouble(coordinates.get("x")));
                    }
                    if (coordinates.containsKey("y")) {
                        vo.setPositionY(convertToDouble(coordinates.get("y")));
                    }
                    if (coordinates.containsKey("z")) {
                        vo.setPositionZ(convertToDouble(coordinates.get("z")));
                    }
                    return vo;
                }
        ).collect(Collectors.toList());
        return result;
    }

    /**
     * 类型转换为double
     *
     * @param value
     * @return
     */
    private static Double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取定位设备
     *
     * @param skey
     * @return
     */
    private PositionDevice getPositionDevice(String skey) {
        PositionDevice positionDevice;
        String object = allcoreRedis.hGet(ExternalConstant.POSITION_DEVICE, skey);
        if (object != null) {
            positionDevice = JSONUtil.toBean(object, PositionDevice.class);
        } else {
            positionDevice = positionDeviceMapper.selectOne(new LambdaQueryWrapper<PositionDevice>()
                    .eq(PositionDevice::getSkey, skey));
            allcoreRedis.hSet(ExternalConstant.POSITION_DEVICE, positionDevice.getSkey(), JSONUtil.toJsonStr(positionDevice));
        }
        return positionDevice;
    }

    /**
     * 获取关联的巡视任务和设备信息
     * @param userId
     * @return
     */
    private InspectionTaskVO getInspectionTaskVO(String userId) {
        InspectionTaskVO inspectionTaskVO = new InspectionTaskVO();
        String one = allcoreRedis.get(ExternalConstant.POSITION_TASK + userId);
        if (one != null) {
            inspectionTaskVO = JSONUtil.toBean(one, InspectionTaskVO.class);
        } else {
            R<InspectionTaskVO> taskResult = mainForAppClient.getTaskAndUserInfo(userId);
            if (taskResult.isSuccess() && taskResult.getData() != null) {
                inspectionTaskVO = taskResult.getData();
                allcoreRedis.setEx(ExternalConstant.POSITION_TASK + userId, JSONUtil.toJsonStr(inspectionTaskVO), 30 * 60L);
            }
        }
        return inspectionTaskVO;
    }
}
