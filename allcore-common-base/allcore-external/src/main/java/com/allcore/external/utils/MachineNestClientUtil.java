package com.allcore.external.utils;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

/**
 * 机巢对接第三方相关工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14 16:27
 */
public class MachineNestClientUtil {

    /**
     * 发送Get请求
     *
     * @param url        请求url
     * @param headersMap 请求头
     * @param paramMap   参数
     * @return 结果
     * <AUTHOR>
     * @date 2022/11/25 13:59
     **/
    public static String doGet(String url, Map<String, String> headersMap, MultiValueMap<String, String> paramMap) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headersMap.forEach(headers::add);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        URI uri = builder.queryParams(paramMap).build().encode().toUri();
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(paramMap, headers);
        //获取返回数据
        return restTemplate.exchange(uri, HttpMethod.GET, httpEntity, String.class).getBody();
    }

    /**
     * 发送post请求
     *
     * @param url        请求url
     * @param headersMap 请求头
     * @param mediaType  Content-Type
     * @param paramMap   请求参数
     * @return 结果
     * <AUTHOR>
     * @date 2022/11/25 14:00
     **/
    public static String doPost(String url, Map<String, String> headersMap, MediaType mediaType, Map<String, Object> paramMap) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaType);
        headersMap.forEach(headers::add);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(paramMap, headers);
        //获取返回数据
        return restTemplate.postForObject(url, httpEntity, String.class);
    }
}
