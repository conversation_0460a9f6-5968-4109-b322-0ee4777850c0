package com.allcore.external.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 05 6月 2025
 */
@Component
@Slf4j
public class OneTimeTaskRegistrar {

    @Autowired
    @Qualifier("taskScheduler")
    private TaskScheduler scheduler;

    @Autowired
    private ApplicationContext applicationContext;

    public void register(TaskEntity entity) {
        long delay = Duration.between(LocalDateTime.now(), entity.getRunAt()).toMillis();
        if (delay < 0) return;

        Runnable task = () -> {
            try {
                log.info("添加执行任务：{}", entity.getTaskId());
                TaskHandler handler = applicationContext.getBean(entity.getHandlerName(), TaskHandler.class);
                handler.execute(entity.getTaskId(), entity.getParams());
            } catch (Exception e) {
                log.error("任务执行失败：" + e.getMessage());
            }
        };
        log.info("添加定时器:{}", entity.getTaskId());
        scheduler.schedule(task, Date.from(entity.getRunAt().atZone(ZoneId.systemDefault()).toInstant()));
    }
}
