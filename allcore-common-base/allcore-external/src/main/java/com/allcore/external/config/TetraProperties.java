package com.allcore.external.config;

import lombok.Data;
import org.bouncycastle.cms.PasswordRecipientId;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PlatformProperties
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "tetra")
public class TetraProperties {

	private String username;

	private String password;

	private String host;

	private String wsUrl;

	private String dhVisualUrl;

	private String dhInfraredUrl;

	private String hkVisualUrl;

	private String hkInfraredUrl;
	
	private String jgInfraredUrl;
}
