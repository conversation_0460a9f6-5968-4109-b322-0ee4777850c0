package com.allcore.external.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.external.config.AbstractMessagingEndpoint;
import com.allcore.external.config.MessagingEndpointRegistry;
import com.allcore.external.config.WebSocketClientManager;
import com.allcore.external.dto.AirportFanSaveDTO;
import com.allcore.external.dto.AirportTaskDTO;
import com.allcore.external.entity.AirportTask;
import com.allcore.external.feign.IAirportClient;
import com.allcore.external.feign.IAirportTaskClient;
import com.allcore.external.mapper.AirportTaskMapper;
import com.allcore.external.service.IAirportTaskService;
import com.allcore.external.utils.FeignTokenRetryExecutor;
import com.allcore.external.utils.UrlToMultipartFileConverter;
import com.allcore.external.vo.AirportTaskVO;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.inspection.dto.InspectionPictureSaveDTO;
import com.allcore.main.code.inspection.feign.InspectionPictureClient;
import com.allcore.main.code.source.feign.IMainSourceClient;
import com.allcore.main.code.source.vo.FanVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 06 6月 2025
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AirportTaskServiceImpl extends ZxhcServiceImpl<AirportTaskMapper, AirportTask> implements IAirportTaskService {

    private final IAirportClient airportClient;
    private final FeignTokenRetryExecutor executor;
    private final WebSocketClientManager webSocketClientManager;
    private final MessagingEndpointRegistry messagingEndpointRegistry;
    private final IOssClient ossClient;
    private final InspectionPictureClient inspectionPictureClient;
    private final IMainSourceClient mainSourceClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveList(List<AirportTaskDTO> list) {
        List<AirportTask> taskList = list.stream().map(item -> {
            AirportTask task = BeanUtil.copy(item, AirportTask.class);
            val taskName = item.getTaskName() + "-" + System.currentTimeMillis();
            task.setTaskStatus(CommonConstant.INTEGER_NUM_ZERO);
            if (StringUtils.equals(item.getDeviceType(), BizDictEnum.DEVICE_TYPE_FAN.getCode())) {
                R<FanVO> fanVOR = mainSourceClient.getDeviceFanInfoById(item.getDeviceId());
                if (!fanVOR.isSuccess()) {
                    throw new ServiceException("获取风机信息失败:" + fanVOR.getMsg());
                }
                FanVO fanvo = fanVOR.getData();
                AirportFanSaveDTO fanSaveDTO = AirportFanSaveDTO.builder().siteId(item.getSiteId())
                        .name(taskName).fanNum(fanvo.getFanNum())
                        .bladeLength(fanvo.getBladeLength())
                        .elevation(fanvo.getElevation())
                        .height(fanvo.getHeight())
                        .latitude(fanvo.getLatitude())
                        .longitude(fanvo.getLongitude())
                        .towerHeight(fanvo.getTowerHeight()).build();
                JSONObject fanResult = executor.execute("airport", () -> airportClient.saveFanMissionConfig(fanSaveDTO));
                if (!StringUtils.equals(fanResult.getString("errorCode"), "00000")) {
                    throw new ServiceException("保存风机任务失败:" + fanResult.getString("errorMessage"));
                }
                task.setMissionId(fanResult.getJSONObject("data").getString("id"));
                return task;
            }
            JSONObject strategyListR = executor.execute("airport", () -> {
                Map<String, Object> map = new HashMap<>(8);
                map.put("current", "1");
                map.put("pageSize", "10");
                map.put("siteId", item.getSiteId());
                map.put("name", item.getFileGuid() + "-" + item.getSiteId());
                return airportClient.getStrategyConfigInfoPageList(map);
            });
            if (!StringUtils.equals(strategyListR.getString("errorCode"), "00000")) {
                throw new ServiceException("飞控查询策略列表失败:" + strategyListR.getString("errorMessage"));
            }
            String strategyId = null;
            JSONArray strategyList = strategyListR.getJSONObject("data").getJSONArray("list");
            if (CollectionUtils.isEmpty(strategyList)) {
                JSONObject strategyR = executor.execute("airport", () -> {
                    try {
                        String fileType = item.getFilePath().substring(item.getFilePath().lastIndexOf(".") + 1);
                        MultipartFile file = UrlToMultipartFileConverter.downloadFileAsMultipartFile(item.getFilePath());
                        if (StringUtils.equals(fileType, "kmz")) {
                            return airportClient.analysisKmzFileAndSaveInfo(file, item.getSiteId());
                        } else {
                            return airportClient.analysisFileAndSaveInfo(file, item.getSiteId());
                        }
                    } catch (IOException e) {
                        throw new ServiceException("飞控: 导入航迹文件失败 " + item.getFilePath());
                    }
                });
                if (!StringUtils.equals(strategyR.getString("errorCode"), "00000")) {
                    throw new ServiceException("飞控: 导入策略文件失败 " + item.getFilePath() + " - " + strategyR.getString("errorMessage"));
                }
                strategyId = strategyR.getJSONObject("data").getString("id");
                Map<String, Object> stmap = new HashMap<>(2);
                stmap.put("id", strategyId);
                JSONObject strategyDetailR = executor.execute("airport", () -> airportClient.getStrategyConfigDetail(stmap));
                if (!StringUtils.equals(strategyDetailR.getString("errorCode"), "00000")) {
                    throw new ServiceException("飞控: 获取策略信息失败 " + strategyDetailR.getString("errorMessage"));
                }
                JSONObject strategyDetail = strategyDetailR.getJSONObject("data");
                strategyDetail.put("name", item.getFileGuid() + "-" + item.getSiteId());
                JSONObject saveR = executor.execute("airport", () -> airportClient.submitStrategyConfig(strategyDetail));
                if (!StringUtils.equals(saveR.getString("errorCode"), "00000")) {
                    throw new ServiceException("飞控: 修改策略失败 " + saveR.getString("errorMessage"));
                }
            } else {
                strategyId = strategyList.getJSONObject(0).getString("id");
            }

            Map<String, Object> taskMap = new HashMap<>();
            taskMap.put("name", taskName);
            taskMap.put("strategyId", strategyId);
            taskMap.put("missionDesc", "");
            taskMap.put("missionState", "1");
            taskMap.put("mediaUploadFlag", "0");
            taskMap.put("useSourceKmz", "1");
            JSONObject result = executor.execute("airport", () -> airportClient.submitManualMissionConfig(taskMap));

//            JSONObject result = executor.execute("airport", () -> {
//                try {
//                    return airportClient.importKmlThenCreateManualMission(
//                            item.getSiteId(),
//                            item.getTaskName() + "-" + list.indexOf(item),
//                            UrlToMultipartFileConverter.downloadFileAsMultipartFile(item.getFilePath())
//                    );
//                } catch (IOException e) {
//                    throw new ServiceException("飞控平台创建机场任务失败");
//                }
//            });
            if (!StringUtils.equals(result.getString("errorCode"), "00000")) {
                throw new ServiceException("飞控:" + result.getString("errorMessage") + result.getString("userTips"));
            }
            task.setMissionId(result.getJSONObject("data").getString("id"));
            return task;
        }).collect(Collectors.toList());
        return saveBatch(taskList, 100);
    }

    @Override
    public List<AirportTaskVO> unfinishedTaskByTaskIds(List<String> taskIds) {
        return list(new LambdaQueryWrapper<AirportTask>()
                        .in(AirportTask::getInspectionTaskId, taskIds)
//                .ne(AirportTask::getTaskStatus, CommonConstant.INTEGER_NUM_THREE)
                        // 不查询执行中、图片已上传的任务
                        .notIn(AirportTask::getTaskStatus, ListUtil.of(2, 5))
                        .orderByDesc(AirportTask::getSort).orderByDesc(AirportTask::getCreateTime)
                        .groupBy(AirportTask::getInspectionTaskId)
        ).stream().map(item ->
                BeanUtil.copy(item, AirportTaskVO.class)
        ).collect(Collectors.toList());
    }

    @Override
    public boolean reExecuteTask(String id) {
        AirportTask task = getById(id);
        Map<String, Object> map = new HashMap<>(2);
        map.put("id", task.getMissionId());
        JSONObject jsonObject = executor.execute("airport", () -> airportClient.excuteMission(map));
        if (StringUtils.equals(jsonObject.getString("errorCode"), "00000")) {
            AbstractMessagingEndpoint endpoint = messagingEndpointRegistry.getEndpoint("airport");
            webSocketClientManager.addClient("airport-" + task.getSiteId(), endpoint.getWebSocketUrl() + task.getSiteId(), null, endpoint.getWebSocketMessageHandler());
            task.setTaskStatus(CommonConstant.API_SCOPE_CATEGORY);
            task.setErrorMessage(null);
            task.setHistoryId(jsonObject.getJSONObject("data").getString("historyId"));
        } else {
            throw new ServiceException("飞控:" + jsonObject.getString("errorMessage") + jsonObject.getString("userTips"));
        }
        return updateById(task);
    }

    @Override
    @Async
    public void uploadTaskPics(String id) {
        log.info("开始下载图片上传 ---- >{}", id);
        AirportTask task = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new ServiceException("飞控任务不存在: " + id));
        Map<String, Object> map = new HashMap<>(2);
        map.put("historyId", task.getHistoryId());
        JSONObject result = executor.execute("airport", () -> airportClient.getPicInfoList(map));
        if (!StringUtils.equals(result.getString("errorCode"), "00000")) {
            throw new ServiceException("飞控:" + result.getString("errorMessage") + result.getString("userTips"));
        }
        log.info("查询巡检图片 ------> {}", result.toJSONString());
        List<CompletableFuture<InspectionPictureSaveDTO>> futureList = result.getJSONArray("data").stream().map(item -> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(item));
            String urlStr = jsonObject.getString("url");
            return CompletableFuture.
                    supplyAsync(() -> downloadImage(urlStr))
                    .thenComposeAsync(path -> uploadAndClean(path, jsonObject, task));
        }).collect(Collectors.toList());
        List<InspectionPictureSaveDTO> dtoList = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
        R r = inspectionPictureClient.savePicList(dtoList);
        if (!r.isSuccess()) {
            throw new ServiceException("飞控图片上传至巡检失败!");
        }
        task.setTaskStatus(5);
        updateById(task);
    }


    private Path downloadImage(String imageUrl) {
        try {
            String filename = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
            log.info("开始下载图片: {}", filename);
            Path savePath = Paths.get(System.getProperty("java.io.tmpdir"), filename);
            try (InputStream inputStream = new URL(imageUrl).openStream();
                 ReadableByteChannel channel = Channels.newChannel(inputStream);
                 FileOutputStream outputStream = new FileOutputStream(savePath.toFile());
                 FileChannel fileChannel = outputStream.getChannel()) {

                fileChannel.transferFrom(channel, 0, Long.MAX_VALUE);
            }
            log.info("下载飞控巡检图片完成: {}", filename);
            return savePath;
        } catch (IOException e) {
            throw new CompletionException(e);
        }
    }

    private CompletableFuture<InspectionPictureSaveDTO> uploadAndClean(Path filePath, JSONObject jsonObject, AirportTask task) {
        return CompletableFuture.supplyAsync(() -> {
            log.info("开始上传图片: {}", filePath.getFileName());
            String fileName = jsonObject.getString("fileName");
            try {
                MultipartFile file = CommonUtil.toMultipartFile(filePath, fileName);
                R<AllcoreFileVO> fileResult = ossClient.putFileAttach(BizEnum.BIZ_CODE_ORIGINAL_PICTURE.getCode(), file, StringPool.YES, "");
                if (!fileResult.isSuccess()) {
                    throw new ServiceException("上传图片至Minio失败: " + fileResult.getMsg());
                }
                log.info("上传图片至Minio成功：{}", fileName);
                String fileGuid = fileResult.getData().getFileGuid();
                InspectionPictureSaveDTO dto = new InspectionPictureSaveDTO();
                dto.setFileGuid(fileGuid);
                dto.setInspectionTaskId(task.getInspectionTaskId());
                dto.setDeviceId(task.getDeviceId());
                dto.setDeviceType(task.getDeviceType());
                dto.setPicAuditStatus(BizDictEnum.AUDIT_STATUS_UNREVIEWED.getCode());
                dto.setPicAlgorithmFlg(BizDictEnum.PIC_DEFECT_FLG_UNLABELED.getCode());
                dto.setBindFlag(StringPool.YES);
                dto.setCreateDept(task.getCreateDept());
                dto.setDeptCode(task.getDeptCode());
                dto.setCreateTime(new Date());
                return dto;
            } catch (Exception e) {
                log.error("上传至Minio失败: {} -> {}", fileName, e.getMessage());
                throw new ServiceException("上传至Minio失败:" + fileName + " -> " + e.getMessage());
            } finally {
                try {
                    Files.deleteIfExists(filePath);
                } catch (IOException ignore) {
                }
            }
        });
    }

}
