package com.allcore.external.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 17:41
 **/
@NoArgsConstructor
@Data
public class CurrentTaskInfo {

    /**
     *
     */
    private Integer curPointNoPathCount;
    /**
     * 任务进度（%）
     */
    private Integer taskTotalProgress;
    /**
     * 异常巡检点数
     */
    private Integer pointFailCount;
    /**
     * 巡检点名称
     */
    private String viewPointName;
    /**
     * 已巡检设备数
     */
    private Integer devExecCount;
    /**
     * 失败原因
     */
    private String errMsg;
    /**
     * 当前已巡检的不可达巡检设备数
     */
    private Integer curDevNoPathCount;
    /**
     * 设备id
     */
    private Integer deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 秒
     */
    private String second;
    /**
     * 待巡检设备数
     */
    private Integer devWaitCount;
    /**
     * 时
     */
    private String hour;
    /**
     * 不可达巡检设备数
     */
    private Integer pointNoPathCount;
    /**
     * 任务剩余时间（秒）
     */
    private Integer leftTime;
    /**
     * 巡检维度（1.巡检对象维度，2.巡检点维度）
     */
    private Integer dimension;
    /**
     * 0-无 1-执行成功 2-执行失败
     */
    private Integer taskStatus;
    /**
     * 异常巡检设备数
     */
    private Integer devFailCount;
    /**
     * 已巡检点数
     */
    private Integer pointExecCount;
    /**
     * 正常巡检点数
     */
    private Integer pointSuccessCount;
    /**
     * 执行动作详细描述
     */
    private String taskActionDetail;
    /**
     * 总巡检点数
     */
    private Integer pointAllCount;
    /**
     *
     */
    private String pointNoPathIds;
    /**
     * 巡检维度（1.巡检对象维度，2.巡检点维度）
     */
    private Integer taskDimension;
    /**
     * 优先级
     */
    private Integer updatePriority;
    /**
     * 巡检点ID
     */
    private Integer viewPointId;
    /**
     * 不可达巡检设备树ID
     */
    private String devNoPathTreeIds;
    /**
     * 任务执行ID
     */
    private Integer execId;
    /**
     * 分钟
     */
    private String minute;
    /**
     * 待巡检点数
     */
    private Integer pointWaitCount;
    /**
     *总巡检设备数
     */
    private Integer devAllCount;
    /**
     * 不可到达巡检设备数
     */
    private Integer devNoPathCount;
    /**
     * 行动作描述
     */
    private String taskActionDesc;
    /**
     * 	设备进度，百分比
     */
    private Integer deviceProgress;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 巡检点进度，百分比
     */
    private Integer pointProgress;
    /**
     * 任务进度，0-无 1-等待执行 2-任务超期 3-正在执行 4-中途暂停 5-执行完成 6-中途终止
     */
    private Integer taskProcess;
    /**
     * 正常巡检设备数
     */
    private Integer devSuccessCount;
    /**
     * 任务ID
     */
    private Integer taskId;
}
