package com.allcore.log.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
/**
 * 审计日志date util
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
@Slf4j
public class DateUtil {

    private static final Random jjj = new Random();

    /**
     * @return yyyy-MM-dd
     * @title: getStringDateShort
     * @description:
     * @throws:
     * @date: 2013-10-23
     */

    public static String getStringDateShort() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        return dateString;
    }


    public static String getDay(String strdate) {

        String min;
        min = strdate.substring(8, 10);
        return min;
    }

    public static String getMonth(String strdate) {

        String min;
        min = strdate.substring(5, 7);
        return min;
    }

    public static String getYear(String strdate) {
        String min;
        min = strdate.substring(0, 4);
        return min;
    }
}
