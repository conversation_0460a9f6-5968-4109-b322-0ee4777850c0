package com.allcore.filesystem.builder.oss;

import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.oss.OssTemplate;
import com.allcore.core.oss.enums.OssEnum;
import com.allcore.core.oss.props.OssProperties;
import com.allcore.core.oss.rule.AllcoreOssRule;
import com.allcore.core.oss.rule.OssRule;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.filesystem.entity.Oss;
import com.allcore.filesystem.service.IOssService;

/**
 * Oss云存储统一构建类
 *
 * <AUTHOR>
 */
public class OssBuilder {

    //	public static final String OSS_CODE = "oss:code:";
    public static final String OSS_PARAM_KEY = "code";

    private final OssProperties ossProperties;
    private final IOssService ossService;

    public OssBuilder(OssProperties ossProperties, IOssService ossService) {
        this.ossProperties = ossProperties;
        this.ossService = ossService;
    }

    /**
     * OssTemplate配置缓存池
     */
//	private final Map<String, OssTemplate> templatePool = new ConcurrentHashMap<>();

    /**
     * oss配置缓存池
     */
//	private final Map<String, Oss> ossPool = new ConcurrentHashMap<>();

    /**
     * 获取template
     *
     * @return OssTemplate
     */
    public OssTemplate template() {
        return template(StringPool.EMPTY);
    }

    /**
     * 获取template
     *
     * @param code 资源编号
     * @return OssTemplate
     */
    public OssTemplate template(String code) {
        String tenantId = "947313";
        if (StringUtil.isBlank(tenantId)) {
            tenantId = "947313";
        }
        if (StringUtil.isBlank(tenantId)) {
            throw new ServiceException("未获取到用户信息，无法授权使用！");
        }

        OssTemplate template = null;

        Oss oss = getOss(tenantId, code);
        OssRule ossRule = new AllcoreOssRule(Boolean.FALSE);
        if (oss.getCategory() == OssEnum.MINIO.getCategory()) {
            template = MinioOssBuilder.template(oss, ossRule);
        } else if (oss.getCategory() == OssEnum.ALI.getCategory()) {
            template = AliOssBuilder.template(oss, ossRule);
        } else if (oss.getCategory() == OssEnum.TENCENT.getCategory()) {
            template = TencentOssBuilder.template(oss, ossRule);
        } else if (oss.getCategory() == OssEnum.HUAWEI.getCategory()) {
            template = HuaweiObsBuilder.template(oss, ossRule);
        }

        return template;


//		Oss ossCached = ossPool.get(tenantId);
//		OssTemplate template = templatePool.get(tenantId);
        // 若为空或者不一致，则重新加载
//		if (Func.hasEmpty(template, ossCached) || !oss.getEndpoint().equals(ossCached.getEndpoint()) || !oss.getAccessKey().equals(ossCached.getAccessKey())) {
//			synchronized (OssBuilder.class) {
//				template = templatePool.get(tenantId);
//				if (Func.hasEmpty(template, ossCached) || !oss.getEndpoint().equals(ossCached.getEndpoint()) || !oss.getAccessKey().equals(ossCached.getAccessKey())) {
//					OssRule ossRule;
//					// 若采用默认设置则开启多租户模式, 若是用户自定义oss则不开启
//					if (oss.getEndpoint().equals(ossProperties.getEndpoint()) && oss.getAccessKey().equals(ossProperties.getAccessKey()) && oss.getUnderRegion().equals(ossProperties.getUnderRegion()) && ossProperties.getTenantMode()) {
//						ossRule = new AllcoreOssRule(Boolean.TRUE);
//					} else {
//						ossRule = new AllcoreOssRule(Boolean.FALSE);
//					}
//					if (oss.getCategory() == OssEnum.MINIO.getCategory()) {
//						template = MinioOssBuilder.template(oss, ossRule);
//					} else if (oss.getCategory() == OssEnum.QINIU.getCategory()) {
//						template = QiniuOssBuilder.template(oss, ossRule);
//					} else if (oss.getCategory() == OssEnum.ALI.getCategory()) {
//						template = AliOssBuilder.template(oss, ossRule);
//					} else if (oss.getCategory() == OssEnum.TENCENT.getCategory()) {
//						template = TencentOssBuilder.template(oss, ossRule);
//					} else if (oss.getCategory() == OssEnum.HUAWEI.getCategory()) {
//						template = HuaweiObsBuilder.template(oss, ossRule);
//					}
//					templatePool.put(tenantId, template);
//					ossPool.put(tenantId, oss);
//				}
//			}
//		}
//		return template;
    }

    /**
     * 获取对象存储实体
     *
     * @param tenantId 租户ID
     * @return Oss
     */
    public Oss getOss(String tenantId, String code) {
//		String key = tenantId;
//		LambdaQueryWrapper<Oss> lqw = Wrappers.<Oss>query().lambda().eq(Oss::getTenantId, tenantId);
        // 获取传参的资源编号并查询，若有则返回，若没有则调启用的配置
//		String ossCode = StringUtil.isBlank(code) ? WebUtil.getParameter(OSS_PARAM_KEY) : code;
//		if (StringUtil.isNotBlank(ossCode)) {
////			key = key.concat(StringPool.DASH).concat(ossCode);
//			lqw.eq(Oss::getOssCode, ossCode);
//		} else {
//			lqw.eq(Oss::getStatus, OssStatusEnum.ENABLE.getNum());
//		}
//
//		lqw.eq(Oss::getUnderRegion, ossProperties.getUnderRegion());


        //此处有缓存如果配置不对需要查看redis中缓存再测试
//		Oss oss = CacheUtil.get(RESOURCE_CACHE, OSS_CODE, key, () -> {
////			Oss o = ossService.getOne(lqw);
//			Oss o = new Oss();
//			o.setId(StringPool.ZERO);
//			o.setCategory(OssEnum.of(ossProperties.getName()).getCategory());
//			o.setEndpoint(ossProperties.getEndpoint());
//			o.setBucketName(ossProperties.getBucketName());
//			o.setAccessKey(ossProperties.getAccessKey());
//			o.setSecretKey(ossProperties.getSecretKey());
//			o.setUnderRegion(ossProperties.getUnderRegion());
//			return  o;


        // 若为空则调用默认配置
//			if ((Func.isEmpty(o))) {
//				Oss defaultOss = new Oss();
//				defaultOss.setId(StringPool.ZERO);
//				defaultOss.setCategory(OssEnum.of(ossProperties.getName()).getCategory());
//				defaultOss.setEndpoint(ossProperties.getEndpoint());
//				defaultOss.setBucketName(ossProperties.getBucketName());
//				defaultOss.setAccessKey(ossProperties.getAccessKey());
//				defaultOss.setSecretKey(ossProperties.getSecretKey());
//				defaultOss.setUnderRegion(RegionEnum.INFORMATION_MANAGEMENT_REGION.getCode());
//				return defaultOss;
//			} else {
//				return o;
//			}
//		});


        Oss oss = new Oss();
        if (StringUtil.isBlank(tenantId)) {
//			tenantId = AuthUtil.getTenantId();
            tenantId = "947313";
        }

//		LambdaQueryWrapper<Oss> lqw = Wrappers.<Oss>query().lambda().eq(Oss::getTenantId, tenantId);
//		String ossCode = StringUtil.isBlank(code) ? "" : code;

//		if (StringUtil.isNotBlank(ossCode)) {
//			lqw.eq(Oss::getUnderRegion, code);
//			Oss o = ossService.getOne(lqw);
//			oss = o;
//
//		}else {
//			oss.setId(StringPool.ZERO);
//			oss.setCategory(OssEnum.of(ossProperties.getName()).getCategory());
//			oss.setEndpoint(ossProperties.getEndpoint());
//			oss.setBucketName(ossProperties.getBucketName());
//			oss.setAccessKey(ossProperties.getAccessKey());
//			oss.setSecretKey(ossProperties.getSecretKey());
//			oss.setUnderRegion(ossProperties.getUnderRegion());
//		}

        oss.setId(StringPool.ZERO);
        oss.setCategory(OssEnum.of(ossProperties.getName()).getCategory());
        oss.setEndpoint(ossProperties.getEndpoint());
        oss.setBucketName(ossProperties.getBucketName());
        oss.setAccessKey(ossProperties.getAccessKey());
        oss.setSecretKey(ossProperties.getSecretKey());
        oss.setUnderRegion(ossProperties.getUnderRegion());


        if (oss == null || oss.getId() == null) {
            throw new ServiceException("未获取到对应的对象存储配置");
        } else {
            return oss;
        }
    }


}
