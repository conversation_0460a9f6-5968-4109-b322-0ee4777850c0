package com.allcore.filesystem.utils;

import com.allcore.filesystem.entity.PicBase64;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Blob;
import java.sql.PreparedStatement;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/11/14 10:26
 */

@Slf4j
public class DbUtils {


//	private static SqlSessionFactory sqlSessionFactory;
//
//	static {
//		initSqlSessionFactoryBySqlSessionFactoryBuilder();
//	}

    private static final String PREFIX_LOG = "【自定义DB工具】";

    private final SqlSessionTemplate sqlSessionTemplate;

    public DbUtils(SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSessionTemplate = sqlSessionTemplate;
    }


    public int excuteQuerySql(PicBase64 picBase64, MultipartFile file) {
        String sql = " insert into file_pic_base64 (id, file_guid, file_name, biz_id, pic_base64 , is_thumbnail,dept_code, sync_direct, create_user, create_dept , create_time, update_user, update_time, status, is_deleted , tenant_id)\n" +
                " values (?,?,?,?,?,?,?,?,?, ?,?,?,?,?,?,?\n" +
                " )";
        log.info(PREFIX_LOG + "执行查询sql:" + sql);
        long startTime = System.currentTimeMillis();
        int num = 0;

        try (
                SqlSession session = getSqlSession();
                PreparedStatement psmt = session.getConnection().prepareStatement(sql);
        ) {

            byte[] bytes = file.getBytes();
            Blob blob = new javax.sql.rowset.serial.SerialBlob(bytes);
//			blob.setBinaryStream(blob.length());
            long blobLength = blob.length();
            System.out.println("Get Blob Data:" + (blob.length() / 1024 / 1024) + "M");
            psmt.setString(1, picBase64.getId());
            psmt.setString(2, picBase64.getFileGuid());
            psmt.setString(3, picBase64.getFileName());
            psmt.setString(4, picBase64.getBizId());
            psmt.setBinaryStream(5, blob.getBinaryStream(), blob.getBinaryStream().available());
            psmt.setString(6, picBase64.getIsThumbnail());
            psmt.setString(7, picBase64.getDeptCode());
            psmt.setString(8, picBase64.getSyncDirect());
            psmt.setString(9, picBase64.getCreateUser());
            psmt.setString(10, picBase64.getCreateDept());
            psmt.setTimestamp(11, new Timestamp(System.currentTimeMillis()));
            psmt.setString(12, picBase64.getUpdateUser());
            psmt.setDate(13, null);
            psmt.setInt(14, picBase64.getStatus());
            psmt.setInt(15, 0);
            psmt.setString(16, picBase64.getTenantId());
            num = psmt.executeUpdate();

            log.info("Bolb Data Size:" + blobLength + "Byte");
            long endtTime = System.currentTimeMillis();
            System.out.println("PSTMT: execute sql:" + sql + "  cost time: " + (endtTime - startTime) + "ms");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return num;
    }

    /**
     * 获取sqlSession
     *
     * @return
     */
    public SqlSession getSqlSession() {
        return SqlSessionUtils.getSqlSession(sqlSessionTemplate.getSqlSessionFactory(),
                sqlSessionTemplate.getExecutorType(), sqlSessionTemplate.getPersistenceExceptionTranslator());
    }

    /**
     * 关闭sqlSession
     *
     * @param session
     */
    public void closeSqlSession(SqlSession session) {
        SqlSessionUtils.getSqlSession(sqlSessionTemplate.getSqlSessionFactory(), sqlSessionTemplate.getExecutorType(), sqlSessionTemplate.getPersistenceExceptionTranslator()).commit();
        SqlSessionUtils.closeSqlSession(session, sqlSessionTemplate.getSqlSessionFactory());
    }
}
