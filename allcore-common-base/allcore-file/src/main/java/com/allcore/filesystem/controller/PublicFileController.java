package com.allcore.filesystem.controller;

import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.filesystem.service.IAttachHandlerService;
import com.allcore.filesystem.service.IPublicFileInfoService;
import com.allcore.filesystem.vo.PublicFileInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @data 2022/12/1 14:11
 * @description: 公共文件管理
 */
@RestController
@RequestMapping("/publicFile")
@Api(tags = "公共文件管理")
public class PublicFileController {

	@Resource
	private IPublicFileInfoService publicFileInfoService;

	@Resource
	private IAttachHandlerService attachHandlerService;

	/**
	 * 公用文件管理页面列表
	 *
	 * @param query 查询
	 * @return {@link R}
	 */
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "公用文件管理页面列表", notes = "公用文件管理页面列表")
	@GetMapping("/filePageList")
	public R<IPage<PublicFileInfoVO>> filePageList(Query query){
		return publicFileInfoService.filePageList( Condition.getPage(query));
	}

	/**
	 * 上传单个文件
	 *
	 * @param prefix  存储桶对象名称
	 * @param file    文件
	 * @return String
	 * <AUTHOR>
	 */
	@SneakyThrows(Exception.class)
	@PostMapping("/uploadPublicFile")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "公用文件上传", notes = "公用文件上传")
	public R<String> uploadPublicFile(@RequestParam("prefix") String prefix, @RequestPart("file") MultipartFile file,
									  @RequestParam(value = "describe")String describe) {
		return publicFileInfoService.uploadPublicFile(prefix, file, describe);
	}

	/**
	 * 上传单个文件并保存至附件表,并且生成缩略图(可选)
	 *
	 * @param fileGuid
	 * @return String
	 * <AUTHOR>
	 */
	@PostMapping("/delPublicFile")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "公用文件删除", notes = "公用文件删除")
	public R<String> delPublicFile(@RequestParam("fileGuid") String fileGuid) {
		return publicFileInfoService.delPublicFile(fileGuid);
	}

	/**
	 * 公用文件下载
	 *
	 * @param fileGuid
	 * @return byte[]
	 * <AUTHOR>
	 */
	@GetMapping("/getPublicFileByte")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "公用文件下载", notes = "公用文件下载")
	public R<byte[]> getPublicFileByte(@RequestParam("fileGuid") String fileGuid) {
		return R.data(attachHandlerService.getFileByte(fileGuid));
	}


}
