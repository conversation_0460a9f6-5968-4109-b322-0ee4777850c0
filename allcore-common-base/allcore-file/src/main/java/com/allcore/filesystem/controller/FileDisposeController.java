package com.allcore.filesystem.controller;



import cn.hutool.core.collection.CollectionUtil;
import com.allcore.core.tool.api.R;
import com.allcore.filesystem.dto.DownloadCompressDto;
import com.allcore.filesystem.entity.FileUploadInfo;
import com.allcore.filesystem.entity.PrefixFile;
import com.allcore.filesystem.service.FileDisposeService;
import com.allcore.filesystem.service.IAttachHandlerService;
import com.allcore.filesystem.vo.AllcoreFileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件处理controller
 *
 * <AUTHOR>
 */

@RestController
//@RequestMapping("/fileDispose")
@RequestMapping
@Api(value = "文件处理控制层", tags = "文件处理控制层")
@CrossOrigin
@RequiredArgsConstructor
@Slf4j
public class FileDisposeController {


	private final IAttachHandlerService attachHandlerService;

	private final FileDisposeService fileDisposeService;


	/**
	 * 获取单个文件信息
	 *
	 * @param fileGuid 文件唯一id
	 * @return
	 */
	@SneakyThrows(Exception.class)
	@GetMapping("/getFileDetail")
	@ApiOperation(value = "获取单个文件信息")
	@ApiImplicitParam(name = "fileGuid", value = "文件唯一id", dataType = "String", required = true)
	public R<AllcoreFileVO> getFileDetail(@RequestParam String fileGuid) {

		AllcoreFileVO vo = attachHandlerService.getFileInfo(fileGuid);
		if (null == vo) {
			return R.success("未查询到该文件！");
		}
		return R.data(vo);
	}


	/**
	 * 获取多个文件信息
	 *
	 * @param fileGuids
	 * @return
	 */
	@SneakyThrows(Exception.class)
	@PostMapping("/getFilesDetail")
	@ApiOperation(value = "获取多个文件信息")
	@ApiImplicitParam(name = "fileGuids", value = "文件唯一id集合", dataType = "List<String>", required = true)
	public R<List<AllcoreFileVO>> getFilesDetail(@RequestBody List<String> fileGuids) {

		List<AllcoreFileVO> vos = attachHandlerService.getFilesInfo(fileGuids);
		if (CollectionUtil.isEmpty(vos)) {
			return R.success("未查询到这些文件！");
		}
		return R.data(vos);
	}


	/**
	 * 批量物理删除文件
	 *
	 * @param fileGuids 文件唯一GUID
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/removeRealFiles")
	@ApiOperation(value = "批量物理删除文件")
	@ApiImplicitParam(name = "fileGuids", value = "文件唯一id集合", dataType = "List<String>", required = true)
	public R removeRealFile(@RequestBody List<String> fileGuids) {
		attachHandlerService.materialRemove(fileGuids);
		return R.success("操作成功");
	}

	/**
	 * 批量逻辑删除文件
	 *
	 * @param fileGuids 文件唯一GUID
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/removeFiles")
	@ApiOperation(value = "批量逻辑删除文件")
	@ApiImplicitParam(name = "fileGuids", value = "文件唯一id集合", dataType = "List<String>", required = true)
	public R removeLogicalFile(@RequestBody List<String> fileGuids) {
		attachHandlerService.logicalRemove(fileGuids);
		return R.success("操作成功");
	}


	/**
	 * 分片文件上传校验
	 *
	 * @param md5 文件md5值
	 * @return R
	 */
	@GetMapping("/check")
	@SneakyThrows
	@ApiOperation(value = "分片文件上传校验")
	@ApiImplicitParam(name = "md5", value = "文件md5值", dataType = "String", required = true)
	public R checkFile(@RequestParam("md5") String md5) {
		return fileDisposeService.checkFile(md5);
	}


	/**
	 * 分片文件上传校验
	 *
	 * @param chunk 文件md5值
	 * @param md5   文件md5值
	 * @param index 文件md5值
	 * @return R
	 */
	@PostMapping("/upload/chunk")
	@ApiOperation(value = "分片文件上传")
/*	@ApiImplicitParams({
		@ApiImplicitParam(name = "chunk", value = "文件分片数据", dataType = "MultipartFile",  required = true),
		@ApiImplicitParam(name = "md5", value = "文件md5值", dataType = "String",  required = true),
		@ApiImplicitParam(name = "index", value = "文件md5值", dataType = "String",  required = true)
	})*/
	public R uploadChunk(@RequestParam("chunk") MultipartFile chunk,
						 @RequestParam("md5") String md5,
						 @RequestParam("index") Integer index,
						 @RequestParam("chunkTotal") Integer chunkTotal,
						 @RequestParam("fileSize") Long fileSize,
						 @RequestParam("fileName") String fileName,
						 @RequestParam("chunkSize") Long chunkSize,
						 @RequestParam(required = false, name = "fileGuid") String fileGuid,
						 @RequestParam(required = false, name = "isThumb", defaultValue = "no") String isThumb,
						 @RequestParam(required = false, name = "prefix", defaultValue = "file-server") String prefix

	) {
		return fileDisposeService.uploadChunk(chunk, md5, index, chunkTotal, fileSize, fileName, chunkSize
			, fileGuid, isThumb, prefix);

	}

	@GetMapping("/fileList")
	public R getFileList(){
		log.info("查询文件列表");
		List<FileUploadInfo> fileList  =fileDisposeService.getFileList();
		return  R.data(201,fileList,"文件列表查询成功");
	}

	/**
	 * 文件分片下载
	 *
	 * @param
	 * @return R
	 */
	@PostMapping("/download")
	public void download(@RequestParam("md5") String md5,
						 @RequestParam("fileName") String fileName,
						 @RequestParam("chunkSize") Integer chunkSize,
						 @RequestParam("chunkTotal") Integer chunkTotal,
						 @RequestParam("index")Integer index,
						 HttpServletResponse response) {
		fileDisposeService.download(fileName,chunkSize,chunkSize,chunkTotal,index,response);

	}


	/**
	 * 批量下载文件并根据文件目录组装压缩包
	 *
	 * @param compressDto dto
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/downloadCompress")
	@ApiOperation(value = "批量下载文件并根据文件目录组装压缩包")
	@ApiImplicitParam(name = "compressDto", value = "dto类", dataType = "DownloadCompressDto", required = true)
	public void downloadCompress(@RequestBody DownloadCompressDto compressDto, HttpServletResponse response) {
		attachHandlerService.downloadCompress(compressDto, response);
	}


	/**
	 * 批量图片打包下载
	 *
	 * @param fileGuids
	 * @param response
	 * @return void
	 * <AUTHOR>
	 * @date 2022/9/19 15:42
	 */
	@SneakyThrows(Exception.class)
	@PostMapping("/getZipFile")
	public void getZipFile(@RequestBody List<String> fileGuids, HttpServletResponse response) {
		attachHandlerService.getZipFile(fileGuids, response);
	}

	/**
	 * 批量图片打包下载
	 *
	 * @param files
	 * @return void
	 * <AUTHOR>
	 * @date 2022/9/19 15:42
	 */
	@SneakyThrows(Exception.class)
	@PostMapping("/getZipPathFile")
	public R<AllcoreFileVO> getZipPathFile(@RequestBody List<PrefixFile> files) {
		return R.data(attachHandlerService.getZipPathFile(files));
	}


}






