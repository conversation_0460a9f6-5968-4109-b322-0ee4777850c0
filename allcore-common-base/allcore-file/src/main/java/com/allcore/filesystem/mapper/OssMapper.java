package com.allcore.filesystem.mapper;

import com.allcore.core.mp.mapper.AllcoreMapper;
import com.allcore.filesystem.entity.Oss;
import com.allcore.filesystem.vo.OssVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface OssMapper extends AllcoreMapper<Oss> {

    /**
     * 自定义分页
     *
     * @param page
     * @param oss
     * @return
     */
    List<OssVO> selectOssPage(IPage page, OssVO oss);

}
