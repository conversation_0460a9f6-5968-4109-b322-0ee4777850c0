package com.allcore.test;


import com.allcore.develop.constant.DevelopConstant;
import com.allcore.develop.support.AllcoreCodeGenerator;

/**
 * 代码生成器
 *
 * <AUTHOR>
 */
public class CodeGenerator {

	/**
	 * 代码生成的模块名
	 */
	public static String CODE_NAME = "资源管理";
	/**
	 * 代码所在服务名
	 */
	public static String SERVICE_NAME = "allcore-develop";
	/**
	 * 代码生成的包名
	 */
	public static String PACKAGE_NAME = "com.allcore.develop";
	/**
	 * 前端代码生成风格
	 */
	public static String CODE_STYLE = DevelopConstant.SABER_NAME;
	/**
	 * 前端代码生成地址
	 */
	public static String PACKAGE_WEB_DIR = "/Users/<USER>/Workspaces/product/Saber";
	/**
	 * 需要去掉的表前缀
	 */
	public static String[] TABLE_PREFIX = {"sys_"};
	/**
	 * 需要生成的表名(两者只能取其一)
	 */
	public static String[] INCLUDE_TABLES = {"sys_datasource"};
	/**
	 * 需要排除的表名(两者只能取其一)
	 */
	public static String[] EXCLUDE_TABLES = {};
	/**
	 * 是否包含基础业务字段
	 */
	public static Boolean HAS_SUPER_ENTITY = Boolean.TRUE;
	/**
	 * 是否包含远程调用
	 */
	private static Boolean HAS_FEIGN = Boolean.TRUE;
	/**
	 * 基础业务字段
	 */
	public static String[] SUPER_ENTITY_COLUMNS = {"id", "create_time", "create_user", "create_dept", "update_time", "update_user", "status", "is_deleted"};


	/**
	 * RUN THIS
	 */
	public static void main(String[] args) {
		AllcoreCodeGenerator generator = new AllcoreCodeGenerator();
		generator.setCodeName(CODE_NAME);
		generator.setServiceName(SERVICE_NAME);
		generator.setCodeStyle(CODE_STYLE);
		generator.setPackageName(PACKAGE_NAME);
		generator.setPackageWebDir(PACKAGE_WEB_DIR);
		generator.setTablePrefix(TABLE_PREFIX);
		generator.setIncludeTables(INCLUDE_TABLES);
		generator.setExcludeTables(EXCLUDE_TABLES);
		generator.setHasSuperEntity(HAS_SUPER_ENTITY);
		generator.setHasFeign(HAS_FEIGN);
		generator.setSuperEntityColumns(SUPER_ENTITY_COLUMNS);
		generator.run();
	}

}
