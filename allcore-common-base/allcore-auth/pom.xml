<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>allcore-common-base</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>

    <artifactId>allcore-auth</artifactId>
    <name>${project.artifactId}</name>
    <version>${allcore.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <!--Allcore-->
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-social</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-system-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <!--freemarker-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-api-crypto</artifactId>
        </dependency>
        <!-- 链路追踪、服务监控 -->
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-trace</artifactId>
        </dependency>
        <!-- 解决Java11无法运行的问题 -->
        <!--<dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>-->
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-auth-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm7JdbcDriver18</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <username>${docker.username}</username>
                    <password>${docker.password}</password>
                    <repository>${docker.registry.url}/${docker.namespace}/${project.artifactId}</repository>
                    <tag>${docker.version}</tag>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
            <!--功能强大的查插件 推荐使用-->
            <!--打包推送命令 mvn clean package docker:push-->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.42.0</version>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <dockerHost>${docker.host}</dockerHost>
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${docker.username}</username>
                        <password>${docker.password}</password>
                    </authConfig>
                    <!-- harbor镜像仓库地址-->
                    <pushRegistry>http://${docker.registry.url}</pushRegistry>
                    <images>
                        <image>
                            <!--推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${docker.registry.url}/${docker.namespace}/${project.artifactId}:${docker.version}</name>
                            <!--定义镜像构建行为-->

                            <build>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>docker-exec</id>
                        <!-- 绑定mvn install阶段，当执行mvn install时 就会执行docker build 和docker push-->
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>