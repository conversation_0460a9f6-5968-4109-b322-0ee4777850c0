package com.allcore.auth.service.impl;

import com.allcore.auth.config.ThirdPartyProperties;
import com.allcore.auth.constant.AuthConstant;
import com.allcore.auth.service.IAccessService;
import com.allcore.auth.vo.ThirdPartyResponse;
import com.allcore.auth.vo.UserInfo;
import com.allcore.auth.vo.UserListData;
import com.allcore.auth.vo.UserValidateRequest;
import com.allcore.common.constant.LoginManageConstant;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.DigestUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.system.cache.ParamCache;
import com.allcore.user.entity.User;
import com.allcore.user.enums.UserEnum;
import com.allcore.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: bl
 * @description: 权限对接服务实现类
 * @author: fanxiang
 * @create: 2025-06-14 14:21
 **/

@Service
@Slf4j
@AllArgsConstructor
public class AccessServiceImpl implements IAccessService {

    private final ThirdPartyProperties thirdPartyProperties;

    @Resource
    private  IUserClient userClient;;

    @Override
    public R<String> getPublicKey() {
        try {
            log.info("开始调用第三方接口获取公钥，URL: {}", thirdPartyProperties.getPublicKeyUrl());

            // 创建专用的RestTemplate，避免负载均衡器拦截
            RestTemplate directRestTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            // 调用第三方接口
            ResponseEntity<ThirdPartyResponse<String>> response = directRestTemplate.exchange(
                    thirdPartyProperties.getPublicKeyUrl(),
                    HttpMethod.GET,
                    requestEntity,
                    new ParameterizedTypeReference<ThirdPartyResponse<String>>() {}
            );

            ThirdPartyResponse<String> responseBody = response.getBody();

            if (responseBody != null && Boolean.TRUE.equals(responseBody.getSuccessful())
                    && responseBody.getStatusCode() == 200) {
                log.info("成功获取第三方公钥");
                return R.data(responseBody.getResultData(), "获取公钥成功");
            } else {
                log.error("第三方接口返回失败，响应: {}", responseBody);
                return R.fail("获取公钥失败：" + (responseBody != null ? responseBody.getResultHint() : "未知错误"));
            }

        } catch (Exception e) {
            log.error("调用第三方接口获取公钥异常", e);
            return R.fail("获取公钥异常：" + e.getMessage());
        }
    }

    @Override
    public boolean validateNrUser(String username, String password) {
        try {
            log.info("开始验证南瑞用户身份，用户名: {}", username);

            // 创建专用的RestTemplate，避免负载均衡器拦截
            RestTemplate directRestTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求体
            UserValidateRequest request = new UserValidateRequest(username, password);
            HttpEntity<UserValidateRequest> requestEntity = new HttpEntity<>(request, headers);

            // 调用第三方验证接口
            ResponseEntity<ThirdPartyResponse<Object>> response = directRestTemplate.exchange(
                    thirdPartyProperties.getLoginUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<ThirdPartyResponse<Object>>() {}
            );

            ThirdPartyResponse<Object> responseBody = response.getBody();

            // 身份验证接口只需要判断statuscode和successful字段
            if (responseBody != null && Boolean.TRUE.equals(responseBody.getSuccessful())
                    && responseBody.getStatusCode() == 200) {
                log.info("南瑞用户身份验证成功，用户名: {}", username);
                return true;
            } else {
                log.warn("南瑞用户身份验证失败，用户名: {}, 状态码: {}, 成功标识: {}, 提示: {}",
                        username,
                        responseBody != null ? responseBody.getStatusCode() : "null",
                        responseBody != null ? responseBody.getSuccessful() : "null",
                        responseBody != null ? responseBody.getResultHint() : "null");
                return false;
            }
        } catch (Exception e) {
            log.error("验证南瑞用户身份异常，用户名: {}", username, e);
            return false;
        }
    }

    @Override
    public R<Integer> checkUserAccount(String username,String tenantId) {
        R<User> userR = userClient.userByAccount(tenantId, username);
        if(!userR.isSuccess()|| Func.isEmpty(userR.getData())){
            return R.fail("用户不存在");
        }
        User user = userR.getData();
        if(UserEnum.NR.getCategory()==user.getUserType()){
            return R.data(user.getUserType(),"该用户是南瑞用户，请使用公钥加密!");
        }
        return R.data(user.getUserType(),"该用户不是南瑞用户，请使用md5加密!");
    }

    @Override
    public boolean validateLoginByToken(String tokenName, String token) {
        try {
            log.info("开始调用南瑞接口验证登录，URL: {}", thirdPartyProperties.getTokenUrl());

            // 创建专用的RestTemplate，避免负载均衡器拦截
            RestTemplate directRestTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set(tokenName,token);

            // 调用第三方接口
            ResponseEntity<ThirdPartyResponse<Object>> response = directRestTemplate.exchange(
                    thirdPartyProperties.getTokenUrl(),
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    new ParameterizedTypeReference<ThirdPartyResponse<Object>>() {}
            );

            ThirdPartyResponse<Object> responseBody=response.getBody();
            if(responseBody!=null && Boolean.TRUE.equals(responseBody.getSuccessful())
            && responseBody.getStatusCode()==200){
                log.info("token验证成功");
                return true;
            }else{
                log.warn("token验证失败");
                return false;
            }

    }catch (Exception e){
            log.error("调用南瑞接口验证登录异常", e);
            return false;
        }
    }

    @Override
    public R getNrUsers() {

        try{

            List<UserInfo> nrUsers = getAllNrUsers();
            if(Func.isEmpty(nrUsers)){
                log.info("没有获取到南瑞用户数据");
                return R.data(nrUsers);
            }
            //获取本平台中的用户列表
            List<String>localAccounts=getLocalUserAccounts();

            //遍历南瑞用户列表，如果本地系统没有该用户，则新增用户
            List<User>userList=new ArrayList<>();
            if (nrUsers != null) {
                nrUsers.forEach(userInfo->{
                    if(!localAccounts.contains(userInfo.getLoginName())){
                        User nrUser=convertToUser(userInfo);
                        userList.add(nrUser);
                    }
                });
            }
            if(Func.isEmpty(userList)){
                log.info("本地系统中已存在所有南瑞用户，无需新增");
                return R.data(userList);
            }
            log.info("本地系统中不存在{}个南瑞用户，开始新增......",userList.size());

            // 将新增的南瑞用户数据入库
            userList.forEach(user->{
                R<Boolean> saveUser = userClient.saveUserInfo(user);
                if(!saveUser.isSuccess()){
                    log.error("新增南瑞用户失败，用户信息: {}",user);
                    throw new ServiceException("新增南瑞用户失败");
                }
                // 保存成功后，根据账号查询用户获取id
                R<User> userR = userClient.userByAccount(user.getTenantId(), user.getAccount());
                if(!userR.isSuccess()||Func.isEmpty(userR.getData())){
                    log.error("查询新增的南瑞用户失败，账号: {}",user.getAccount());
                    throw new ServiceException("查询新增南瑞用户失败");
                }
                User savedUser=userR.getData();

                R<Boolean> saveUserDept = userClient.updateUserDept(savedUser.getId(), savedUser.getDeptId());
                if(!saveUserDept.isSuccess()){
                    log.error("新增南瑞用户部门失败，用户信息: {}",user);
                    throw new ServiceException("新增南瑞用户部门失败");
                }
                log.info("新增南瑞用户成功，用户信息: {}",user);

            });
        }catch (Exception e){
            log.info("新增用户数据发生异常！");
        }

        return R.success("同步南瑞用户数据成功");
    }

    private User convertToUser(UserInfo userInfo) {

        User user=new User();

        user.setId(userInfo.getId());
        user.setAccount(userInfo.getLoginName());
        user.setPassword(DigestUtil.encrypt(AuthConstant.DEFAULT_PASSWORD));
        user.setName(userInfo.getRealName());
        user.setRealName(userInfo.getRealName());
        user.setTenantId(AuthConstant.TENANT_ID);
        user.setUserType(UserEnum.NR.getCategory());
        user.setFromType("1");
        user.setIsDeleted(0);
        user.setStatus(1);
        user.setAccountStatus("yes");
        user.setRoleId(AuthConstant.ROLE_ID);
        user.setDeptId(AuthConstant.DEPT_ID);
        Date now = new Date();
        user.setAccountStartTime(now);
        user.setAccountEndTime(
                DateUtil.plusDays(
                        now,
                        Long.parseLong(ParamCache.getValue(LoginManageConstant.LONG_ACCOUNT_SLEEP_DAY_KEY))
                )
        );
        user.setPasswordStartTime(now);
        user.setPasswordEndTime(
                DateUtil.plusDays(
                        now,
                        Long.parseLong(ParamCache.getValue(LoginManageConstant.USER_PASSWORD_USE_DAY_KEY))
                )
        );
        return user;
    }

    private List<String> getLocalUserAccounts() {

        List<String> accounts = new ArrayList<>();

        try{
            List<User> users = userClient.selectUserList(1, 0, "yes");
            if(Func.isEmpty(users)){
                log.info("本地没有用户数据");
                return accounts;
            }
            for (User user : users) {
                if(Func.isNotEmpty(user.getAccount())){
                    accounts.add(user.getAccount());
                }
            }
            log.info("获取到本地共有{}个用户",accounts.size());
            return accounts;
        }catch (Exception e){
            log.error("获取本地用户账号异常",e);
            throw new ServiceException("获取本地用户账号异常");
        }
    }

    private List<UserInfo> getAllNrUsers() {

        try{

            log.info("开始调用南瑞接口获取用户数据，url:{}",thirdPartyProperties.getUserListUrl());

            // 创建专用的RestTemplate，避免负载均衡器拦截
            RestTemplate directRestTemplate = new RestTemplate();

            //设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            //调用南瑞接口
            ResponseEntity<ThirdPartyResponse<UserListData>> response = directRestTemplate.exchange(
                    thirdPartyProperties.getUserListUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<ThirdPartyResponse<UserListData>>() {}
            );
            //处理响应数据
            ThirdPartyResponse<UserListData> responseData = response.getBody();


            if(Func.notNull(responseData) && Boolean.TRUE.equals(responseData.getSuccessful())
                    && responseData.getStatusCode() == 200){
                log.info("成功获取到南瑞用户数据，用户数量: {}",
                        responseData.getResultData() != null ? responseData.getResultData().getTotal() : 0);
                return responseData.getResultData().getList();
            }else{
                log.error("调用南瑞接口获取用户数据失败，响应: {}",responseData);
                return null;
            }

        }catch (Exception e){
            log.error("调用第三方接口获取用户列表异常", e);
            throw new ServiceException("调用第三方接口获取用户列表异常");
        }

    }

}
