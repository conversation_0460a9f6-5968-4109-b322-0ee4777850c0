package com.allcore.auth.endpoint;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
//import com.allcore.auth.feign.AuthClient;
import com.allcore.core.jwt.JwtUtil;
import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.support.Kv;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;

/**
 * 测试authfeign
 * <AUTHOR>
 * @date 2022/09/20 09:04
 **/
@NonDS
@RestController
@AllArgsConstructor
@Slf4j
public class FeignTest {

//	private AuthClient authClient;
//
//	/**
//	 * （feign 调用实例）
//	 * <AUTHOR>
//	 * @date 2022/09/19 13:38
//	 * @return void
//	 */
//	@GetMapping("/test/token")
//	public R testToken(){
//		//body
//		MultiValueMap<String,String> body = new LinkedMultiValueMap<>();
//		/*登录账号*/
//		body.add("username","zhaojp5913");
//		/*密码需要MD5加密传输  Jsepc01!  32位 小写  对应 95c75b126b7382bdb0cedeafab114c1a*/
//		body.add("password","95c75b126b7382bdb0cedeafab114c1a");
//		body.add("grant_type","password");
//		body.add("scope","all");
//		/*来源app*/
//		body.add("fromType", JwtUtil.APP);
//		body.add("appCode","5C9A-B3B8-2F82-FB13");
//
//		MultiValueMap<String,String> headers = new LinkedMultiValueMap<>();
//		headers.add("Authorization","Basic c2FiZXI6c2FiZXJfc2VjcmV0");
//		headers.add("Tenant-Id","947313");
//		headers.add("Content-Type","x-www-form-urlencoded");
//		//调用auth服务
//		log.info("[接口名称：调用auth][请求数据body：{}]]",body);
//		Object token = authClient.postAccessToken(body,headers);
//		LinkedHashMap<String,Object> map = (LinkedHashMap<String, Object>) token;
//		return R.data(map);
//
//	}
//
//	@GetMapping("/test/logOut")
//	public Kv testLogOut(@RequestParam("token") String token){
//
//		MultiValueMap<String,String> headers = new LinkedMultiValueMap<>();
//		headers.add("Allcore-Auth","bearer "+token);
//		Kv rst = authClient.logout(headers);
//		return rst;
//	}
}
