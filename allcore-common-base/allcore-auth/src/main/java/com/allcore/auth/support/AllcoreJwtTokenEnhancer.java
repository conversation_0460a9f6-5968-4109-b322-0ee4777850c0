package com.allcore.auth.support;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.allcore.auth.service.AllcoreUserDetails;
import com.allcore.auth.utils.TokenUtil;
import com.allcore.core.jwt.JwtUtil;
import com.allcore.core.jwt.props.JwtProperties;
import com.allcore.core.tool.utils.Func;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import java.util.HashMap;
import java.util.Map;

/**
 * jwt返回参数增强
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
public class AllcoreJwtTokenEnhancer implements TokenEnhancer {

	private final JwtAccessTokenConverter jwtAccessTokenConverter;
	private final JwtProperties jwtProperties;

	@Override
	public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
		AllcoreUserDetails principal = (AllcoreUserDetails) authentication.getUserAuthentication().getPrincipal();

		//token参数增强
		Map<String, Object> info = new HashMap<>(19);
		info.put(TokenUtil.CLIENT_ID, TokenUtil.getClientIdFromHeader());
		info.put(TokenUtil.USER_ID, Func.toStr(principal.getUserId()));
		info.put(TokenUtil.DEPT_ID, Func.toStr(principal.getDeptId()));
		info.put(TokenUtil.DEPT_CODE, Func.toStr(principal.getDeptCode()));
		info.put(TokenUtil.DEPT_CATEGORY, Func.toStr(principal.getDeptCategory()));
		info.put(TokenUtil.APP_CODE, Func.toStr(principal.getAppCode()));
		info.put(TokenUtil.MAJOR, Func.toStr(principal.getMajor()));
		info.put(TokenUtil.REF_TYPE, Func.toStr(principal.getRefType()));
		info.put(TokenUtil.ACCOUNT_TYPE, Func.toStr(principal.getAccountType()));
		info.put(TokenUtil.IS_FLYER, Func.toStr(principal.getIsFlyer()));
		info.put(TokenUtil.CREATE_TIME, Func.toStr(principal.getCreateTime()));
		info.put(TokenUtil.POST_ID, Func.toStr(principal.getPostId()));
		info.put(TokenUtil.ROLE_ID, Func.toStr(principal.getRoleId()));
		info.put(TokenUtil.ROLE_HOME_CONFIG, Func.toStr(principal.getRoleHomeConfig()));
		info.put(TokenUtil.TENANT_ID, principal.getTenantId());
		info.put(TokenUtil.OAUTH_ID, principal.getOauthId());
		info.put(TokenUtil.ACCOUNT, principal.getAccount());
		info.put(TokenUtil.USER_NAME, principal.getUsername());
		info.put(TokenUtil.NICK_NAME, principal.getName());
		info.put(TokenUtil.REAL_NAME, principal.getRealName());
		info.put(TokenUtil.ROLE_NAME, principal.getRoleName());
		info.put(TokenUtil.ROLE_NAME_REAL, principal.getRoleNameReal());
		info.put(TokenUtil.FROM_TYPE, principal.getFromType());
		info.put(TokenUtil.AVATAR, principal.getAvatar());
		info.put(TokenUtil.DETAIL, principal.getDetail());
		info.put(TokenUtil.LICENSE, TokenUtil.LICENSE_NAME);
		info.put(TokenUtil.LONGITUDE, principal.getLongitude());
		info.put(TokenUtil.LATITUDE, principal.getLatitude());
		info.put(TokenUtil.ELEVATION, principal.getElevation());
		info.put(TokenUtil.AREA, principal.getArea());
		info.put(TokenUtil.AREA_NAME, principal.getAreaName());
		((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(info);

		//token状态设置
		OAuth2AccessToken oAuth2AccessToken = jwtAccessTokenConverter.enhance(accessToken, authentication);
		String accessTokenValue = oAuth2AccessToken.getValue();
		String tenantId = principal.getTenantId();
		String fromType = principal.getFromType();
		String userId = Func.toStr(principal.getUserId());
		log.info("AllcoreJwtTokenEnhancer===========fromType:{}",fromType);
		JwtUtil.addAccessToken(tenantId, userId,fromType, accessTokenValue, accessToken.getExpiresIn());

		if (jwtProperties.getSingle()) {
			OAuth2RefreshToken oAuth2RefreshToken = oAuth2AccessToken.getRefreshToken();
			String refreshTokenValue = oAuth2RefreshToken.getValue();
			JwtUtil.addRefreshToken(tenantId, userId,fromType, refreshTokenValue, accessToken.getExpiresIn() * 168);
		}

		return accessToken;
	}
}
