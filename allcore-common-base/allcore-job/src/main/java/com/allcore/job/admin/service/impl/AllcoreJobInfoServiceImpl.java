package com.allcore.job.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import com.allcore.core.tool.api.R;
import com.allcore.job.admin.core.cron.CronExpression;
import com.allcore.job.admin.core.route.ExecutorRouteStrategyEnum;
import com.allcore.job.admin.core.scheduler.MisfireStrategyEnum;
import com.allcore.job.admin.core.scheduler.ScheduleTypeEnum;
import com.allcore.job.admin.core.thread.JobScheduleHelper;
import com.allcore.job.admin.mapper.AllcoreJobGroupMapper;
import com.allcore.job.admin.mapper.AllcoreJobInfoMapper;
import com.allcore.job.admin.mapper.AllcoreJobLogMapper;
import com.allcore.job.admin.mapper.AllcoreJobLogReportMapper;
import com.allcore.job.admin.service.AllcoreJobInfoService;
import com.allcore.job.core.entity.AllcoreJobGroup;
import com.allcore.job.core.entity.AllcoreJobInfo;
import com.allcore.job.core.entity.AllcoreJobLogReport;
import com.allcore.job.core.enums.ExecutorBlockStrategyEnum;
import com.allcore.job.core.enums.JobEnum;
import com.allcore.job.core.vo.DashboardVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
* @author: ldh
* @date: 2023/3/16 14:10
* @description:
*/
@Service
public class AllcoreJobInfoServiceImpl extends ServiceImpl<AllcoreJobInfoMapper, AllcoreJobInfo> implements AllcoreJobInfoService {
	private static Logger logger = LoggerFactory.getLogger(AllcoreJobInfoServiceImpl.class);

	@Resource
	private AllcoreJobGroupMapper groupMapper;
	@Resource
	private AllcoreJobInfoMapper jobInfoMapper;
	@Resource
	public AllcoreJobLogMapper jobLogMapper;
	@Resource
	private AllcoreJobLogReportMapper jobLogReportMapper;
	
	@Override
	public R<Page<AllcoreJobInfo>> pageList(int page, int size, Integer jobGroup, Integer triggerStatus, String jobDesc, String executorHandler, String author) {
		Page<AllcoreJobInfo> pageParam = new Page<>(page, size);
		Page<AllcoreJobInfo> pageInfo = new LambdaQueryChainWrapper<>(jobInfoMapper)
				.eq(jobGroup!=null, AllcoreJobInfo::getJobGroup, jobGroup)
				.eq(triggerStatus!=null, AllcoreJobInfo::getTriggerStatus, triggerStatus)
				.like(StringUtils.isNotBlank(jobDesc), AllcoreJobInfo::getJobDesc, jobDesc)
				.like(StringUtils.isNotBlank(author), AllcoreJobInfo::getAuthor, author)
				.page(pageParam);
		pageInfo.getRecords().forEach(e->e.setSchedule(e.getScheduleType()+":"+e.getScheduleConf()));
		return R.data(pageInfo);
	}

	@Override
	public R<String> add(AllcoreJobInfo jobInfo) {
		// valid trigger
		if (JobEnum.CRON.getCode().equals(jobInfo.getScheduleType())) {
			if (jobInfo.getScheduleConf()==null || !CronExpression.isValidExpression(jobInfo.getScheduleConf())) {
				return R.fail( "Cron"+ JobEnum.SYSTEM_UNVALID.getMessage());
			}
		} else if (JobEnum.FIX_RATE.getCode().equals(jobInfo.getScheduleType())) {
			if (jobInfo.getScheduleConf() == null) {
				return R.fail (JobEnum.SCHEDULE_TYPE.getMessage() );
			}
			try {
				int fixSecond = Integer.valueOf(jobInfo.getScheduleConf());
				if (fixSecond < 1) {
					return R.fail(JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage());
				}
			} catch (Exception e) {
				return R.fail(JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage());
			}
		}
		// valid advanced
		if (ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null) == null) {
			return R.fail (JobEnum.EXECUTOR_ROUTE_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		if (MisfireStrategyEnum.match(jobInfo.getMisfireStrategy(), null) == null) {
			return R.fail (JobEnum.MISFIRE_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		if (ExecutorBlockStrategyEnum.match(jobInfo.getExecutorBlockStrategy(), null) == null) {
			return R.fail (JobEnum.EXECUTOR_BLOCK_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		// 》ChildJobId valid
		if (jobInfo.getChildJobid()!=null && jobInfo.getChildJobid().trim().length()>0) {
			String[] childJobIds = jobInfo.getChildJobid().split(",");
			for (String childJobIdItem: childJobIds) {
				if (childJobIdItem!=null && childJobIdItem.trim().length()>0 && isNumeric(childJobIdItem)) {
					AllcoreJobInfo childJobInfo = jobInfoMapper.loadById(Integer.parseInt(childJobIdItem));
					if (childJobInfo==null) {
						return R.fail(MessageFormat.format((JobEnum.JOB_CHILD_JOB_ID.getMessage()+"({0})"+ JobEnum.SYSTEM_NOT_FOUND.getMessage()), childJobIdItem));
					}
				} else {
					return R.fail(MessageFormat.format((JobEnum.JOB_CHILD_JOB_ID.getMessage()+"({0})"+ JobEnum.SYSTEM_UNVALID.getMessage()), childJobIdItem));
				}
			}

			String temp = "";
			for (String item:childJobIds) {
				temp += item + ",";
			}
			temp = temp.substring(0, temp.length()-1);

			jobInfo.setChildJobid(temp);
		}
		jobInfo.setCreateTime(new Date());
		jobInfo.setUpdateTime(new Date());
		jobInfoMapper.insert(jobInfo);
		if (jobInfo.getId() < 1) {
			return R.fail (JobEnum.JOB_ADD.getMessage()+ JobEnum.SYSTEM_FAIL );
		}
		return R.data(String.valueOf(jobInfo.getId()));
	}

	private boolean isNumeric(String str){
		try {
			int result = Integer.valueOf(str);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	@Override
	public R<String> update(AllcoreJobInfo jobInfo) {
		// valid advanced
		if (ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null) == null) {
			return R.fail (JobEnum.EXECUTOR_ROUTE_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage());
		}
		if (MisfireStrategyEnum.match(jobInfo.getMisfireStrategy(), null) == null) {
			return R.fail (JobEnum.MISFIRE_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		if (ExecutorBlockStrategyEnum.match(jobInfo.getExecutorBlockStrategy(), null) == null) {
			return R.fail(JobEnum.EXECUTOR_BLOCK_STRATEGY.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		// 》ChildJobId valid
		if (jobInfo.getChildJobid()!=null && jobInfo.getChildJobid().trim().length()>0) {
			String[] childJobIds = jobInfo.getChildJobid().split(",");
			for (String childJobIdItem: childJobIds) {
				if (childJobIdItem!=null && childJobIdItem.trim().length()>0 && isNumeric(childJobIdItem)) {
					AllcoreJobInfo childJobInfo = jobInfoMapper.loadById(Integer.parseInt(childJobIdItem));
					if (childJobInfo==null) {
						return R.fail(MessageFormat.format((JobEnum.JOB_CHILD_JOB_ID.getMessage()+"({0})"+ JobEnum.SYSTEM_NOT_FOUND.getMessage()), childJobIdItem));
					}
				} else {
					return R.fail(MessageFormat.format((JobEnum.JOB_CHILD_JOB_ID.getMessage()+"({0})"+ JobEnum.SYSTEM_UNVALID.getMessage()), childJobIdItem));
				}
			}

			String temp = "";
			for (String item:childJobIds) {
				temp += item + ",";
			}
			temp = temp.substring(0, temp.length()-1);

			jobInfo.setChildJobid(temp);
		}
		AllcoreJobGroup jobGroup = groupMapper.load(jobInfo.getJobGroup());
		if (jobGroup == null) {
			return R.fail (JobEnum.JOB_GROUP.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}
		AllcoreJobInfo existsJobInfo = jobInfoMapper.loadById(jobInfo.getId());
		if (existsJobInfo == null) {
			return R.fail(JobEnum.JOB_INFO_ID.getMessage()+ JobEnum.SYSTEM_NOT_FOUND.getMessage() );
		}
		// next trigger time (5s后生效，避开预读周期)
		long nextTriggerTime = existsJobInfo.getTriggerNextTime();
		boolean scheduleDataNotChanged = jobInfo.getScheduleType().equals(existsJobInfo.getScheduleType()) && jobInfo.getScheduleConf().equals(existsJobInfo.getScheduleConf());
		if (existsJobInfo.getTriggerStatus() == 1 && !scheduleDataNotChanged) {
			try {
				Date nextValidTime = JobScheduleHelper.generateNextValidTime(jobInfo, new Date(System.currentTimeMillis() + JobScheduleHelper.PRE_READ_MS));
				if (nextValidTime == null) {
					return R.fail (JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage());
				}
				nextTriggerTime = nextValidTime.getTime();
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
				return R.fail (JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage());
			}
		}
		existsJobInfo.setJobGroup(jobInfo.getJobGroup());
		existsJobInfo.setJobDesc(jobInfo.getJobDesc());
		existsJobInfo.setAuthor(jobInfo.getAuthor());
		existsJobInfo.setAlarmEmail(jobInfo.getAlarmEmail());
		existsJobInfo.setScheduleType(jobInfo.getScheduleType());
		existsJobInfo.setScheduleConf(jobInfo.getScheduleConf());
		existsJobInfo.setMisfireStrategy(jobInfo.getMisfireStrategy());
		existsJobInfo.setExecutorRouteStrategy(jobInfo.getExecutorRouteStrategy());
		existsJobInfo.setExecutorHandler(jobInfo.getExecutorHandler());
		existsJobInfo.setExecutorParam(jobInfo.getExecutorParam());
		existsJobInfo.setExecutorBlockStrategy(jobInfo.getExecutorBlockStrategy());
		existsJobInfo.setExecutorTimeout(jobInfo.getExecutorTimeout());
		existsJobInfo.setExecutorFailRetryCount(jobInfo.getExecutorFailRetryCount());
		existsJobInfo.setChildJobid(jobInfo.getChildJobid());
		existsJobInfo.setTriggerNextTime(nextTriggerTime);
		existsJobInfo.setUpdateTime(new Date());
        jobInfoMapper.update(existsJobInfo);
		return R.success("操作成功");
	}

	@Override
	public R<String> remove(int id) {
		AllcoreJobInfo jobInfo = jobInfoMapper.loadById(id);
		if (jobInfo == null) {
			return R.success("操作成功");
		}
		jobInfoMapper.delete(id);
		jobLogMapper.delete(id);
		return R.success("操作成功");
	}

	@Override
	public R<String> start(int id) {
		AllcoreJobInfo jobInfo = jobInfoMapper.loadById(id);

		// valid
		ScheduleTypeEnum scheduleTypeEnum = ScheduleTypeEnum.match(jobInfo.getScheduleType(), ScheduleTypeEnum.NONE);
		if (ScheduleTypeEnum.NONE == scheduleTypeEnum) {
			return R.fail (JobEnum.NONE_START.getMessage());
		}
		// next trigger time (5s后生效，避开预读周期)
		long nextTriggerTime = 0;
		try {
			Date nextValidTime = JobScheduleHelper.generateNextValidTime(jobInfo, new Date(System.currentTimeMillis() + JobScheduleHelper.PRE_READ_MS));
			if (nextValidTime == null) {
				return R.fail (JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
			}
			nextTriggerTime = nextValidTime.getTime();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return R.fail (JobEnum.SCHEDULE_TYPE.getMessage()+ JobEnum.SYSTEM_UNVALID.getMessage() );
		}

		jobInfo.setTriggerStatus(1);
		jobInfo.setTriggerLastTime(0L);
		jobInfo.setTriggerNextTime(nextTriggerTime);
		jobInfo.setUpdateTime(new Date());
		jobInfoMapper.update(jobInfo);
		return R.success("操作成功");
	}

	@Override
	public R<String> stop(int id) {
        AllcoreJobInfo jobInfo = jobInfoMapper.loadById(id);

		jobInfo.setTriggerStatus(0);
		jobInfo.setTriggerLastTime(0L);
		jobInfo.setTriggerNextTime(0L);

		jobInfo.setUpdateTime(new Date());
		jobInfoMapper.update(jobInfo);
		return R.success("操作成功");
	}

	@Override
	public DashboardVo dashboardInfo() {
		int jobInfoCount = jobInfoMapper.findAllCount();
		int jobLogCount = 0;
		int jobLogSuccessCount = 0;
		AllcoreJobLogReport logReport = jobLogReportMapper.queryLogReportTotal();
		if (logReport != null) {
			jobLogCount = logReport.getRunningCount() + logReport.getSucCount() + logReport.getFailCount();
			jobLogSuccessCount = logReport.getSucCount();
		}

		// executor count
		Set<String> executorAddressSet = new HashSet<String>();
		QueryWrapper<AllcoreJobGroup> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().orderByDesc(AllcoreJobGroup::getCreateTime);
		List<AllcoreJobGroup> groupList = groupMapper.selectList(queryWrapper);

		if (groupList!=null && !groupList.isEmpty()) {
			for (AllcoreJobGroup group: groupList) {
				if (group.getRegistryList()!=null && !group.getRegistryList().isEmpty()) {
					executorAddressSet.addAll(group.getRegistryList());
				}
			}
		}

		int executorCount = executorAddressSet.size();
		DashboardVo dashboardVo = new DashboardVo();
		dashboardVo.setExecutorCount(executorCount);
		dashboardVo.setJobLogCount(jobLogCount);
		dashboardVo.setJobInfoCount(jobInfoCount);
		dashboardVo.setJobLogSuccessCount(jobLogSuccessCount);
		return dashboardVo;
	}

	@Override
	public R<Map<String, Object>> chartInfo(String startDate, String endDate) {
		// process
		List<String> triggerDayList = new ArrayList<String>();
		List<Integer> triggerDayCountRunningList = new ArrayList<Integer>();
		List<Integer> triggerDayCountSucList = new ArrayList<Integer>();
		List<Integer> triggerDayCountFailList = new ArrayList<Integer>();
		int triggerCountRunningTotal = 0;
		int triggerCountSucTotal = 0;
		int triggerCountFailTotal = 0;

		List<AllcoreJobLogReport> logReportList = jobLogReportMapper.queryLogReport(startDate, endDate);
		if (logReportList!=null && logReportList.size()>0) {
			for (AllcoreJobLogReport item: logReportList) {
				int triggerDayCountRunning = item.getRunningCount();
				int triggerDayCountSuc = item.getSucCount();
				int triggerDayCountFail = item.getFailCount();
				triggerDayList.add(item.getTriggerDayA());
				triggerDayCountRunningList.add(triggerDayCountRunning);
				triggerDayCountSucList.add(triggerDayCountSuc);
				triggerDayCountFailList.add(triggerDayCountFail);
				triggerCountRunningTotal += triggerDayCountRunning;
				triggerCountSucTotal += triggerDayCountSuc;
				triggerCountFailTotal += triggerDayCountFail;
			}
		} else {
			for (int i = -6; i <= 0; i++) {
				triggerDayList.add(DateUtil.formatDate(DateUtil.offsetDay(new Date(), i)));
				triggerDayCountRunningList.add(0);
				triggerDayCountSucList.add(0);
				triggerDayCountFailList.add(0);
			}
		}

		Map<String, Object> result = new HashMap<String, Object>();
		result.put("triggerDayList", triggerDayList);
		result.put("triggerDayCountRunningList", triggerDayCountRunningList);
		result.put("triggerDayCountSucList", triggerDayCountSucList);
		result.put("triggerDayCountFailList", triggerDayCountFailList);

		result.put("triggerCountRunningTotal", triggerCountRunningTotal);
		result.put("triggerCountSucTotal", triggerCountSucTotal);
		result.put("triggerCountFailTotal", triggerCountFailTotal);

		return R.data(result);
	}

	@Override
	public R<AllcoreJobInfo> detail(int id) {
		QueryWrapper<AllcoreJobInfo> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(AllcoreJobInfo::getId,id);
		AllcoreJobInfo jobInfo = jobInfoMapper.selectOne(queryWrapper);
		return R.data(jobInfo);
	}

}
