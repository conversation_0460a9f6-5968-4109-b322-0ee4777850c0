FROM zxhc.io/library/eclipse-temurin:8-jre

MAINTAINER <EMAIL>

RUN mkdir -p /allcore2.0/platform

WORKDIR /allcore2.0/platform

EXPOSE 18091

ADD ./target/allcore-message.jar ./app.jar

#command模式
#ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]

#CMD ["--spring.profiles.active=test"]

#env模式
ENTRYPOINT java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar app.jar
