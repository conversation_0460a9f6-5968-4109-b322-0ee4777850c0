package com.allcore.service;

import com.allcore.entity.MessageUserSubscription;
import com.allcore.vo.MessageUserSubscriptionVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 消息-用户订阅表 服务类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface IMessageUserSubscriptionService extends IService<MessageUserSubscription> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messageUserSubscription
	 * @return
	 */
	IPage<MessageUserSubscriptionVO> selectMessageUserSubscriptionPage(IPage<MessageUserSubscriptionVO> page, MessageUserSubscriptionVO messageUserSubscription);

}
