package com.allcore.vo;

import com.allcore.entity.MessageUserSubscription;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息-用户订阅表视图实体类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MessageUserSubscriptionVO对象", description = "消息-用户订阅表")
public class MessageUserSubscriptionVO extends MessageUserSubscription {
	private static final long serialVersionUID = 1L;

}
