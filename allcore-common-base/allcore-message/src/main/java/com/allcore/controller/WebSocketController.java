package com.allcore.controller;

import io.swagger.annotations.Api;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.core.tool.utils.WebUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * webSocket 控制器
 *
 * <AUTHOR>
 */
@RestController
@RefreshScope
@RequestMapping("/socket")
@Api(value = "websocket接口", tags = "websocket接口")
public class WebSocketController {

	/**
	 * 默认websocket端口
	 */
	@Value("${message.websocketHostPort:}")
	private String websocketHostPort;

	/**
	 * 临时接口获取websocket 路径ip+port
	 * @return
	 */
	@GetMapping("/websocketHostPort")
	public R getWebsocketHostPort() {
		if(StringUtil.isBlank(websocketHostPort)){
			return R.data("ws://"+WebUtil.getIP()+":18091");
		}
		return R.data(websocketHostPort);
	}

	/**
	 * 以下是无人机点位推送测试
	 */

//	private final WebSocket webSocket;

//	private final MessageServerProperties messageServerProperties;

//	private final String[] point = {"116.597628,39.958646","116.597628,39.958646","116.597628,39.929165","116.597628,39.929165","116.871631,38.385747","116.871631,38.385747","116.871614,38.385844","116.871614,38.385844"};

	//开始/结束推送标志
//	private volatile ConcurrentHashMap<String,Boolean> map = new ConcurrentHashMap<>();

//	/**
//	 * socket开始数据推送模拟
//	 * @param userId
//	 * @return
//	 */
//	@PostMapping("/startUavPointData")
//	public R startUavPointData(String userId) {
//		map.put(userId,false);
//		new Thread(()->{
//			while (!map.get(userId)) {
//				for (int i = 0; i < point.length; i++) {
//					//需要发送的数据
//					Map<String, Object> parmMap = new HashMap<>();
//					//map地图对象
//					Map<String, Object> mapData = new HashMap<>();
//					//无人机对象
//					Map<String, Object> uavData = new HashMap<>();
//					//空域对象
//					Map<String, Object> airspaceData = new HashMap<>();
//					//无人机point对象
//					Map<String, Object> pointData = new HashMap<>();
//					pointData.put("point",point[i]);
//
//					uavData.put("uavcode001",pointData);
//
//					mapData.put("airspace", airspaceData);
//					mapData.put("uav", uavData);
//
//					parmMap.put("messageType", MessageTypeEnum.MAP.getCode());
//					parmMap.put("messageData", mapData);
//					//发送WebSocket消息通知在线用户
//					String param = JsonUtil.toJson(parmMap);
//					webSocket.sendToOne(userId, param);
//					ThreadUtil.sleep(5000);
//				}
//			}
//		}).start();
//		return R.status(true);
//	}
//
//	/**
//	 * 结束推送
//	 * @param userId
//	 * @return
//	 */
//	@PostMapping("/endUavPointData")
//	public R endUavPointData(String userId) {
//		map.put(userId,true);
//		return R.status(true);
//	}

}
