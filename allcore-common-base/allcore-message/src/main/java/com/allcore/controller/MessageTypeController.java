package com.allcore.controller;

import com.allcore.entity.MessageType;
import com.allcore.service.IMessageTypeService;
import com.allcore.vo.MessageTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息类型表 控制器
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/messagetype")
@Api(value = "消息类型表", tags = "消息类型表接口")
public class MessageTypeController extends AllcoreController {

	private final IMessageTypeService messageTypeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入messageType")
	public R<MessageType> detail(MessageType messageType) {
		MessageType detail = messageTypeService.getOne(Condition.getQueryWrapper(messageType));
		return R.data(detail);
	}

	/**
	 * 分页 消息类型表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入messageType")
	public R<IPage<MessageType>> list(MessageType messageType, Query query) {
		IPage<MessageType> pages = messageTypeService.page(Condition.getPage(query), Condition.getQueryWrapper(messageType));
		return R.data(pages);
	}

	/**
	 * 自定义分页 消息类型表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入messageType")
	public R<IPage<MessageTypeVO>> page(MessageTypeVO messageType, Query query) {
		IPage<MessageTypeVO> pages = messageTypeService.selectMessageTypePage(Condition.getPage(query), messageType);
		return R.data(pages);
	}

	/**
	 * 新增 消息类型表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入messageType")
	public R save(@Valid @RequestBody MessageType messageType) {
		return R.status(messageTypeService.save(messageType));
	}

	/**
	 * 修改 消息类型表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入messageType")
	public R update(@Valid @RequestBody MessageType messageType) {
		return R.status(messageTypeService.updateById(messageType));
	}

	/**
	 * 新增或修改 消息类型表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入messageType")
	public R submit(@Valid @RequestBody MessageType messageType) {
		return R.status(messageTypeService.saveOrUpdate(messageType));
	}


	/**
	 * 删除 消息类型表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<MessageType> list = new ArrayList();
		List<String> idsList = Func.toStrList(ids);
		idsList.forEach((id) -> {
			MessageType entity = BeanUtil.newInstance(MessageType.class);
			entity.setUpdateTime(DateUtil.now());
			entity.setId(id);
			list.add(entity);});
		return R.status(messageTypeService.updateBatchById(list) && messageTypeService.removeByIds(idsList));

	}


}
