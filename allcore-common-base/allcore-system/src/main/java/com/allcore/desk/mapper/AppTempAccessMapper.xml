<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.desk.mapper.AppTempAccessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="appTempAccessResultMap" type="com.allcore.desk.entity.AppTempAccess">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
        <result column="no_use_date" jdbcType="TIMESTAMP" property="noUseDate" />
    </resultMap>


    <select id="selectAppTempAccessPage" resultMap="appTempAccessResultMap">
        select id,
               sn,
               type,
               create_date,
               end_date,
               no_use_date from sys_app_temp_access
    </select>

    <select id="selectAccessBySn" resultMap="appTempAccessResultMap" parameterType="java.lang.String">
        select id,
               sn,
               type,
               create_date,
               end_date,
               no_use_date from sys_app_temp_access where sn = #{sn}
    </select>

    <insert id="insertAccess" parameterType="com.allcore.desk.entity.AppTempAccess">
        insert into sys_app_temp_access ( sn, type, create_date, end_date, no_use_date)
        values ( #{sn}, #{type},#{createDate}, #{endDate}, #{noUseDate})
    </insert>
</mapper>
