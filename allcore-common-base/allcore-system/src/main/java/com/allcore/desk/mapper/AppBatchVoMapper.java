package com.allcore.desk.mapper;


import com.allcore.desk.vo.AppBatchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/26 13:39
 * @describe
 */
public interface AppBatchVoMapper {
    /**
     * 查询批次信息 根据父批次集合
     * @param batchNums
     * @return
     */
    List<AppBatchVO> selectByFatherNums(@Param("list") List<String> batchNums);

    /**
     * 批量修改批次信息
     * @param voList
     */
    void updateVoList(@Param("list") List<AppBatchVO> voList);

    /**
     * 查询已绑定的数量 根据批次号
     * @param batchNums
     * @return
     */
    int selectNumByBatchNums(@Param("list") List<String> batchNums);
}
