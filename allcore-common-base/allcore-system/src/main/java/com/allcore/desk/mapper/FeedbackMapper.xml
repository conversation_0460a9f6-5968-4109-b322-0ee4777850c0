<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.desk.mapper.FeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="feedbackResultMap" type="com.allcore.desk.entity.Feedback">
        <result column="id" property="id"/>
        <result column="fb_title" property="fbTitle"/>
        <result column="fb_content" property="fbContent"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_public" property="isPublic"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="feedbackResultMapVO" type="com.allcore.desk.vo.FeedbackVO" extends="feedbackResultMap">
        <collection property="feedbackAnswer" ofType="com.allcore.desk.vo.FeedbackAnswerVO">
            <result column="aid" property="id"/>
            <result column="fb_id" property="fbId"/>
            <result column="fid" property="fid"/>
            <result column="answer_content" property="answerContent"/>
            <result column="answer_user" property="answerUser"/>
            <result column="answer_time" property="answerTime"/>
            <result column="answer_dept" property="answerDept"/>
        </collection>
    </resultMap>

    <sql id="sql_list">
        fb.id,fb_title,fb_content,create_dept,create_user,create_time,update_user,update_time,status,is_public,is_deleted,
            fba.id as aid,fb_id,fid,answer_content,answer_user,answer_time,answer_dept
    </sql>


    <select id="selectFeedbackPage" resultMap="feedbackResultMapVO">
        select <include refid="sql_list" />
            from tb_feedback fb
                left join tb_feedback_answer fba on fb.id=fba.fb_id
                 <where>
                     fb.is_deleted = 0
                     <if test="feedback.queryStr != null">
                     and (fb.fb_title like concat(concat('%', #{feedback.queryStr}), '%') or fb.fb_content like concat(concat('%', #{feedback.queryStr}), '%'))
                     </if>
                     and ((fb.create_user = #{feedback.createUser} and fb.is_public = 1) or fb.is_public = 2)

                 </where>
                 GROUP BY fb.id
    </select>

</mapper>
