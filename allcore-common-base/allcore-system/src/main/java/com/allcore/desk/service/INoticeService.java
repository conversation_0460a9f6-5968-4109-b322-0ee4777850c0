package com.allcore.desk.service;


import com.allcore.core.mp.base.BaseService;
import com.allcore.desk.entity.Notice;
import com.allcore.desk.vo.NoticeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface INoticeService extends BaseService<Notice> {

	/**
	 * 自定义分页
	 * @param page
	 * @param notice
	 * @return
	 */
	IPage<NoticeVO> selectNoticePage(IPage<NoticeVO> page, NoticeVO notice);

}
