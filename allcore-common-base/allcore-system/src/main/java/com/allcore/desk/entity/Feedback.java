package com.allcore.desk.entity;

import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* @author: ldh
* @date: 2023/3/13 10:00
* @description: 意见反馈表实体类
*/
@Data
@TableName("tb_feedback")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Feedback对象", description = "意见反馈表")
public class Feedback extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 标题
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标题")
	private String fbTitle;

	/**
	 * 内容
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "内容")
	private String fbContent;

	/**
	 * 是否公开 1未公开  2公开 默认 1
	 */
	@ApiModelProperty(value = "是否公开 1未公开  2公开 默认 1")
	private Integer isPublic;


}
