package com.allcore.user.wrapper;

import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictCache;
import com.allcore.dict.enums.DictEnum;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Tenant;
import com.allcore.user.entity.User;
import com.allcore.user.vo.UserVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class UserWrapper extends BaseEntityWrapper<User, UserVO> {

	public static UserWrapper build() {
		return new UserWrapper();
	}

	@Override
	public UserVO entityVO(User user) {
		UserVO userVO = Objects.requireNonNull(BeanUtil.copy(user, UserVO.class));
		Tenant tenant = SysCache.getTenantByTenantId(user.getTenantId());
		List<String> roleName = SysCache.getRoleNames(user.getRoleId());
		List<String> deptName = SysCache.getDeptNames(user.getDeptId());
		List<String> postName = SysCache.getPostNames(user.getPostId());
		userVO.setTenantName(tenant.getTenantName());
		userVO.setRoleName(Func.join(roleName));
		userVO.setDeptName(Func.join(deptName));
		userVO.setPostName(Func.join(postName));
		userVO.setSexName(DictCache.getValue(DictEnum.SEX, user.getSex()));
		userVO.setUserTypeName(DictCache.getValue(DictEnum.USER_TYPE, user.getUserType()));
		if(StringUtil.isNotBlank(user.getMajor())){
			List<String> majorList = Func.toStrList(user.getMajor());
			List<String> majorZhList = new ArrayList<>();
			majorList.forEach(e->{
				majorZhList.add(DictCache.getValue(DictEnum.MAJOR,e));
			});
			userVO.setMajorZh(Func.join(majorZhList));
		}
		userVO.setIsFlyerZh(DictCache.getValue(DictEnum.COMMON_YES_NO,user.getIsFlyer()));
		userVO.setAccountStatusZh(DictCache.getValue(DictEnum.ACCOUNT_STATUS,user.getAccountStatus()));
		userVO.setLoginStatusZh(DictCache.getValue(DictEnum.LOGIN_STATUS,user.getLoginStatus()));
		return userVO;
	}

}
