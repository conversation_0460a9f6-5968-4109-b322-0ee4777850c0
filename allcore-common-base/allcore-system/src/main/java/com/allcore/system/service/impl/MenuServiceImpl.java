package com.allcore.system.service.impl;

import com.allcore.common.config.IndexConfig;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.constant.AllcoreConstant;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.support.Kv;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.system.cache.SysCache;
import com.allcore.system.dto.MenuDTO;
import com.allcore.system.entity.*;
import com.allcore.system.mapper.MenuMapper;
import com.allcore.system.service.*;
import com.allcore.system.vo.MenuVO;
import com.allcore.system.wrapper.MenuWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.allcore.common.constant.CommonConstant.API_SCOPE_CATEGORY;
import static com.allcore.common.constant.CommonConstant.DATA_SCOPE_CATEGORY;
import static com.allcore.core.cache.constant.CacheConstant.MENU_CACHE;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {

	private final IRoleMenuService roleMenuService;
	private final IRoleTopMenuService roleTopMenuService;
	private final IRoleScopeService roleScopeService;
	private final ITopMenuSettingService topMenuSettingService;
	private final static String PARENT_ID = "parentId";
	private final static Integer MENU_CATEGORY = 1;

	private IndexConfig indexConfig;

	@Override
	public List<MenuVO> lazyList(String parentId, Map<String, Object> param) {
		if (Func.isEmpty(Func.toStr(param.get(PARENT_ID)))) {
			parentId = null;
		}
		return baseMapper.lazyList(parentId, param);
	}

	@Override
	public List<MenuVO> lazyMenuList(String parentId, Map<String, Object> param) {
		if (Func.isEmpty(Func.toStr(param.get(PARENT_ID)))) {
			parentId = null;
		}
		return baseMapper.lazyMenuList(parentId, param);
	}


	@Override
	public List<MenuVO> routes(String roleId, String topMenuId) {
		if (StringUtil.isBlank(roleId)) {
			return null;
		}
		List<Menu> allMenus = baseMapper.allMenu();
		List<Menu> roleMenus;
		// 超级管理员并且不是顶部菜单请求则返回全部菜单
		if (AuthUtil.isAdministrator() && Func.isEmpty(topMenuId)) {
			roleMenus = allMenus;
		}
		// 非超级管理员并且不是顶部菜单请求则返回对应角色权限菜单
		else if (!AuthUtil.isAdministrator() && Func.isEmpty(topMenuId)) {
			roleMenus = tenantPackageMenu(baseMapper.roleMenuByRoleId(Func.toStrList(roleId),indexConfig.getRefType()));
		}
		// 顶部菜单请求返回对应角色权限菜单
		else {
			// 角色配置对应菜单
			List<Menu> roleIdMenus = baseMapper.roleMenuByRoleId(Func.toStrList(roleId),indexConfig.getRefType());
			// 反向递归角色菜单所有父级
			List<Menu> routes = new LinkedList<>(roleIdMenus);
			roleIdMenus.forEach(roleMenu -> recursion(allMenus, routes, roleMenu));
			// 顶部配置对应菜单
			List<Menu> topIdMenus = baseMapper.roleMenuByTopMenuId(topMenuId);
			// 筛选匹配角色对应的权限菜单
			roleMenus = topIdMenus.stream().filter(x ->
				routes.stream().anyMatch(route -> route.getId().equals(x.getId()))
			).collect(Collectors.toList());
		}
		return buildRoutes(allMenus, roleMenus);
	}

	private List<MenuVO> buildRoutes(List<Menu> allMenus, List<Menu> roleMenus) {
		List<Menu> routes = new LinkedList<>(roleMenus);
		roleMenus.forEach(roleMenu -> recursion(allMenus, routes, roleMenu));
		routes.sort(Comparator.comparing(Menu::getSort));
		MenuWrapper menuWrapper = new MenuWrapper();
		List<Menu> collect = routes.stream().filter(x -> Func.equals(x.getCategory(), 1)).collect(Collectors.toList());
		return menuWrapper.listNodeVO(collect);
	}

	private void recursion(List<Menu> allMenus, List<Menu> routes, Menu roleMenu) {
		Optional<Menu> menu = allMenus.stream().filter(x -> Func.equals(x.getId(), roleMenu.getParentId())).findFirst();
		if (menu.isPresent() && !routes.contains(menu.get())) {
			routes.add(menu.get());
			recursion(allMenus, routes, menu.get());
		}
	}

	@Override
	public List<MenuVO> buttons(String roleId) {
		List<Menu> buttons = (AuthUtil.isAdministrator()) ? baseMapper.allButtons() : baseMapper.buttons(Func.toStrList(roleId),indexConfig.getRefType());
		MenuWrapper menuWrapper = new MenuWrapper();
		return menuWrapper.listNodeVO(buttons);
	}

	@Override
	public List<TreeNode> tree() {
		return ForestNodeMerger.merge(baseMapper.tree());
	}

	@Override
	public List<TreeNode> grantTree(AllcoreUser user) {
		List<TreeNode> menuTree = user.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID)
			? baseMapper.grantTree() : baseMapper.grantTreeByRole(Func.toStrList(user.getRoleId()),indexConfig.getRefType());
		return ForestNodeMerger.merge(tenantPackageTree(menuTree, user.getTenantId()));
	}

	@Override
	public List<TreeNode> grantTopTree(AllcoreUser user) {
		List<TreeNode> menuTree = user.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID)
			? baseMapper.grantTopTree() : baseMapper.grantTopTreeByRole(Func.toStrList(user.getRoleId()),indexConfig.getRefType());
		return ForestNodeMerger.merge(tenantPackageTree(menuTree, user.getTenantId()));
	}

	/**
	 * 租户菜单权限自定义筛选
	 */
	private List<TreeNode> tenantPackageTree(List<TreeNode> menuTree, String tenantId) {
		TenantPackage tenantPackage = SysCache.getTenantPackage(tenantId);
		if (!AuthUtil.isAdministrator() && Func.isNotEmpty(tenantPackage) && !StringPool.ZERO.equals(tenantPackage.getId())) {
			List<String> menuIds = Func.toStrList(tenantPackage.getMenuId());
			// 筛选出两者菜单交集集合
			List<TreeNode> collect = menuTree.stream().filter(x -> menuIds.contains(x.getId())).collect(Collectors.toList());
			// 创建递归基础集合
			List<TreeNode> packageTree = new LinkedList<>(collect);
			// 递归筛选出菜单集合所有父级
			collect.forEach(treeNode -> recursionParent(menuTree, packageTree, treeNode));
			// 递归筛选出菜单集合所有子级
			collect.forEach(treeNode -> recursionChild(menuTree, packageTree, treeNode));
			// 合并在一起返回最终集合
			return packageTree;
		}
		return menuTree;
	}

	/**
	 * 父节点递归
	 */
	public void recursionParent(List<TreeNode> menuTree, List<TreeNode> packageTree, TreeNode treeNode) {
		Optional<TreeNode> node = menuTree.stream().filter(x -> Func.equals(x.getId(), treeNode.getParentId())).findFirst();
		if (node.isPresent() && !packageTree.contains(node.get())) {
			packageTree.add(node.get());
			recursionParent(menuTree, packageTree, node.get());
		}
	}

	/**
	 * 子节点递归
	 */
	public void recursionChild(List<TreeNode> menuTree, List<TreeNode> packageTree, TreeNode treeNode) {
		List<TreeNode> nodes = menuTree.stream().filter(x -> Func.equals(x.getParentId(), treeNode.getId())).collect(Collectors.toList());
		nodes.forEach(node -> {
			if (!packageTree.contains(node)) {
				packageTree.add(node);
				recursionChild(menuTree, packageTree, node);
			}
		});
	}

	/**
	 * 租户菜单权限自定义筛选
	 */
	private List<Menu> tenantPackageMenu(List<Menu> menu) {
		TenantPackage tenantPackage = SysCache.getTenantPackage(AuthUtil.getTenantId());
		if (Func.isNotEmpty(tenantPackage) && !StringPool.ZERO.equals(tenantPackage.getId())) {
			List<String> menuIds = Func.toStrList(tenantPackage.getMenuId());
			menu = menu.stream().filter(x -> menuIds.contains(x.getId())).collect(Collectors.toList());
		}
		return menu;
	}

	@Override
	public List<TreeNode> grantDataScopeTree(AllcoreUser user) {
		return ForestNodeMerger.merge(user.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID) ?
			baseMapper.grantDataScopeTree() : baseMapper.grantDataScopeTreeByRole(Func.toStrList(user.getRoleId()),indexConfig.getRefType()));
	}

	@Override
	public List<TreeNode> grantApiScopeTree(AllcoreUser user) {
		return ForestNodeMerger.merge(user.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID) ?
			baseMapper.grantApiScopeTree() : baseMapper.grantApiScopeTreeByRole(Func.toStrList(user.getRoleId()),indexConfig.getRefType()));
	}

	@Override
	public List<String> roleTreeKeys(String roleIds) {
		List<RoleMenu> roleMenus = roleMenuService.list(Wrappers.<RoleMenu>query().lambda().eq(RoleMenu::getRefType,indexConfig.getRefType()).in(RoleMenu::getRoleId, Func.toStrList(roleIds)));
		return roleMenus.stream().map(roleMenu -> Func.toStr(roleMenu.getMenuId())).collect(Collectors.toList());
	}

	@Override
	public List<String> roleTopMenuKeys(String roleIds) {
		List<RoleTopMenu> roleTopMenus = roleTopMenuService.list(Wrappers.<RoleTopMenu>query().lambda()
				.eq(RoleTopMenu::getRefType,indexConfig.getRefType()).in(RoleTopMenu::getRoleId, Func.toStrList(roleIds)));
		return roleTopMenus.stream().map(roleTopMenu -> Func.toStr(roleTopMenu.getTopMenuId())).collect(Collectors.toList());
	}



	@Override
	public List<String> topTreeKeys(String topMenuIds) {
		List<TopMenuSetting> settings = topMenuSettingService.list(Wrappers.<TopMenuSetting>query().lambda().in(TopMenuSetting::getTopMenuId, Func.toStrList(topMenuIds)));
		return settings.stream().map(setting -> Func.toStr(setting.getMenuId())).collect(Collectors.toList());
	}

	@Override
	public List<String> dataScopeTreeKeys(String roleIds) {
		List<RoleScope> roleScopes = roleScopeService.list(Wrappers.<RoleScope>query().lambda().eq(RoleScope::getScopeCategory, DATA_SCOPE_CATEGORY).in(RoleScope::getRoleId, Func.toStrList(roleIds)));
		return roleScopes.stream().map(roleScope -> Func.toStr(roleScope.getScopeId())).collect(Collectors.toList());
	}

	@Override
	public List<String> apiScopeTreeKeys(String roleIds) {
		List<RoleScope> roleScopes = roleScopeService.list(Wrappers.<RoleScope>query().lambda().eq(RoleScope::getScopeCategory, API_SCOPE_CATEGORY).in(RoleScope::getRoleId, Func.toStrList(roleIds)));
		return roleScopes.stream().map(roleScope -> Func.toStr(roleScope.getScopeId())).collect(Collectors.toList());
	}

	@Override
	@Cacheable(cacheNames = MENU_CACHE, key = "'auth:routes:' + #user.roleId")
	public List<Kv> authRoutes(AllcoreUser user) {
		List<MenuDTO> routes = baseMapper.authRoutes(Func.toStrList(user.getRoleId()),indexConfig.getRefType());
		List<Kv> list = new ArrayList<>();
		routes.forEach(route -> list.add(Kv.create().set(route.getPath(), Kv.create().set("authority", Func.toStrArray(route.getAlias())))));
		return list;
	}

	@Override
	public boolean removeMenu(String ids) {
		Long cnt = baseMapper.selectCount(Wrappers.<Menu>query().lambda().in(Menu::getParentId, Func.toStrList(ids)));
		if (cnt > 0L) {
			throw new ServiceException("请先删除子节点!");
		}
		return removeByIds(Func.toStrList(ids));
	}

	@Override
	public boolean submit(Menu menu) {
		//此处修改了官方的 菜单名或编号已存在 校验
		LambdaQueryWrapper<Menu> menuQueryWrapper = Wrappers.lambdaQuery();
		if (menu.getId() == null) {
			menuQueryWrapper.eq(Menu::getCode, menu.getCode());
		} else {
			menuQueryWrapper.ne(Menu::getId, menu.getId()).and(
				wrapper -> wrapper.eq(Menu::getCode, menu.getCode())
			);
		}
		Long cnt = baseMapper.selectCount(menuQueryWrapper);
		if (cnt > 0L) {
			throw new ServiceException("菜单编号已存在!");
		}
		//去除 限制修改的时候父级为空
		if (menu.getParentId() == null) {
			menu.setParentId(AllcoreConstant.TOP_PARENT_ID);
		}
		if (menu.getParentId() != null && menu.getId() == null) {
			Menu parentMenu = baseMapper.selectById(menu.getParentId());
			if (parentMenu != null && parentMenu.getCategory() != 1) {
				throw new ServiceException("父节点只可选择菜单类型!");
			}
		}
		menu.setIsDeleted(AllcoreConstant.DB_NOT_DELETED);
		return saveOrUpdate(menu);
	}

	@Override
	public Menu getMenuByOldId(String oldId) {
		return baseMapper.getMenuByOldId(oldId);
	}

	@Override
	public List<Menu> getMenuByCode(String code) {
		List<Menu> rst = baseMapper.selectList(Wrappers.<Menu>query().lambda().eq(Menu::getCode, code));
		return rst;
	}

}
