package com.allcore.business.mapper;

import com.allcore.core.entity.FlowConfig;
import com.allcore.core.vo.FlowConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务流程设置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface FlowConfigMapper extends BaseMapper<FlowConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param flowConfig
	 * @return
	 */
	List<FlowConfigVO> selectFlowConfigPage(IPage page, @Param("dto") FlowConfigVO flowConfig);

}
