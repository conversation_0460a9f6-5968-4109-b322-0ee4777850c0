package com.allcore.common.utils.wpml.actionActuatorFuncParam;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * 动作参数(结束录像)
 *
 * <AUTHOR>
 * @date 2023/3/10 10:05
 * @version 1.0
 */
@Data
public class StopRecord extends WpmlActionActuatorFuncParam{
    /**
     * 拍摄照片存储类型
     * zoom: 存储变焦镜头拍摄照片
     * wide: 存储广角镜头拍摄照片
     * ir: 存储红外镜头拍摄照片
     * narrow_band: 存储窄带镜头拍摄照片
     * 注：存储多个镜头照片，格式如“<wpml:payloadLensIndex>wide,ir,narrow_band</wpml:payloadLensIndex>”表示同时使用广角、红外和窄带镜头
     */
    @XStreamAlias("wpml:payloadLensIndex")
    private String payloadLensIndex;

}
