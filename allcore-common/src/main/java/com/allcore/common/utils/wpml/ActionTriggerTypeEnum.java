package com.allcore.common.utils.wpml;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 动作触发器类型
 * 
 * <AUTHOR>
 * @date 2023/8/17 09:55
 * @version 1.0
 */
@Getter
public enum ActionTriggerTypeEnum {

    REACH_POINT("reachPoint", "到达航点时执行"),

    MULTIPLE_TIMING("multipleTiming", "等时触发"),

    MULTIPLE_DISTANCE("multipleDistance", "等距触发"),

    BETWEEN_ADJACENT_POINTS("betweenAdjacentPoints", "航段触发，均匀转云台");

    private String value;

    private String label;


    ActionTriggerTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static ActionTriggerTypeEnum find(String value) {
        if (StrUtil.isBlank(value)) {
            return REACH_POINT;
        }
        return Arrays.stream(ActionTriggerTypeEnum.values())
                .filter(methodEnum -> methodEnum.getValue().equals(value))
                .findAny()
                .orElse(REACH_POINT);
    }

}
