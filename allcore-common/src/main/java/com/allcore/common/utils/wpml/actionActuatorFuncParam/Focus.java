package com.allcore.common.utils.wpml.actionActuatorFuncParam;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * 动作参数(对焦)
 *
 * <AUTHOR>
 * @date 2023/3/10 10:05
 * @version 1.0
 */
@Data
public class Focus extends WpmlActionActuatorFuncParam{
    /**
     * 是否点对焦
     * 0：区域对焦 1：点对焦
     */
    @XStreamAlias("wpml:isPointFocus")
    private Integer isPointFocus;

    /**
     * 对焦点位置
     * [0, 1]
     * * 注：对焦点或对焦区域左上角在画面的X轴（宽）坐标。0为最左侧，1为最右侧。
     */
    @XStreamAlias("wpml:focusX")
    private Float focusX;

    /**
     * 对焦点位置
     * [0, 1]
     * * 注：对焦点或对焦区域左上角在画面的Y轴（高）坐标。0为最上方，1为最下方。
     */
    @XStreamAlias("wpml:focusY")
    private Float focusY;

    /**
     * 对焦区域宽度比
     * [0, 1]
     * 注：对焦区域大小占画面整体的比例，此为宽度比
     * 必需元素
     * * 注：当且仅当“isPointFocus”为“0”（即区域对焦）时必需。
     */
    @XStreamAlias("wpml:focusRegionWidth")
    private Float focusRegionWidth;

    /**
     * 对焦区域高度比
     * [0, 1]
     * * 注：对焦区域大小占画面整体的比例，此为高度比
     * 必需元素
     * * 注：当且仅当“isPointFocus”为“0”（即区域对焦）时必需。
     */
    @XStreamAlias("wpml:focusRegionHeight")
    private Float focusRegionHeight;


    /**
     * 是否无穷远对焦	布尔型	-	0: 非无穷远对焦 1: 无穷远对焦	必需元素	M3E/M3T/M3M
     */
    @XStreamAlias("wpml:isInfiniteFocus")
    private Integer isInfiniteFocus;

    /**
     * 云台Yaw转动角度
     */
    @XStreamAlias("wpml:gimbalYawRotateAngle")
    private String gimbalYawRotateAngle;

    /**
     * 是否使能云台转动时间
     * 0：不使能
     * 1：使能
     */
    @XStreamAlias("wpml:gimbalRotateTimeEnable")
    private String gimbalRotateTimeEnable;

    /**
     * 云台完成转动用时
     */
    @XStreamAlias("wpml:gimbalRotateTime")
    private String gimbalRotateTime;
}
