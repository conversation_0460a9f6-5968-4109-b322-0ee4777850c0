package com.allcore.common.utils.kml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 负载机型信息
 *
 * <AUTHOR>
 * @date 2023/3/9 17:48
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XStreamAlias("wpml:payloadInfo")
public class PayloadInfo {

    /**
     *
     */
    @XStreamAlias("wpml:payloadEnumValue")
    private String payloadEnumValue;

    /**
     *
     */
    @XStreamAlias("wpml:payloadSubEnumValue")
    private String payloadSubEnumValue;

    /**
     *
     */
    @XStreamAlias("wpml:payloadPositionIndex")
    private String payloadPositionIndex;
}