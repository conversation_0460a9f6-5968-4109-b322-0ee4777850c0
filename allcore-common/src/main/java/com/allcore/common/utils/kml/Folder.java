package com.allcore.common.utils.kml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@Data
@XStreamAlias("Folder")
public class Folder {

    // 模板共用元素
    /**
     * 预定义模板类型
     */
    @XStreamAlias("wpml:templateType")
    private String templateType;


    @XStreamAlias("wpml:useGlobalTransitionalSpeed")
    private String useGlobalTransitionalSpeed;

    /**
     * 模板ID
     */
    @XStreamAlias("wpml:templateId")
    private String templateId;

    /**
     * 全局航线飞行速度
     */
    @XStreamAlias("wpml:autoFlightSpeed")
    private String autoFlightSpeed;

    @XStreamAlias("wpml:transitionalSpeed")
    private String transitionalSpeed;

    /**
     * 坐标系参数
     */
//    @XStreamAlias("wpml:waylineCoordinateSysParam")
//    private WaylineCoordinateSysParam waylineCoordinateSysParam;

    /**
     * 负载设置
     */
//    @XStreamAlias("wpml:payloadParam")
//    private PayloadParam payloadParam;


    // 航点飞行模板元素
    /**
     * 全局航点类型（全局航点转弯模式）
     */
    @XStreamAlias("wpml:globalWaypointTurnMode")
    private String globalWaypointTurnMode;

    /**
     * 全局航段轨迹是否尽量贴合直线
     */
    @XStreamAlias("wpml:globalUseStraightLine")
    private String globalUseStraightLine;

    /**
     * 云台俯仰角控制模式
     */
    @XStreamAlias("wpml:gimbalPitchMode")
    private String gimbalPitchMode;

    /**
     * 全局航线高度（椭球高）
     */
    @XStreamAlias("wpml:ellipsoidHeight")
    private String ellipsoidHeight;

    /**
     * 全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）
     */
    @XStreamAlias("wpml:height")
    private String height;

    /**
     * 全局偏航角模式参数
     */
//    @XStreamAlias("wpml:globalWaypointHeadingParam")
//    private GlobalWaypointHeadingParam globalWaypointHeadingParam;

    /**
     * 航点信息（包括航点经纬度和高度等）
     */
//    @XStreamImplicit(itemFieldName = "Placemark")
//    private List<Placemark> placemarkList;

    // 建图航拍模板元素
    /**
     * 是否开启标定飞行
     * 注：仅适用于M300RTK与L1机型
     */
    @XStreamAlias("wpml:caliFlightEnable")
    private String caliFlightEnable;

    /**
     * 是否开启高程优化
     */
    @XStreamAlias("wpml:elevationOpimizeEnable")
    private String elevationOpimizeEnable;

    /**
     * 是否开启智能摆拍
     * 注：仅适用于M300RTK与P1机型
     */
    @XStreamAlias("wpml:elevationOpimizeEnable")
    private String smartObliqueEnable;

    /**
     * 智能摆拍拍摄俯仰角
     * 注：仅适用于M300RTK与P1机型。P1机型云台建议输入范围[-90, -45]
     */
    @XStreamAlias("wpml:smartObliqueGimbalPitch")
    private String smartObliqueGimbalPitch;

    /**
     * 拍照模式（定时或定距）
     */
    @XStreamAlias("wpml:shootType")
    private String shootType;

    /**
     * 航线方向
     */
    @XStreamAlias("wpml:direction")
    private String direction;

    /**
     * 测区外扩距离
     */
    @XStreamAlias("wpml:margin")
    private String margin;

    /**
     * 重叠率参数
     */
    @XStreamAlias("wpml:overlap")
    private String overlap;

    /**
     * 全局航线高度（椭球高）:ellipsoidHeight
     * 全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）:height
     */

    /**
     * 测区多边形
     */
//    @XStreamAlias("Polygon")
//    private Polygon polygon;

    // 倾斜摄影模板元素
    /**
     * 云台俯仰角度（倾斜）
     */
    @XStreamAlias("wpml:inclinedGimbalPitch")
    private String inclinedGimbalPitch;

    /**
     * 航线飞行速度（倾斜）
     */
    @XStreamAlias("wpml:inclinedFlightSpeed")
    private String inclinedFlightSpeed;

    // 航带飞行模板元素
    /**
     * 是否开启单航线飞行
     */
    @XStreamAlias("wpml:singleLineEnable")
    private String singleLineEnable;

    /**
     * 每个子航带航线长度
     */
    @XStreamAlias("wpml:cuttingDistance")
    private String cuttingDistance;

    /**
     * 是否开启边缘优化
     */
    @XStreamAlias("wpml:boundaryOptimEnable")
    private String boundaryOptimEnable;

    /**
     * 航带左侧外扩距离
     */
    @XStreamAlias("wpml:leftExtend")
    private String leftExtend;

    /**
     * 航带右侧外扩距离
     */
    @XStreamAlias("wpml:rightExtend")
    private String rightExtend;

    /**
     * 是否包含中心线
     */
    @XStreamAlias("wpml:includeCenterEnable")
    private String includeCenterEnable;

    /**
     * 航点信息
     */
//    @XStreamAlias("LineString")
//    private LineString lineString;


    /**
     * 其他，参数无具体说明
     */
    @XStreamAlias("wpml:globalHeight")
    private String globalHeight;

}
