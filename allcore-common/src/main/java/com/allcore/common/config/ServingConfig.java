package com.allcore.common.config;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 供服配置
 * <AUTHOR>
 * @date 2023/3/23 10:21
 */
@Data
@RefreshScope
@Component
public class ServingConfig {


	/**
	 * 供服推送开关
	 * <AUTHOR>
	 * @date 2023/3/23 10:24
	 */
	public static boolean SERVING_SWITCH;

	/**
	 * 供服接口地址
	 * <AUTHOR>
	 * @date 2023/3/23 10:24
	 */
	public static String SERVING_URL;

	/**
	 * 巡视工单完成接口
	 * <AUTHOR>
	 * @date 2023/3/23 10:24
	 */
	public static final String  WORK_ORDER_FINISH_INTERFACE  = "/WMCenter/patrol/workOrderCompleteUAV?professionalKind=03";


	/**
	 * 无人机巡视结果
	 * <AUTHOR>
	 * @date 2023/3/23 10:25
	 */
	public static final String  PLANE_INSPECT_RESULT_INTERFACE  = "/WMCenter/patrol/patrolSignUAV?professionalKind=03";



	/**
	 * 巡视轨迹上传
	 * <AUTHOR>
	 * @date 2023/3/23 10:25
	 */
	public static final String  INSPECT_TRACK_UPLOAD_INTERFACE  = "/WMCenter/patrol/createRouteUAV?professionalKind=03";

	/**
	 * nacos配置的是否开启供服推送
	 * <AUTHOR>
	 * @date 2023/3/23 10:57
	 */
	@Value("${serving.switch:false}")
	public void SERVING_SWITCH_SERVICE(boolean IS_OPEN_SERVING_SWITCH) {
		SERVING_SWITCH = IS_OPEN_SERVING_SWITCH;
	}

	/**
	 * nacos配置的供服URL地址
	 * <AUTHOR>
	 * @date 2023/3/23 10:58
	 */
	@Value("${serving.url:http://25.64.68.150:32100}")
	public void SERVING_SERVICE_URL(String SERVING_SERVICE_URL) {
		SERVING_URL = SERVING_SERVICE_URL;
	}
}
