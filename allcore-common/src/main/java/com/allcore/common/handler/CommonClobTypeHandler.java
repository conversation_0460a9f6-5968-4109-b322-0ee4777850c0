package com.allcore.common.handler;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.FileCopyUtils;

import java.io.*;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * CommonBlobTypeHandler
 *
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.CLOB) // 声明数据库中对应数据类型
@MappedTypes(value = String.class)
public class CommonClobTypeHandler extends BaseTypeHandler<String> {

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType)
		throws SQLException {
		final StringReader stringReader = new StringReader(parameter);
		final int length = parameter.getBytes().length;
		ps.setCharacterStream(i, stringReader, length);
		stringReader.close();
	}

	@Override
	public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
		String str = StringUtils.EMPTY;

		try (final OutputStream outPutStream = new ByteArrayOutputStream();
			 final Writer writer = new OutputStreamWriter(outPutStream);
			 Reader reader = rs.getCharacterStream(columnName)) {
			if (reader != null) {
				FileCopyUtils.copy(reader, writer);
				str = outPutStream.toString();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}

		return str;
	}

	@Override
	public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		String str = StringUtils.EMPTY;
		try (final OutputStream outPutStream = new ByteArrayOutputStream();
			 final Writer writer = new OutputStreamWriter(outPutStream);
			 Reader reader = rs.getCharacterStream(columnIndex);) {
			if (reader != null) {
				FileCopyUtils.copy(reader, writer);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}

		return str;
	}

	@Override
	public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		String str = StringUtils.EMPTY;
		try (final OutputStream outPutStream = new ByteArrayOutputStream();
			 final Writer writer = new OutputStreamWriter(outPutStream);
			 Reader reader = cs.getCharacterStream(columnIndex)) {
			if (reader != null) {
				FileCopyUtils.copy(reader, writer);
				str = outPutStream.toString();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}

		return str;
	}

}
