package com.allcore.common.enums;

import lombok.Getter;

/**
 * 网格化枚举
 *
 * <AUTHOR>
 */
@Getter
public enum GridEnum {

	DEVICE_NATURE("device_nature","设备性质"),
	DEVICE_NATURE_DMS("dms","配电"),
	DEVICE_NATURE_TMS("tms","输电"),

	DEVICE_TYPE("device_type","设备类型"),
	DEVICE_TYPE_LINE("line","线路"),
	DEVICE_TYPE_TOWER("tower","杆塔"),

	GRID_TASK_TYPE("grid_task_type","网格任务类型"),
	GRID_TASK_TYPE_PLAN("plan","计划任务"),
	GRID_TASK_TYPE_NON_PLAN("non_plan","非计划任务"),

	INSPECT_SCHEMA("inspect_schema","巡检模式"),
	INSPECT_SCHEMA_FIXED("fixed","固定起降点"),
	INSPECT_SCHEMA_WITHOUT_FIXED("without_fixed","非固定起降点"),

	BEST_TARGET("best_target","巡检最优模式"),
	BEST_TARGET_LEAST_TIME("least_time","最少巡检时长"),
	BEST_TARGET_BIGGEST_INSPECT("biggest_inspect","最大巡检量"),
	BEST_TARGET_LEAST_PERSON("least_person","人员安排最少"),
	BEST_TARGET_LEAST_PLANE("least_plane","无人机数量最少"),

	grid_status("grid_status","网格任务状态"),
	grid_status_no_report("noSubmit","未上报"),
	grid_status_report("submit","已上报 - 废弃"),
	grid_status_waiting_audit("waiting_audit","待审核"),
	grid_status_audit_pass("audit_pass","审核通过"),
	grid_status_audit_reject("audit_reject","审核驳回"),
	grid_status_finish("end","已完成"),

	GRID_TASK_MISSION_TYPE("task_type","任务类型"),
	GRID_TASK_MISSION_TYPE_TASK("task","任务"),
	GRID_TASK_MISSION_TYPE_FLY_TASK("flyTask","飞行任务"),

	WORK_NATURE("work_nature","作业性质"),
	WORK_NATURE_FINE_INSPECT("fine_inspect","精细化巡检"),
	WORK_NATURE_RED_INSPECT("red_inspect","红外巡检"),
	WORK_NATURE_LASER_INSPECT("laser_inspect","激光扫描"),
	WORK_NATURE_WAY_INSPECT("way_inspect","通道巡检"),
	WORK_NATURE_TREE_INSPECT("tree_inspect","树障巡检"),

	GRID_STATUS("grid_status","网格任务状态"),

	INSPECT_MODE("inspect_mode","巡检方式"),
	INSPECT_MODE_AUTOMATIC("automatic","自主"),
	INSPECT_MODE_UNAUTOMATIC("unautomatic","非自主"),

	YES_NO("yes_no","是否"),
	YES_NO_YES("yes","是"),
	YES_NO_NO("no","否"),

	VOLTAGE_LEVEL("010401","电压等级dictKey"),

	/**
	 * 流程引擎key
	 */
	FLOW_KEY("flow_key", "流程引擎key"),
	FLOW_KEY_GRID("grid","网格任务"),

	/**
	 * 流程引擎关联表名
	 */
	FLOW_TABLE("table_name", "流程引擎表名"),
	FLOW_TABLE_GRID("grid_info","网格任务信息表"),

	/**
	 * feign - 获取设备名称的key
	 */
	FEIGN_DEVICE_NAME("deviceName", "设备名称"),

	FEIGN_VOLTAGE_LEVEL_NAME("voltageLevelName", "电压等级"),

	specialty_type("specialty_type","专业类别"),
	specialty_type_pei_dian("pd","配电"),
	specialty_type_shu_dian("sd","输电"),

	;

	/**
	 * 字典编码
	 */
	private final String code;

	/**
	 * 字典名称
	 */
	private final String name;

	/**
	 * 构造方法
	 *
	 * @param code 字典编码
	 * @param name 字典名称
	 */
	GridEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	/**
	 * 根据name获取code
	 * @param name
	 * @return
	 */
	public static String getCodeByName(String name){
		GridEnum[] enums =  GridEnum.values();
		for (GridEnum item : enums) {
			if (item.getName().equals(name)){
				return item.getCode();
			}
		}
		return null;
	}

	/**
	 * 根据code获取name
	 * @param code
	 * @return
	 */
	public static String getNameByCode(String code){
		GridEnum[] enums =  GridEnum.values();
		for (GridEnum item : enums) {
			if (item.getCode().equals(code)){
				return item.getName();
			}
		}
		return null;
	}

}
