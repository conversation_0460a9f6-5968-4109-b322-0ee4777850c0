package com.allcore.external.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * <p>光伏设备曲线</p>
 *
 * @author: sunkun
 * Date: 23 7月 2025
 */
@Data
@Document(collection = "pv_device_curve_data")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PvDeviceCurveData {

    @Id
    private String id;

    /**
     * 逆变器设备id
     */
    private String deviceId;

    /**
     * 逆变器编号
     */
    private String inverterNumber;

    /**
     * 光伏区域id
     */
    private String pvAreaId;

    /**
     * 离散率
     */
    private Float discreteRate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
