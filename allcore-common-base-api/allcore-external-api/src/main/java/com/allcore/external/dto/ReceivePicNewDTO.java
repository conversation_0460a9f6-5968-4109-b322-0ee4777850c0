package com.allcore.external.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 无人机拍摄图片批量上传dto（新）
 * <AUTHOR>
 * @date 2023/10/26 09:49
 **/
@Data
@ApiModel(value = "无人机拍摄图片批量上传", description = "无人机拍摄图片批量上传")
public class ReceivePicNewDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 机巢编码
	 */
	@NotNull(groups = AddGroup.class, message = "机巢编码 不能为空")
	private String machineNestCode;

	/**
	 * 任务ID
	 */
	@NotNull(groups = AddGroup.class, message = "任务ID 不能为空")
	private String taskId;

	/**
	 * 任务类型
	 */
	@NotNull(groups = AddGroup.class, message = "任务类型 不能为空")
	private String taskType;

	/**
	 * 航迹文件类型
	 */
	@NotNull(groups = AddGroup.class, message = "航迹文件类型 不能为空")
	private String fileType;

	/**
	 * 图片总数量
	 */
	@NotNull(groups = AddGroup.class, message = "图片总数量 不能为空")
	private Integer sumPic;

	/**
	 * 无人机拍摄图片详情集合
	 */
	@NotNull(groups = AddGroup.class, message = "无人机拍摄图片详情集合 不能为空")
	private List<UavPicDetailDTO> picDetails;

	@ApiModelProperty("创建人")
	private String createUser;

	@ApiModelProperty("创建部门")
	private String createDept;

	@ApiModelProperty("部门编码")
	private String deptCode;

}
