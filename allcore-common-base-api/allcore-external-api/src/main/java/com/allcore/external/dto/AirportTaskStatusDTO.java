package com.allcore.external.dto;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * <p>机库实时回传的任务状态</p>
 *
 * @author: sunkun
 * Date: 09 6月 2025
 */
@Data
@Document(collection = "airport_task_status")
public class AirportTaskStatusDTO implements Serializable {

    private static final long serialVersionUID = 2581405644812195276L;

    private String historyId;

    private MissionInfoDTO missionInfo;

    private String missionTypeName;

    /**
     * 任务开始时间
     */
    private String startTime;

    /**
     * 飞行记录状态（1.起飞准备 2.飞行中 6. 任务完成 99.任务失败）
     */
    private String flightState;

    /**
     * 飞行记录状态名称
     */
    private String flightStateName;

    /**
     * 失败原因
     */
    private String failureMsgInfo;
}
