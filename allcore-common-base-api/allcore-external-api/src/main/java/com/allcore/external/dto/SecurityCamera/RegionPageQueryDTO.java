package com.allcore.external.dto.SecurityCamera;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/7 13:48
 **/
@NoArgsConstructor
@Data
public class RegionPageQueryDTO {


    @ApiModelProperty(value = "资源类型，region：区域，camera：监控点",required = true)
    private String resourceType;

    @ApiModelProperty(value = "父级区域唯一编码集合")
    private List<String> parentIndexCodes;

    @ApiModelProperty(value = "是否包含下级区域，true时，搜索parentIndexCodes的所有子、孙区域；\n" +
            "false时，只搜索parentIndexCodes的直接子区域；")
    private Boolean isSubRegion;

    @ApiModelProperty(value = "当前页码；pageNo≥1",required = true)
    private Integer pageNo;

    @ApiModelProperty(value = "分页大小；0<pageSize≤1000",required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "权限码集合，view:查看")
    private List<String> authCodes;

    @ApiModelProperty(value = "区域类型，10-普通区域，11-级联区域，12-楼栋单元")
    private Integer regionType;

    @ApiModelProperty(value = "区域名称")
    private String regionName;
    @ApiModelProperty(value = "本级区域向上查询")
    private List<String> sonOrgIndexCodes;

    @ApiModelProperty(value = "级联标识，\n" +
            "0-全部，\n" +
            "1-本级，\n" +
            "2-级联，\n" +
            "默认0")
    private Integer cascadeFlag;

    @ApiModelProperty(value = "排序字段,注意：排序字段必须是查询条件，否则返回参数错误")
    private String orderBy;

    @ApiModelProperty(value = "降序升序,降序：desc，\n" +
            "升序：asc")
    private String orderType;

    @ApiModelProperty(value = "查询表达式")
    private List<ExpressionsDTO> expressions;

    @NoArgsConstructor
    @Data
    public static class ExpressionsDTO {
        @ApiModelProperty(value = "资源属性名")
        private String key;
        @ApiModelProperty(value = "操作运算符， 0 ：=， 1 ：>=， 2 ：<=， 3 ：in， 4 ：not in， 5 ：between， 6 ：like， 7 ：pre like， 8 ：suffix like")
        private Integer operator;
        @ApiModelProperty(value = "资源属性值")
        private List<String> values;
    }
}
