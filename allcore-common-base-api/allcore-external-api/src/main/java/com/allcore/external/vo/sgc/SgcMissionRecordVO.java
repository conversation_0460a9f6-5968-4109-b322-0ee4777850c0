package com.allcore.external.vo.sgc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: bl
 * @description: SGC巡检任务信息VO
 * @author: fanxiang
 * @create: 2025-05-07 19:28
 **/

@Data
public class SgcMissionRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("机器人IP")
    private String agvIp;

    @ApiModelProperty("机器人名称")
    private String agvName;

    @ApiModelProperty("告警数量")
    private Integer alarmCount;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("编辑人")
    private String editBy;

    @ApiModelProperty("编辑时间")
    private String editTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("异常巡检点数")
    private Integer exceptionCount;

    @ApiModelProperty("巡检进度")
    private String finishRate;

    @ApiModelProperty("任务类型(例行巡检:ROUTINE，全面巡检:OVERALL，专项巡检:ITEM，特殊巡检:SPECIAL)")
    private String inspectionType;

    @ApiModelProperty("逻辑删除标识符（0：正常，1：删除）")
    private String isDeleted;

    @ApiModelProperty("巡检点总数")
    private Integer missionCount;

    @ApiModelProperty("巡检时长")
    private String missionDuration;

    @ApiModelProperty("任务ID")
    private String missionId;

    @ApiModelProperty("任务名称")
    private String missionName;

    @ApiModelProperty("巡检计划ID")
    private String missionPlanId;

    @ApiModelProperty("执行方式")
    private String missionType;

    @ApiModelProperty("已经巡检点数量")
    private Integer pointCount;

    @ApiModelProperty("执行状态(1:正常 0:异常)")
    private String resultStatus;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("站点")
    private String stationId;

    @ApiModelProperty("点位名称")
    private String pointName;

    @ApiModelProperty("任务状态 (CREATED: 待执行,RUNNING: 执行中, SUCCESS: 已完成, SHUTDOWN: 停止)")
    private String status;

    @ApiModelProperty("更新时间")
    private String updateTime;
}
