package com.allcore.external.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>告警信息</p>
 *
 * @author: sunkun
 * Date: 08 5月 2025
 */
@Data
@TableName("alarm_info")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlarmInfo implements Serializable {

    private static final long serialVersionUID = -1780124927849781933L;

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @TableId(
            value = "id",
            type = IdType.ASSIGN_UUID
    )
    private String id;

    /**
     * 单位id
     */
    private String deptId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 告警来源 - 字典 -》 alarm_source
     */
    private String alarmSource;

    /**
     * 告警类别  - 字典 -》 alarm_type
     */
    private String alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 告警等级  字典 -> alarm_level
     */
    private String alarmLevel;

    /**
     * 告警时间
     */
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime alarmTime;

    /**
     * 告警状态
     */
    private Integer alarmStatus;

    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime createTime;

    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime updateTime;
    /**
     * 处理意见
     */
    private String opinion;
    /**
     * 最后修改人
     */
    private Long updateUser;
    /**
     * 巡检任务id
     */
    private String inspectionTaskId;

    /**
     * 巡检任务标注图片id
     */
    private String inspectionPictureTaggingId;

    /**
     * 复检次数
     */
    private Integer recheckNum;

    /**
     * mongo原始数据主键id
     */
    private String mongoId;

}
