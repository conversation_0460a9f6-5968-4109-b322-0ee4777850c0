package com.allcore.external.dto.ElecFence;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2025/5/8 14:46
 **/
@NoArgsConstructor
@Data
@Document(collection = "elecFence_alarm_data_report")
public class FenceAlarmDTO {

    @JsonProperty("DeviceId")
    @ApiModelProperty(value = "报警主机的id号")
    private Integer deviceId;
    @JsonProperty("AlarmTime")
    @ApiModelProperty(value = "报警发生时间")
    private String alarmTime;
    @JsonProperty("Channel")
    @ApiModelProperty(value = "报警发生通道")
    private String channel;
    @JsonProperty("SubChannel")
    @ApiModelProperty(value = "报警发生的子通道，如果子通道为0，这个空缺")
    private String subChannel;
    @JsonProperty("CID")
    @ApiModelProperty(value = "发生报警事件的CID代码")
    private String cid;
    @ApiModelProperty(value = "报警类型")
    private String alarmType;
    @ApiModelProperty(value = "报警级别")
    private String alarmLevel;
    @ApiModelProperty(value = "报警内容")
    private String alarmContent;
}
