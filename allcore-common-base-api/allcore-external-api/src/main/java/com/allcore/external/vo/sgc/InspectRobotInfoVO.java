package com.allcore.external.vo.sgc;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: bl
 * @description: 巡检机器人信息VO
 * @author: fanxiang
 * @create: 2025-05-06 09:40
 **/

@Data
@TableName("sgc_info")
public class InspectRobotInfoVO implements Serializable {

    private static  final long serialVersionUID = 1L;

    @TableField(exist = false)
   @ApiModelProperty("compass系统URL")
    private String compassUrl;

   @ApiModelProperty("机器人IP")
    private String agvIp;

   @ApiModelProperty("机器人名称")
    private String agvName;

    @TableField(exist = false)
   @ApiModelProperty("机器人图片")
    private String agvPicUrl;

   @ApiModelProperty("agv状态，在线：0，离线：1")
    private Integer agvStatus;

   @ApiModelProperty("机器人状态中文")
   @TableField(exist = false)
   private String agvStatusZh;

   @ApiModelProperty("机器人类型")
    private String agvType;

    @TableField(exist = false)
   @ApiModelProperty("创建时间")
    private String createTime;

    @TableField(exist = false)
   @ApiModelProperty("描述")
    private String description;

    @TableField(exist = false)
   @ApiModelProperty("是否禁用（启用：0，禁用：1）")
    private String disable;

    @TableField(exist = false)
   @ApiModelProperty("编辑人")
    private String editBy;

    @TableField(exist = false)
   @ApiModelProperty("编辑时间")
    private String editTime;

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_UUID
    )
    private String id;

    @TableField(exist = false)
   @ApiModelProperty("ins系统URL")
    private String insUrl;

    @TableField(exist = false)
   @ApiModelProperty("是否是室外(1:是，0:否)")
    private Integer isOutDoor;

   @ApiModelProperty("识别算法地址")
    private String  recognitionUrl;

   @ApiModelProperty("视频流地址")
    private String rtspSocket;

    @TableField(exist = false)
   @ApiModelProperty("环境传感器数据")
    private String sensorEnv;

    @TableField(exist = false)
   @ApiModelProperty("外接传感器(0:否，1:是")
    private String sensorOuter;

    @TableField(exist = false)
   @ApiModelProperty("更新时间")
    private String updateTime;

   @ApiModelProperty("变电站")
    private String substation;

   @ApiModelProperty("序列号")
    private String  agvCode;

   @ApiModelProperty("单位code")
   private String deptCode;

   @TableField(exist = false)
   @ApiModelProperty("单位名称")
    private String deptName;


   @TableLogic
   @ApiModelProperty("是否删除(0-不删除 1- 删除)")
   private Integer isDeleted;

}
