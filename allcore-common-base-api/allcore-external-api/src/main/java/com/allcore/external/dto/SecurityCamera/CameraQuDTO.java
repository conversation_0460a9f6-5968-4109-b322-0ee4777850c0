package com.allcore.external.dto.SecurityCamera;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15 14:14
 **/
@NoArgsConstructor
@Data
public class CameraQuDTO {

    @ApiModelProperty(value = "当前页码")
    private Integer pageNo;
    @ApiModelProperty(value = "权限码集合")
    private List<String> authCodes;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "资源类型")
    private String resourceType;

    @ApiModelProperty(value = "查询表达式")
    private List<ExperssionDTO> experssions;

    @ApiModelProperty(value = "监控点编号")
    private String indexCode;
    @ApiModelProperty(value = "监控点名称")
    private String name;
    @ApiModelProperty(value = "监控点类型，字典：camera_type，0：枪机，1：半球，2：快球，3：云台枪机")
    private String cameraType;

    @NoArgsConstructor
    @Data
    public static class ExperssionDTO {
        @ApiModelProperty(value = "资源属性名")
        private String key;
        @ApiModelProperty(value = "操作运算符")
        private Integer operator;
        @ApiModelProperty(value = "资源属性值")
        private List<String> values;
    }
}
