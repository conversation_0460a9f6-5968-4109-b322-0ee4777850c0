package com.allcore.external.vo.sgc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: bl
 * @description: 操作点位信息
 * @author: fanxiang
 * @create: 2025-06-05 09:46
 **/

@Data
public class EquipmentPointVO {
    @ApiModelProperty("巡检动作")
    private String actionType;

    @ApiModelProperty("调度任务id")
    private String adsMissionId;

    @ApiModelProperty("调度任务名称")
    private String adsMissionName;


    @ApiModelProperty("机器人ip")
    private String agvIp;

    @ApiModelProperty("测试类名")
    private String className;

    @ApiModelProperty("单位")
    private String company;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("是否禁用(0:启用 ，1:禁用)")
    private String disable;

    @ApiModelProperty("编辑人")
    private String editBy;

    @ApiModelProperty("编辑时间")
    private String editTime;

    @ApiModelProperty("设备id")
    private String equipmentId;

    @ApiModelProperty("设备类型")
    private String equipmentType;

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("地图id")
    private String mapId;

    @ApiModelProperty("地图标记点")
    private String marketCode;

    @ApiModelProperty("点位名称")
    private String pointName;

    @ApiModelProperty("变电站")
    private String substation;

    @ApiModelProperty("设备区域")
    private String areaName;

}
