package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.allcore.common.base.ZxhcEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/23 14:48
 * @describe
 */
@Data
public class ComplexBikeInfoDTO extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = -3472947028096449239L;

	@ApiModelProperty(value = "单兵车Guid")
	private String bikeGuid;

	@ApiModelProperty(value = "车辆名称")
	private String bikeName;

	@ApiModelProperty(value = "车辆SN码")
	private String bikeSnCode;

	@ApiModelProperty(value = "车架号")
	private String VIN;

	@ApiModelProperty(value = "无人机guid")
	private String droneGuid;

	@ApiModelProperty(value = "无人机名称")
	private String droneName;

	@ApiModelProperty(value = "无人机SN码")
	private String droneSnCode;

	@ApiModelProperty(value = "无人机类型")
	private String droneType;

	@ApiModelProperty(value = "经度")
	private Double longitude;

	@ApiModelProperty(value = "纬度")
	private Double latitude;

	@ApiModelProperty(value = "里程")
	private Double mileage;

	@ApiModelProperty(value = "工作状态")
	private Integer workState;

	@ApiModelProperty(value = "异常状态")
	private Integer exceptionState;

	@ApiModelProperty(value = "异常上报时间")
	private String exceptionTime;

	@ApiModelProperty(value = "负责人")
	private String headPerson;
}
