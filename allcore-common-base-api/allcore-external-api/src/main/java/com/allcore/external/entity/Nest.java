/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.external.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机巢表实体类
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@TableName("machine_nest")
@EqualsAndHashCode(callSuper = true)
public class Nest extends ZxhcEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 机巢guid
	 */
	private String machineGuid;
	/**
	 * 机巢名称
	 */
	private String machineName;
	/**
	 * 机巢类型
	 */
	private String machineType;
	/**
	 * 机巢品牌
	 */
	private String machineBrand;
	/**
	 * 舱门开合类
	 */
	private String doorType;
	/**
	 * 机巢型号
	 */
	private String machineModel;
	/**
	 * 机巢SN码
	 */
	private String machineSn;
	private String planeSn;
	/**
	 * 资产属性
	 */
	private String assetAttribute;
	/**
	 * 保管人
	 */
	private String custodian;
	/**
	 * 巡检半径
	 */
	private Double inspectionRadius;
	/**
	 * 最高点高度
	 */
	private Double maxHeight;
	/**
	 * 巡航时间
	 */
	private Double cruiseTime;
	/**
	 * 高度
	 */
	private Double height;
	/**
	 * 经度
	 */
	private Double longitude;
	/**
	 * 纬度
	 */
	private Double latitude;
	/**
	 * 部门code
	 */
	@ApiModelProperty("访问密钥ID")
	private String accessKeyId;

	@ApiModelProperty("访问密钥")
	private String accessKeySecret;

	@ApiModelProperty("机巢服务地址")
	private String serviceAddress;


}
