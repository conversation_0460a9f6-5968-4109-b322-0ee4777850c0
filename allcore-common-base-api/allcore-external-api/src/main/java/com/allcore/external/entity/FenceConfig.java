package com.allcore.external.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/4 11:23
 **/
@Data
@TableName("EXTERNAL_FENCE_CONFIG")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FenceConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @TableId(
            value = "id",
            type = IdType.ASSIGN_UUID
    )
    private String id;
    private String deptCode;
    private String channel;
}
