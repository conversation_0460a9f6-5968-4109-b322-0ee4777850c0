package com.allcore.external.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 15:22
 **/
@NoArgsConstructor
@Data
@Document(collection = "integratedline_alarm_data_report")
public class IntegratedLineAlarmDTO {

    @JsonProperty("CMD_ID")
    @ApiModelProperty(value = "单兵车Guid")
    private String cmdId;
    @JsonProperty("DATA")
    @ApiModelProperty(value = "报警消息体")
    private List<DATADTO> data;
    @JsonProperty("TIME_STAMP")
    @ApiModelProperty(value = "报警时间，格式yyyy/MM/dd hh:mm:ss")
    private String timeStamp;

    @NoArgsConstructor
    @Data
    public static class DATADTO {
        @JsonProperty("SITE")
        @ApiModelProperty(value = "场站")
        private String site;

        @JsonProperty("LINE")
        @ApiModelProperty(value = "线路")
        private String line;

        @JsonProperty("COMPONENT")
        @ApiModelProperty(value = "告警形式")
        private String component;
        @JsonProperty("VALUE")
        @ApiModelProperty(value = "报警具体信息")
        private String value;
    }
}
