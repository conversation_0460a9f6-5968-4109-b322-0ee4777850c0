<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>allcore-common-base-api</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-external-api</artifactId>
    <name>${project.artifactId}</name>
    <version>${allcore.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-secure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-datascope</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-mongo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-common</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
    </dependencies>

</project>
