package com.allcore.job.core.entity;

import com.allcore.job.core.vo.LogListVo;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@TableName("sys_job_log")
@ApiModel(value = "AllcoreJobLog对象", description = "")
public class AllcoreJobLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("执行器主键ID")
    @TableField("job_group")
    private Integer jobGroup;

    @ApiModelProperty("任务，主键ID")
    @TableField("job_id")
    private Integer jobId;

    @ApiModelProperty("执行器地址，本次执行的地址")
    @TableField("executor_address")
    private String executorAddress;

    @ApiModelProperty("执行器任务handler")
    @TableField("executor_handler")
    private String executorHandler;

    @ApiModelProperty("执行器任务参数")
    @TableField("executor_param")
    private String executorParam;

    @ApiModelProperty("执行器任务分片参数，格式如 1/2")
    @TableField("executor_sharding_param")
    private String executorShardingParam;

    @ApiModelProperty("失败重试次数")
    @TableField("executor_fail_retry_count")
    private Integer executorFailRetryCount;

    @ApiModelProperty("调度-时间")
    @TableField("trigger_time")
    private Date triggerTime;

    @ApiModelProperty("调度-结果code")
    @TableField("trigger_code")
    private int triggerCode;

    @ApiModelProperty("调度-结果")
    @TableField(exist = false)
    private String triggerResult;

    @ApiModelProperty("调度-日志")
    @TableField("trigger_msg")
    private String triggerMsg;

    @ApiModelProperty("执行-时间")
    @TableField("handle_time")
    private Date handleTime;

    @ApiModelProperty("执行-状态code")
    @TableField("handle_code")
    private int handleCode;

    @ApiModelProperty("执行-状态")
    @TableField(exist = false)
    private String handleResult;

    @ApiModelProperty("执行-日志")
    @TableField("handle_msg")
    private String handleMsg;

    @ApiModelProperty("告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败")
    @TableField("alarm_status")
    private Integer alarmStatus;

    @ApiModelProperty("触发日志")
    @TableField(exist = false)
    private LogListVo listVo;


}




