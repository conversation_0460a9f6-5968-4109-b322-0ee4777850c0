package com.allcore.job.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@TableName("sys_job_log_report")
@ApiModel(value = "AllcoreJobLogReport对象", description = "")
public class AllcoreJobLogReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("调度-时间")
    @TableField("trigger_day")
    private Date triggerDay;

    @ApiModelProperty("调度-时间(展示)")
    @TableField(exist = false)
    private String triggerDayA;

    @ApiModelProperty("运行中-日志数量")
    @TableField("running_count")
    private Integer runningCount;

    @ApiModelProperty("执行成功-日志数量")
    @TableField("suc_count")
    private Integer sucCount;

    @ApiModelProperty("执行失败-日志数量")
    @TableField("fail_count")
    private Integer failCount;

    @TableField("update_time")
    private LocalDateTime updateTime;


}




