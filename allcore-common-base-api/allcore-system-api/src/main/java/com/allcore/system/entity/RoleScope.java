package com.allcore.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_role_scope")
@ApiModel(value = "RoleScope对象", description = "RoleScope对象")
public class RoleScope implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private String id;

	/**
	 * 权限类型
	 */
	@ApiModelProperty(value = "权限类型")
	private Integer scopeCategory;

	/**
	 * 权限id
	 */
	@ApiModelProperty(value = "权限id")
	private String scopeId;

	/**
	 * 角色id
	 */
	@ApiModelProperty(value = "角色id")
	private String roleId;


}
