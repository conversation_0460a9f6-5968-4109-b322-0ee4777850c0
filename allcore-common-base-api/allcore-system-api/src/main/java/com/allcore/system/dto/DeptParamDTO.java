package com.allcore.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2023/4/13 16:47
 * @description: 单位查询参数
 */
@Data
@ApiModel("单位查询参数")
public class DeptParamDTO {

	@ApiModelProperty("单位名称")
	private String deptName;

	@ApiModelProperty("单位全称")
	private String fullName;

	@ApiModelProperty("单位code左模糊查询")
	private String lDeptCodeLike;

	@ApiModelProperty("单位code右模糊查询")
	private String rDeptCodeLike;

	@ApiModelProperty("单位code模糊查询")
	private String deptCodeLike;

	@ApiModelProperty("租户id")
	private String tenantId;

	@ApiModelProperty("三级单位等级")
	private Integer deptLevel;
}
