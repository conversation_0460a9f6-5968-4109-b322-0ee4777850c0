package com.allcore.system.entity;

import lombok.Data;

/**
 * 登陆管理
 *
 * <AUTHOR>
 * @date 2022/09/02 08:57
 **/
@Data
public class LoginManage {

	/**
	 * 允许用户登录IP
	 */
	private String allowIp;

	/**
	 * 允许用户登陆IP段
	 */
	private String allowIpRange;

	/**
	 * 审计日志存储上限
	 */
	private String auditLogSaveSize;

	/**
	 * 临时账户周期
	 */
	private String tempAccountDay;

	/**
	 * 长期账户休眠时长
	 */
	private String longAccountSleepTime;

	/**
	 * 长期账户休眠周期
	 */
	private String longAccountSleepDay;

	/**
	 * 是否限制IP
	 */
	private String allowFlag;

	/**
	 * 允许同时在线用户数
	 */
	private String allowThreadUserCount;

	/**
	 * 连续登录失败锁定时间
	 */
	private String loginErrorLockTime;

	/**
	 * 休眠到期提醒
	 */
	private String accountSleepWarnDay;

	/**
	 * 允许用户登录结束时间
	 */
	private String allowLoginEndTime;

	/**
	 * 用户会话超时时间
	 */
	private String userSessionTimeout;

	/**
	 * 允许用户登录开始时间
	 */
	private String allowLoginStartTime;

	/**
	 * 用户口令有效日期
	 */
	private String userPasswordUseDay;

	/**
	 * 登陆失败次数
	 */
	private String accountFailCount;
	/**
	 * 备份日志后库里存留数据时间
	 */
	private String bakLogTime;
}
