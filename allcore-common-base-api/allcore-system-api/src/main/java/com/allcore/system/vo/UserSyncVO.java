package com.allcore.system.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "userSyncVO对象", description = "userSyncVO对象")
public class UserSyncVO {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否激活了钉钉：
	 *
	 * */
	private Boolean active;

	private String admin;

	/**
	 * 头像地址。
	 *
	 * */
	private String avatar;

	private String boss;

	/**
	 * 所属部门id列表。
	 *
	 * */
	private List<String> dept_id_list;

	/**
	 * 员工在部门中的排序
	 *
	 * */
	private String dept_order;

	private String email;

	private String exclusive_account;

	private String hide_mobile;

	/**
	 * 入职时间，Unix时间戳，单位毫秒。
	 * 说明 
	 * 第三方企业应用不返回该参数。
	 *
	 * */
	private Long hired_date;

	private String leader;

	/**
	 * 手机号码
	 *
	 * */
	private String mobile;

	private String name;

	private String remark;

	/**
	 *
	 * 国际电话区号。
	 * */
	private String state_code;

	private String telephone;

	private String title;
	/**
	 * 用户在当前开发者企业帐号范围内的唯一标识。
	 *
	 * */
	private String unionid;

	/**
	 *
	 * 用户的userId。
	 * */
	private String userid;

	/**
	 * 办公地点。
	 * 说明
	 *
	 * */
	private String work_place;
}
