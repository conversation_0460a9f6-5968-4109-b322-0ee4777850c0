package com.allcore.desk.entity;

import com.allcore.core.tenant.mp.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_notice")
@EqualsAndHashCode(callSuper = true)
public class Notice extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 标题
	 */
	@ApiModelProperty(value = "标题")
	private String title;

	/**
	 * 通知类型
	 */
	@ApiModelProperty(value = "通知类型")
	private Integer category;

	/**
	 * 发布日期
	 */
	@ApiModelProperty(value = "发布日期")
	private Date releaseTime;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "内容")
	private String remark;

	/**
	 * 图片路径
	 */
	@ApiModelProperty(value = "图片路径")
	private String iconUrl;

	/**
	 * 附件guid
	 */
	@ApiModelProperty(value = "附件guid")
	private String appendixGuid;

	/**
	 * 附件名
	 */
	@ApiModelProperty(value = "附件名")
	private String appendixName;

}
