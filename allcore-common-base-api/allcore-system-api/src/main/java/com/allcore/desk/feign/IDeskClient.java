package com.allcore.desk.feign;


import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.tool.api.AppR;
import com.allcore.desk.dto.HandleSignDataDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME
)
public interface IDeskClient {

	String API_PREFIX = "/client";
	String ANDROID_LOGIN_APP = API_PREFIX + "/android-login-app";
	String HANDLE_SIGN_DATA = API_PREFIX + "/handle-Sign-Data";

	/**
	 * （app登录）
	 * <AUTHOR>
	 * @date 2023/04/07 11:17
	 * @param tenantId
	 * @param userName
	 * @param ticket
	 * @return com.allcore.core.tool.api.R<com.allcore.common.utils.AppR>
	 */
	@GetMapping(ANDROID_LOGIN_APP)
	AppR androidLoginApp(@RequestParam("tenantId") String tenantId, @RequestParam("userName") String userName, @RequestParam("ticket") String ticket);

	@PostMapping(HANDLE_SIGN_DATA)
	AppR handleSignData(@RequestBody HandleSignDataDTO handleSignDataDTO);


}
