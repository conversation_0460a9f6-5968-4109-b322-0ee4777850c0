package com.allcore.dict.feign;

import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.tool.api.R;
import com.allcore.dict.entity.DictBiz;
import com.allcore.dict.vo.DictBizVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallback = IDictBizClientFallback.class
)
public interface IDictBizClient {

	String API_PREFIX = "/client";
	String GET_BY_ID = API_PREFIX + "/dict-biz/get-by-id";
	String GET_KEY = API_PREFIX + "/dict-biz/get-key";
	String GET_VALUE = API_PREFIX + "/dict-biz/get-value";
	String GET_LIST = API_PREFIX + "/dict-biz/get-list";
	String GET_TREE_LIST = API_PREFIX + "/dict-biz/get-tree-list";

	String FIRST_LEVEL_DICTIONARY = API_PREFIX + "/dict-biz/first-level-dictionary";


	/**
	 * （获取第一层字典）
	 * <AUTHOR>
	 * @date 2022/09/30 17:07
	 * @param code
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.entity.DictBiz>>
	 */
	@GetMapping(FIRST_LEVEL_DICTIONARY)
	R<List<DictBiz>> firstLevelDictionary(@RequestParam("code") String code);

	/**
	 * 获取字典实体
	 *
	 * @param id 主键
	 * @return
	 */
	@GetMapping(GET_BY_ID)
	R<DictBiz> getById(@RequestParam("id") String id);

	/**
	 * 获取字典表对应值
	 *
	 * @param code    字典编号
	 * @param dictKey 字典序号
	 * @return
	 */
	@GetMapping(GET_VALUE)
	R<String> getValue(@RequestParam("code") String code, @RequestParam("dictKey") String dictKey);

	/**
	 * 获取字典表
	 *
	 * @param code 字典编号
	 * @return
	 */
	@GetMapping(GET_LIST)
	R<List<DictBiz>> getList(@RequestParam("code") String code);

	/**
	 * 获取字典树
	 *
	 * @param code 字典编号
	 * @return
	 */
	@GetMapping(GET_TREE_LIST)
	R<List<DictBizVO>> getTreeList(@RequestParam("code") String code);

	/**
	 * 获取字典key
	 *
	 * @param code         字典编号
	 * @param dictBizValue 字典value
	 * @return {@link R}<{@link String}>
	 */
	@GetMapping(GET_KEY)
	R<String> getKey(@RequestParam("code")String code,@RequestParam("dictBizValue") String dictBizValue);

}
