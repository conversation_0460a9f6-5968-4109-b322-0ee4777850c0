package com.allcore.system.config;


import lombok.AllArgsConstructor;
import com.allcore.core.datascope.handler.ScopeModelHandler;
import com.allcore.core.secure.config.RegistryConfiguration;
import com.allcore.core.secure.handler.IPermissionHandler;
import com.allcore.system.handler.ApiScopePermissionHandler;
import com.allcore.system.handler.DataScopeModelHandler;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 公共封装包配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AllArgsConstructor
@AutoConfigureBefore(RegistryConfiguration.class)
public class ScopeConfiguration {

	@Bean
	public ScopeModelHandler scopeModelHandler() {
		return new DataScopeModelHandler();
	}

	@Bean
	public IPermissionHandler permissionHandler() {
		return new ApiScopePermissionHandler();
	}

}
